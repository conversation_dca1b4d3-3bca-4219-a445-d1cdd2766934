{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.10", "main": "index.ts", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "~14.0.4", "@kichiyaki/react-native-barcode-generator": "^0.6.7", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@stripe/stripe-react-native": "0.38.6", "@supabase/supabase-js": "^2.48.1", "@types/node": "^22.13.10", "@types/uuid": "^10.0.0", "@ungap/structured-clone": "^1.3.0", "base64-arraybuffer": "^1.0.2", "dotenv": "^16.4.7", "expo": "~52.0.47", "expo-apple-authentication": "~7.1.3", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-notifications": "~0.29.14", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "lottie-react-native": "7.1.0", "nanoid": "^5.1.3", "react": "18.3.1", "react-native": "0.76.9", "react-native-check-version": "^1.4.0", "react-native-device-info": "^14.0.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-viewing": "^0.2.2", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-webview": "^13.15.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/ungap__structured-clone": "^1.2.0", "postcss-value-parser": "^4.2.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "private": true}