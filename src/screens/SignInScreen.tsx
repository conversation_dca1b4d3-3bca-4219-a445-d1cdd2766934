import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import Button from '../components/Button';
import AppleSignInButton from '../components/AppleSignInButton';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, SPACING, RADIUS, FONT_SIZES, GRADIENTS } from '../config/theme';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/navigation';
import { RouteProp } from '@react-navigation/native';

type SignInScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'SignIn'>;
  route: RouteProp<RootStackParamList, 'SignIn'>;
};

export default function SignInScreen({ navigation, route }: SignInScreenProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isOtpMode, setIsOtpMode] = useState(false);
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  
  const handleSendOtp = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email');
      return;
    }
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: { shouldCreateUser: false },
      });
      if (error) throw error;
      setOtpSent(true);
      Alert.alert('Success', 'Verification code has been sent to your email');
    } catch (error: any) {
      Alert.alert('Error sending OTP', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      Alert.alert('Error', 'Please enter the verification code');
      return;
    }
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'email',
      });
      if (error) throw error;
      const redirectScreen = route.params?.redirectScreen;
      const redirectParams = route.params?.redirectParams;
      if (redirectScreen) {
        navigation.replace(redirectScreen, redirectParams);
      } else {
        navigation.replace('Home', { screen: 'HomeTab' });
      }
    } catch (error: any) {
      Alert.alert('Verification Failed', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      const redirectScreen = route.params?.redirectScreen;
      const redirectParams = route.params?.redirectParams;
      if (redirectScreen) {
        navigation.replace(redirectScreen, redirectParams);
      } else {
        navigation.replace('Home', { screen: 'HomeTab' });
      }
    } catch (error: any) {
      Alert.alert('Login Failed', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)']}
      style={styles.gradientContainer}
    >
      <StatusBar barStyle="light-content" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {navigation.canGoBack() && (
             <TouchableOpacity
               style={styles.backButton}
               onPress={() => navigation.goBack()}
             >
               <Ionicons name="chevron-back" size={28} color={COLORS.TEXT_WHITE} />
             </TouchableOpacity>
           )}
           
          <View style={styles.contentContainer}>
             <Ionicons 
               name="log-in-outline" 
               size={80}
               color={COLORS.PRIMARY_LIGHT} 
               style={styles.icon} 
             />
            <Text style={styles.title}>Welcome Back!</Text>
            <Text style={styles.subtitle}>Sign in to continue</Text>

            {/* Apple Sign In Button - Only show on iOS */}
            {Platform.OS === 'ios' && (
              <AppleSignInButton
                onSuccess={() => {
                  const redirectScreen = route.params?.redirectScreen;
                  const redirectParams = route.params?.redirectParams;
                  if (redirectScreen) {
                    navigation.replace(redirectScreen, redirectParams);
                  } else {
                    navigation.replace('Home', { screen: 'HomeTab' });
                  }
                }}
                onError={(error) => {
                  Alert.alert('Sign In Failed', error);
                }}
                style={styles.appleButtonTop}
                loading={loading}
              />
            )}

            {/* Divider */}
            {Platform.OS === 'ios' && (
              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>or</Text>
                <View style={styles.dividerLine} />
              </View>
            )}

            <View style={styles.form}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="Enter your email"
                placeholderTextColor={COLORS.TEXT_GRAY}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete='email'
                textContentType='emailAddress'
              />

              {!isOtpMode ? (
                <>
                  <Text style={styles.label}>Password</Text>
                  <View style={styles.passwordContainer}>
                    <TextInput
                      style={styles.input}
                      value={password}
                      onChangeText={setPassword}
                      placeholder="Enter your password"
                      placeholderTextColor={COLORS.TEXT_GRAY}
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                      autoComplete='password'
                      textContentType='password'
                    />
                    <TouchableOpacity
                      style={styles.eyeButton}
                      onPress={() => setShowPassword(!showPassword)}
                    >
                      <Ionicons
                        name={showPassword ? 'eye-outline' : 'eye-off-outline'}
                        size={24}
                        color={COLORS.TEXT_GRAY}
                      />
                    </TouchableOpacity>
                  </View>
                  
                  <Button
                    title="Sign In"
                    onPress={handleSignIn}
                    loading={loading}
                    style={styles.mainButton}
                  />
                  
                  <TouchableOpacity onPress={() => setIsOtpMode(true)} style={styles.switchModeButton}>
                     <Text style={styles.switchModeText}>Sign In with Verification Code</Text>
                  </TouchableOpacity>


                </>
              ) : (
                <>
                  {!otpSent ? (
                     <>
                       <Text style={styles.otpInfoText}>
                         We will send a verification code to your email address.
                       </Text>
                       <Button
                         title="Send Code"
                         onPress={handleSendOtp}
                         loading={loading}
                         style={styles.mainButton}
                       />
                     </>
                  ) : (
                    <>
                      <Text style={styles.label}>Verification Code</Text>
                      <TextInput
                        style={styles.input}
                        value={otp}
                        onChangeText={setOtp}
                        placeholder="Enter 6-digit code"
                        placeholderTextColor={COLORS.TEXT_GRAY}
                        keyboardType="number-pad"
                        maxLength={6}
                      />
                       <Button
                         title="Verify Code & Sign In"
                         onPress={handleVerifyOtp}
                         loading={loading}
                         style={styles.mainButton}
                       />
                    </>
                  )}
                  <TouchableOpacity onPress={() => setIsOtpMode(false)} style={styles.switchModeButton}>
                     <Text style={styles.switchModeText}>Sign In with Password</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
            
            <View style={styles.footer}>
              <Text style={styles.footerText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignUp')}>
                <Text style={styles.footerLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>


          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
  },
  keyboardAvoidView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: SPACING.LARGE,
    paddingBottom: SPACING.LARGE,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight || SPACING.LARGE : SPACING.XLARGE,
  },
  backButton: {
     position: 'absolute',
     top: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + SPACING.MEDIUM : SPACING.XLARGE + SPACING.SMALL,
     left: SPACING.MEDIUM,
     zIndex: 10,
     padding: SPACING.SMALL,
   },
  contentContainer: {
    alignItems: 'center',
  },
  icon: {
    marginTop: SPACING.LARGE,
    marginBottom: SPACING.MEDIUM,
  },
  title: {
    fontSize: FONT_SIZES.XXXLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
    marginBottom: SPACING.XLARGE,
    textAlign: 'center',
  },
  form: {
    width: '100%',
    marginBottom: SPACING.LARGE,
  },
  label: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_LIGHT,
    marginBottom: SPACING.SMALL,
    marginLeft: SPACING.SMALL,
  },
  input: {
    backgroundColor: COLORS.TRANSPARENT_LIGHT,
    color: COLORS.TEXT_WHITE,
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  passwordContainer: {
    position: 'relative',
    width: '100%',
  },
  eyeButton: {
    position: 'absolute',
    right: SPACING.MEDIUM,
    top: 0,
    bottom: 12,
    justifyContent: 'center',
    paddingHorizontal: SPACING.SMALL,
  },
   mainButton: {
     width: '100%',
     marginTop: SPACING.SMALL,
   },
   switchModeButton: {
     marginTop: SPACING.LARGE,
     padding: SPACING.SMALL,
   },
   switchModeText: {
     color: COLORS.TEXT_LIGHT,
     fontSize: FONT_SIZES.SMALL,
     textAlign: 'center',
   },
   otpInfoText: {
     color: COLORS.TEXT_LIGHT,
     fontSize: FONT_SIZES.SMALL,
     textAlign: 'center',
     marginBottom: SPACING.MEDIUM,
     lineHeight: FONT_SIZES.SMALL * 1.5,
   },
   divider: {
     flexDirection: 'row',
     alignItems: 'center',
     marginVertical: SPACING.LARGE,
   },
   dividerLine: {
     flex: 1,
     height: 1,
     backgroundColor: COLORS.TEXT_GRAY,
     opacity: 0.3,
   },
   dividerText: {
     marginHorizontal: SPACING.MEDIUM,
     color: COLORS.TEXT_GRAY,
     fontSize: FONT_SIZES.SMALL,
   },
   appleButton: {
     marginTop: SPACING.SMALL,
   },
   appleButtonTop: {
     marginTop: SPACING.LARGE,
     marginBottom: SPACING.MEDIUM,
   },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.MEDIUM,
  },
  footerText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
  },
  footerLink: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
  },
}); 