import { 
  StyleSheet, 
  View, 
  Text, 
  FlatList, 
  RefreshControl, 
  TouchableOpacity, 
  Platform, 
  StatusBar,
  Image,
  Alert,
  ActivityIndicator,
  Animated
} from 'react-native';
import { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import { Event } from '../types/event';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/navigation';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';
import { createFavoriteService } from '../services/favoriteService';
import { useAuth } from '../context/AuthContext';
import { toastService } from '../services/toastService';
import LoadingAnimation from '../components/LoadingAnimation';

type FavoriteEventsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// 简化的收藏事件类型，只包含必要字段
type SimpleFavoriteEvent = {
  event_id: string;
  title: string;
  image: string;
  location: string;
  is_archived: boolean;
};

export default function FavoriteEventsScreen() {
  const navigation = useNavigation<FavoriteEventsScreenNavigationProp>();
  const { user } = useAuth();
  const [events, setEvents] = useState<SimpleFavoriteEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isPartialRefreshing, setIsPartialRefreshing] = useState(false);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const loadFavoriteEvents = useCallback(async () => {
    try {
      if (!user) {
        // 未登录情况下处理
        setEvents([]);
        return;
      }
      
      const favoriteService = createFavoriteService(user.id);
      
      // 使用优化后的方法获取收藏事件
      const { data, error } = await favoriteService.getUserFavoriteEvents();
      
      if (error) throw error;
      
      // 数据已经是我们需要的格式，直接设置
      setEvents(data as SimpleFavoriteEvent[]);
    } catch (error) {
      console.error('Loading favorites failed:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user]);

  // 初始加载
  useEffect(() => {
    loadFavoriteEvents();
  }, [loadFavoriteEvents]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadFavoriteEvents();
  };
  
  // 部分刷新，用于顶部刷新按钮
  const handlePartialRefresh = async () => {
    try {
      setIsPartialRefreshing(true);
      
      // 添加淡出动画
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: true
      }).start();
      
      await loadFavoriteEvents();
      
      // 添加淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } catch (error) {
      // 出错时也恢复透明度
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
      console.error('Failed to refresh favorites:', error);
    } finally {
      setIsPartialRefreshing(false);
    }
  };
  
  // 处理取消收藏
  const handleRemoveFavorite = (eventId: string, eventTitle: string) => {
    if (!user) return;
    
    // 显示确认对话框
    Alert.alert(
      "Remove Favorite",
      `Are you sure you want to remove "${eventTitle}" from your favorites?`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            try {
              const favoriteService = createFavoriteService(user.id);
              await favoriteService.removeFavorite(eventId);
              // 从列表中移除
              setEvents(prev => prev.filter(e => e.event_id !== eventId));
              toastService.showFavoriteRemoved(eventTitle);
            } catch (error) {
              console.error('Remove favorite failed:', error);
              toastService.show({
                title: 'Error',
                message: 'Failed to remove from favorites. Please try again.',
                duration: 3000,
              });
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingAnimation text="Loading favorites..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>My <Text style={{color: COLORS.PRIMARY}}>Favorites</Text></Text>
        <TouchableOpacity
          style={[styles.backButton, (refreshing || isPartialRefreshing) && styles.refreshButtonDisabled]}
          onPress={handlePartialRefresh}
          disabled={refreshing || isPartialRefreshing}
        >
          <Ionicons name="refresh" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
      </View>

      {isPartialRefreshing && (
        <View style={styles.partialRefreshContainer}>
          <ActivityIndicator size="small" color={COLORS.PRIMARY} />
          <Text style={styles.partialRefreshText}>Refreshing...</Text>
        </View>
      )}

      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        <FlatList
          data={events}
          keyExtractor={(item) => item.event_id}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={[
                styles.eventCard,
                item.is_archived && styles.archivedEventCard
              ]}
              onPress={() => !item.is_archived && navigation.navigate('EventDetails', { eventId: item.event_id })}
              activeOpacity={item.is_archived ? 1 : 0.9}
            >
              <View style={styles.imageContainer}>
                <Image source={{ uri: item.image }} style={styles.eventImage} />
                {item.is_archived && (
                  <View style={styles.archivedOverlay}>
                    <View style={styles.archivedBadge}>
                      <Ionicons name="archive" size={14} color={COLORS.TEXT_WHITE} />
                      <Text style={styles.archivedBadgeText}>Archived</Text>
                    </View>
                  </View>
                )}
              </View>
              <View style={styles.eventDetails}>
                <Text style={[
                  styles.eventTitle,
                  item.is_archived && styles.archivedText
                ]} numberOfLines={2}>
                  {item.title}
                </Text>
                <View style={styles.locationContainer}>
                  <Ionicons name="location-outline" size={16} color={item.is_archived ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
                  <Text style={[
                    styles.locationText,
                    item.is_archived && styles.archivedText
                  ]} numberOfLines={1}>
                    {item.location}
                  </Text>
                </View>
              </View>
              <TouchableOpacity 
                style={[
                  styles.favoriteButton,
                  item.is_archived && styles.archivedFavoriteButton
                ]}
                onPress={() => handleRemoveFavorite(item.event_id, item.title)}
              >
                <Ionicons 
                  name="heart" 
                  size={22} 
                  color={item.is_archived ? COLORS.TEXT_GRAY : COLORS.PRIMARY} 
                />
              </TouchableOpacity>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={COLORS.PRIMARY}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="heart-outline" size={48} color={COLORS.PRIMARY} />
              <Text style={styles.emptyText}>No Favorites Yet</Text>
              <Text style={styles.emptySubText}>Your favorite events will appear here</Text>
            </View>
          }
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.LARGE,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: SPACING.MEDIUM,
  },
  backButton: {
    width: 64,
    height: 48,
    borderRadius: RADIUS.LARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButtonDisabled: {
    opacity: 0.5,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  loadingText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
  },
  listContent: {
    padding: SPACING.LARGE,
    paddingBottom: SPACING.XLARGE,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.XLARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  emptyText: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    marginTop: SPACING.MEDIUM,
  },
  emptySubText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
  },
  // 新增样式 - 简化版事件卡片
  eventCard: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    marginBottom: 16,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  imageContainer: {
    position: 'relative',
    width: 100,
    height: 100,
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  eventDetails: {
    flex: 1,
    padding: 12,
    justifyContent: 'center',
  },
  eventTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 8,
    lineHeight: 20,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_LIGHT,
    flex: 1,
  },
  favoriteButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginRight: 8,
  },
  partialRefreshContainer: {
    position: 'absolute',
    top: 110,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: RADIUS.MEDIUM,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 9999
  },
  partialRefreshText: {
    color: COLORS.TEXT_WHITE,
    marginLeft: 10,
    fontSize: FONT_SIZES.SMALL
  },
  archivedEventCard: {
    opacity: 0.7,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  archivedText: {
    color: COLORS.TEXT_GRAY,
  },
  archivedFavoriteButton: {
    opacity: 0.7,
  },
  archivedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  archivedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: RADIUS.SMALL,
  },
  archivedBadgeText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.XSMALL,
    marginLeft: 4,
    fontWeight: '600',
  },
}); 