import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Dimensions,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import { SignUpScreenProps } from '../types/navigation';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, SPACING, RADIUS, FONT_SIZES, GRADIENTS } from '../config/theme';
import Button from '../components/Button';

export default function SignUpScreen({ navigation }: SignUpScreenProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState('');
  
  const validateForm = () => {
    if (!email || !password || !confirmPassword) {
      Alert.alert('Error', 'Please fill in all required fields');
      return false;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return false;
    }

    if (password.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return false;
    }

    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
      Alert.alert('Error', 'Password must contain uppercase, lowercase letters and numbers');
      return false;
    }

    return true;
  };

  const handleSignUpAttempt = async () => {
    if (!validateForm()) return;
    setLoading(true);
    try {
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: { data: { email: email } },
      });

      if (signUpError) {
        if (signUpError.message.includes('User already registered')) {
          Alert.alert('Registration Failed', 'This email is already registered. Please sign in or use a different email.');
        } else {
          throw signUpError;
        }
      } else {
        Alert.alert(
          'Verification Required',
          `A verification code has been sent to ${email}. Please enter it below.`
        );
        setShowOtpInput(true);
      }
    } catch (error: any) {
      console.error('Registration attempt error:', error);
      Alert.alert('Registration Failed', error.message || 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp || otp.length !== 6) {
      Alert.alert('Error', 'Please enter the 6-digit verification code.');
      return;
    }
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'signup',
      });

      if (error) throw error;

      Alert.alert('Success', 'Account verified successfully!');
      navigation.replace('Home', { screen: 'HomeTab' });
    } catch (error: any) {
      console.error('OTP Verification error:', error);
      Alert.alert('Verification Failed', error.message || 'Invalid or expired code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });
      if (error) throw error;
      Alert.alert('Code Resent', `A new verification code has been sent to ${email}.`);
      setOtp('');
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      Alert.alert('Error Resending Code', error.message || 'Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)']}
      style={styles.gradientContainer}
    >
      <StatusBar barStyle="light-content" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {navigation.canGoBack() && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => {
                if (showOtpInput) {
                  setShowOtpInput(false);
                  setOtp('');
                } else {
                  navigation.goBack();
                }
              }}
            >
              <Ionicons name="chevron-back" size={28} color={COLORS.TEXT_WHITE} />
            </TouchableOpacity>
          )}

          {!showOtpInput ? (
            <View style={styles.contentContainer}>
              <Ionicons 
                name="person-add-outline" 
                size={80} 
                color={COLORS.PRIMARY_LIGHT} 
                style={styles.icon} 
              />
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>Join Lezigo today!</Text>
              
              <View style={styles.form}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email"
                  placeholderTextColor={COLORS.TEXT_GRAY}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete='email'
                  textContentType='emailAddress'
                />

                <Text style={styles.label}>Password</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={styles.input}
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Enter password (min 8 chars)"
                    placeholderTextColor={COLORS.TEXT_GRAY}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoComplete='new-password'
                    textContentType='newPassword'
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons
                      name={showPassword ? 'eye-outline' : 'eye-off-outline'}
                      size={24}
                      color={COLORS.TEXT_GRAY}
                    />
                  </TouchableOpacity>
                </View>
                <View style={styles.passwordHintContainer}>
                   <Text style={styles.passwordHintText}>• Minimum 8 characters</Text>
                   <Text style={styles.passwordHintText}>• Uppercase, lowercase & number</Text>
                 </View>

                <Text style={styles.label}>Confirm Password</Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={styles.input}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder="Confirm your password"
                    placeholderTextColor={COLORS.TEXT_GRAY}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoComplete='new-password'
                    textContentType='newPassword'
                  />
                  <TouchableOpacity
                    style={styles.eyeButton}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons
                      name={showConfirmPassword ? 'eye-outline' : 'eye-off-outline'}
                      size={24}
                      color={COLORS.TEXT_GRAY}
                    />
                  </TouchableOpacity>
                </View>
                
                <Button
                  title="Create Account"
                  onPress={handleSignUpAttempt}
                  loading={loading}
                  style={styles.mainButton}
                />
              </View>
            </View>
          ) : (
            <View style={styles.contentContainer}>
              <Ionicons 
                name="keypad-outline"
                size={80} 
                color={COLORS.PRIMARY_LIGHT} 
                style={styles.icon} 
              />
              <Text style={styles.title}>Verify Your Email</Text>
              <Text style={styles.subtitle}>Enter the 6-digit code sent to:</Text>
              <Text style={styles.emailText}>{email}</Text> 
              
              <View style={styles.form}>
                 <Text style={styles.label}>Verification Code</Text>
                 <TextInput
                   style={[styles.input, styles.otpInput]}
                   value={otp}
                   onChangeText={setOtp}
                   placeholder="_ _ _ _ _ _"
                   placeholderTextColor={COLORS.TEXT_GRAY}
                   keyboardType="number-pad"
                   maxLength={6}
                   textContentType="oneTimeCode"
                 />
                 
                 <Button
                   title="Verify Code"
                   onPress={handleVerifyOtp}
                   loading={loading}
                   style={styles.mainButton}
                   disabled={otp.length !== 6}
                 />

                 <TouchableOpacity onPress={handleResendOtp} style={styles.resendButton} disabled={loading}>
                   <Text style={styles.resendText}>Resend Code</Text>
                 </TouchableOpacity>
              </View>
              
              <TouchableOpacity onPress={() => { setShowOtpInput(false); setOtp(''); }} style={styles.switchModeButton}>
                <Text style={styles.switchModeText}>Entered wrong email?</Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
  },
  keyboardAvoidView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: SPACING.LARGE,
    paddingBottom: SPACING.LARGE,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight || SPACING.LARGE : SPACING.XLARGE,
  },
  backButton: {
     position: 'absolute',
     top: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + SPACING.MEDIUM : SPACING.XLARGE + SPACING.SMALL,
     left: SPACING.MEDIUM,
     zIndex: 10,
     padding: SPACING.SMALL,
   },
  contentContainer: {
    alignItems: 'center',
  },
  icon: {
    marginTop: SPACING.LARGE,
    marginBottom: SPACING.MEDIUM,
  },
  title: {
    fontSize: FONT_SIZES.XXXLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
    marginBottom: SPACING.XLARGE,
    textAlign: 'center',
  },
  emailText: {
     fontSize: FONT_SIZES.MEDIUM,
     color: COLORS.TEXT_WHITE,
     fontWeight: '600',
     marginBottom: SPACING.XLARGE,
     textAlign: 'center',
   },
  form: {
    width: '100%',
    marginBottom: SPACING.LARGE,
  },
  label: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_LIGHT,
    marginBottom: SPACING.SMALL,
    marginLeft: SPACING.SMALL,
  },
  input: {
    backgroundColor: COLORS.TRANSPARENT_LIGHT,
    color: COLORS.TEXT_WHITE,
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: SPACING.SMALL,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  otpInput: {
     textAlign: 'center',
     fontSize: FONT_SIZES.LARGE,
     letterSpacing: Platform.OS === 'ios' ? 10 : 5,
   },
  passwordContainer: {
    position: 'relative',
    width: '100%',
    marginBottom: SPACING.XSMALL,
  },
  eyeButton: {
    position: 'absolute',
    right: SPACING.MEDIUM,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: SPACING.SMALL,
  },
  passwordHintContainer: {
     width: '100%',
     paddingLeft: SPACING.SMALL,
     marginBottom: SPACING.MEDIUM,
   },
   passwordHintText: {
     fontSize: FONT_SIZES.XSMALL,
     color: COLORS.TEXT_GRAY,
     lineHeight: FONT_SIZES.XSMALL * 1.5,
   },
   mainButton: {
     width: '100%',
     marginTop: SPACING.MEDIUM,
   },
   resendButton: {
     marginTop: SPACING.MEDIUM,
     alignItems: 'center',
     padding: SPACING.SMALL,
   },
   resendText: {
     color: COLORS.PRIMARY_LIGHT,
     fontSize: FONT_SIZES.SMALL,
   },
   switchModeButton: {
     marginTop: SPACING.LARGE,
     padding: SPACING.SMALL,
   },
   switchModeText: {
     color: COLORS.TEXT_LIGHT,
     fontSize: FONT_SIZES.SMALL,
     textAlign: 'center',
   },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: SPACING.MEDIUM,
  },
  footerText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
  },
  footerLink: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
  },
}); 