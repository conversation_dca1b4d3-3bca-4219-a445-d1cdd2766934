import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ScrollView,
  Platform,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING, RADIUS } from '../config/theme';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';
import { NotificationSettingsScreenProps } from '../types/navigation';
import LoadingAnimation from '../components/LoadingAnimation';

interface NotificationPreferences {
  event_reminders: boolean;
  ticket_notifications: boolean;
  promotional_messages: boolean;
  push_enabled: boolean;
}

export default function NotificationSettingsScreen({ navigation }: NotificationSettingsScreenProps) {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    event_reminders: true,
    ticket_notifications: true,
    promotional_messages: true,
    push_enabled: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setPreferences({
          event_reminders: data.event_reminders,
          ticket_notifications: data.ticket_validated || true, // 映射到ticket_validated字段
          promotional_messages: data.promotional_messages ?? false,
          push_enabled: data.push_enabled,
        });
      }
      // 如果没有数据，保持默认值（都为true）
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      Alert.alert('Error', 'Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async (newPreferences: NotificationPreferences) => {
    if (!user?.id) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: user.id,
          event_reminders: newPreferences.event_reminders,
          ticket_validated: newPreferences.ticket_notifications, // 映射到数据库字段
          promotional_messages: newPreferences.promotional_messages,
          push_enabled: newPreferences.push_enabled,
          // 其他字段使用默认值
          event_updates: true,
          email_enabled: true,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id'
        });

      if (error) throw error;

      setPreferences(newPreferences);
      console.log('Notification preferences saved successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert('Error', 'Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (key: keyof NotificationPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value };
    
    // 如果禁用推送通知，也禁用所有依赖于推送的设置
    if (key === 'push_enabled' && !value) {
      newPreferences.event_reminders = false;
      newPreferences.ticket_notifications = false;
      newPreferences.promotional_messages = false;
    }
    
    savePreferences(newPreferences);
  };

  const renderSettingItem = (
    key: keyof NotificationPreferences,
    title: string,
    description: string,
    icon: string,
    disabled?: boolean
  ) => (
    <View style={[styles.settingItem, disabled && styles.disabledItem]}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={disabled ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, disabled && styles.disabledText]}>{title}</Text>
          <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={preferences[key]}
        onValueChange={(value) => handleToggle(key, value)}
        trackColor={{ false: COLORS.BACKGROUND_CARD, true: COLORS.PRIMARY_TRANSPARENT }}
        thumbColor={preferences[key] ? COLORS.PRIMARY : COLORS.TEXT_GRAY}
        disabled={disabled || saving}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
          </TouchableOpacity>
          <Text style={styles.title}>Notification Settings</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <LoadingAnimation text="Loading preferences..." />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.title}>Notification Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Push Notifications</Text>
          <Text style={styles.sectionDescription}>
            Control how you receive notifications on your device
          </Text>

          {renderSettingItem(
            'push_enabled',
            'Enable Push Notifications',
            'Receive notifications on your device',
            'notifications'
          )}

          <View style={styles.divider} />

          {renderSettingItem(
            'event_reminders',
            'Event Reminders',
            'Get notified 1 hour before your events start',
            'time',
            !preferences.push_enabled
          )}



          {renderSettingItem(
            'ticket_notifications',
            'Ticket Validation',
            'Welcome notifications when entering events',
            'ticket',
            !preferences.push_enabled
          )}

          {renderSettingItem(
            'promotional_messages',
            'Promotional Messages',
            'Special offers and event recommendations',
            'megaphone',
            !preferences.push_enabled
          )}
        </View>

        <View style={styles.infoContainer}>
          <Ionicons name="information-circle" size={20} color={COLORS.TEXT_GRAY} />
          <Text style={styles.infoText}>
            You can always change these settings later. Some notifications are required for account security and cannot be disabled.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.LARGE,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: SPACING.MEDIUM,
  },
  backButton: {
    width: 64,
    height: 48,
    borderRadius: RADIUS.LARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: SPACING.LARGE,
  },
  section: {
    marginBottom: SPACING.LARGE,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
  },
  sectionDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.LARGE,
    lineHeight: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.BACKGROUND_CARD,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  disabledItem: {
    opacity: 0.5,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: SPACING.MEDIUM,
    flex: 1,
  },
  settingTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.XSMALL,
  },
  settingDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    lineHeight: 18,
  },
  disabledText: {
    color: COLORS.TEXT_GRAY,
    opacity: 0.7,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.SMALL,
    marginTop: SPACING.LARGE,
  },
  infoText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginLeft: SPACING.SMALL,
    flex: 1,
    lineHeight: 18,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.PRIMARY_TRANSPARENT,
    marginVertical: SPACING.MEDIUM,
    marginHorizontal: SPACING.SMALL,
  },
}); 