import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Platform,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ChangePasswordScreenProps } from '../types/navigation';
import Button from '../components/Button';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';

export default function ChangePasswordScreen() {
  const navigation = useNavigation<ChangePasswordScreenProps['navigation']>();
  const { signOut } = useAuth();
  const [newPassword, setNewPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
      return 'Password must contain uppercase, lowercase letters and numbers';
    }

    return null;
  };

  // 更新密码
  const handleUpdatePassword = async () => {
    try {
      setLoading(true);

      if (!newPassword) {
        Alert.alert('Error', 'Please enter your new password');
        return;
      }

      // 验证新密码的强度
      const passwordError = validatePassword(newPassword);
      if (passwordError) {
        Alert.alert('Error', passwordError);
        return;
      }

      // 更新密码
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      // 先显示成功消息，然后再退出登录
      Alert.alert(
        'Success',
        'Password has been changed successfully. You will now be signed out.',
        [
          {
            text: 'OK',
            onPress: async () => {
              try {
                // 使用 AuthContext 的 signOut 方法，它会处理完整的登出流程
                // 包括清理 push tokens 和调用 supabase.auth.signOut()
                await signOut();
                // 返回到ProfileScreen
                navigation.navigate('Home', { screen: 'Profile' });
              } catch (error) {
                console.error('Error signing out:', error);
                // 即使退出登录失败，也返回到ProfileScreen
                navigation.navigate('Home', { screen: 'Profile' });
              }
            }
          }
        ]
      );
    } catch (error: any) {
      console.error('Error changing password:', error);
      Alert.alert(
        'Error',
        error.message || 'Failed to change password. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const renderPasswordInput = (
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    showPassword: boolean,
    toggleShowPassword: () => void
  ) => (
    <View style={styles.inputContainer}>
      <TextInput
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor="#666"
        secureTextEntry={!showPassword}
        autoCapitalize="none"
      />
      <TouchableOpacity
        style={styles.eyeButton}
        onPress={toggleShowPassword}
      >
        <Ionicons
          name={showPassword ? 'eye-outline' : 'eye-off-outline'}
          size={24}
          color="#666"
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Change Password</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <View style={styles.form}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>New Password</Text>
            {renderPasswordInput(
              newPassword,
              setNewPassword,
              'Enter new password',
              showNewPassword,
              () => setShowNewPassword(!showNewPassword)
            )}

            <View style={styles.tips}>
              <Text style={styles.tipsTitle}>Password Requirements:</Text>
              <Text style={styles.tipsText}>• Minimum 8 characters</Text>
              <Text style={styles.tipsText}>• Include uppercase and lowercase letters</Text>
              <Text style={styles.tipsText}>• Include numbers</Text>
            </View>

            <Button
              title="Update Password"
              onPress={handleUpdatePassword}
              style={styles.submitButton}
              loading={loading}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
  },
  backButton: {
    width: 64,
    height: 48,
    borderRadius: 20,
    backgroundColor: '#1c1c1e',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  form: {
    marginTop: 20,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 15,
  },
  inputContainer: {
    marginBottom: 20,
    position: 'relative',
  },
  input: {
    height: 56,
    backgroundColor: '#1c1c1e',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#fff',
    paddingRight: 50,
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    top: 16,
    padding: 4,
  },
  tips: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#1c1c1e',
    borderRadius: 12,
    marginBottom: 30,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
  },
  tipsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  submitButton: {
    marginTop: 10,
  },
}); 