import { StyleSheet, Text, View, FlatList, StatusBar, ActivityIndicator, Animated, Platform } from 'react-native';
import FeaturedEvent from '../components/FeaturedEvent';
import EventCard from '../components/EventCard';
import { HomeScreenProps } from '../types/navigation';
import { eventService, PaginatedResult } from '../services/eventService';
import { favoriteService } from '../services/favoriteService';
import { useEffect, useState, useRef, useCallback } from 'react';
import { Database } from '../types/database';
import { COLORS, RADIUS, FONT_SIZES, SPACING } from '../config/theme';
import LoadingAnimation from '../components/LoadingAnimation';
import Ionicons from 'react-native-vector-icons/Ionicons';

type Event = Database['public']['Tables']['events']['Row'] & {
  sessions: Database['public']['Tables']['event_sessions']['Row'][];
  organizer: Database['public']['Tables']['organizer_profiles']['Row'];
  event_id: string;  // 确保event_id存在
  title: string;     // 确保title存在
  image: string;     // 确保image存在
};

type UIEvent = Event & {
  date?: string;
  fullAddress: string | null;
  isFeatured: boolean;
  participants: {
    current: number;
    total: number;
  };
  price: number;
};

type TrendingEvent = {
  event_id: string;
  title: string;
  location: string;
  image: string;
  is_featured: boolean;
  is_preview: boolean;
  sessions: {
    id: string;
    start_time: string;
    price: number | null;
  }[];
  organizer: {
    organizer_id: string;
    display_name: string | null;
  };
  price?: number;  // 预处理后的最低价格
};

type UITrendingEvent = TrendingEvent & {
  date?: string;
  price: number | 'Free';
  is_preview?: boolean;
};

export default function HomeScreen({ navigation }: HomeScreenProps) {
  const [events, setEvents] = useState<TrendingEvent[]>([]);
  const [featuredEvent, setFeaturedEvent] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [favoriteStatus, setFavoriteStatus] = useState<Record<string, boolean>>({});
  const [isPartialRefreshing, setIsPartialRefreshing] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current; // 用于事件列表的透明度动画
  const PAGE_SIZE = 10;

  // 检查是否在顶部
  const isAtTop = () => {
    return flatListRef.current ? true : false; // 默认为true，在下面添加监听时获取实际值
  };

  // 跟踪滚动位置
  const [isAtTopPosition, setIsAtTopPosition] = useState(true);

  // 监听滚动事件
  const handleScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setIsAtTopPosition(offsetY <= 0);
  };

  useEffect(() => {
    loadEvents();
  }, []);

  // 添加滚动到顶部的函数
  const scrollToTop = () => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  };

  // 部分刷新内容 - 刷新特色活动和事件列表
  const partialRefreshContent = async () => {
    try {
      setIsPartialRefreshing(true);
      
      // 添加淡出动画
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: true
      }).start();
      
      // 获取趋势活动（不使用缓存，获取最新数据）
      const result = await eventService.getTrendingEvents(0, PAGE_SIZE, false);
      
      // 处理特色活动
      const featured = result.data.find(event => event.is_featured);
      if (featured) {
        // 直接使用趋势活动中的信息
        setFeaturedEvent(featured as unknown as Event);
      } else {
        setFeaturedEvent(null);
      }
      
      // 过滤掉特色事件，只更新普通事件列表
      const nonFeatured = result.data.filter(event => !event.is_featured);
      setEvents(nonFeatured as unknown as TrendingEvent[]);
      
      // 更新收藏状态（包含特色活动和普通活动）
      const allEvents = featured ? [featured, ...nonFeatured] : nonFeatured;
      if (allEvents.length > 0) {
        const eventIds = allEvents.map(event => event.event_id);
        const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
        
        setFavoriteStatus(prev => ({
          ...prev,
          ...batchFavoriteStatus
        }));
      }
      
      setCurrentPage(0);
      setHasMore(result.hasMore);
      
      // 添加淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
    } catch (error) {
      // 出错时也恢复透明度
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();
      console.error('Failed to refresh content:', error);
    } finally {
      setIsPartialRefreshing(false);
    }
  };

  // 设置tabPress监听器
  useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', (e) => {
      // 如果已经在Home tab
      if (navigation.isFocused()) {
        e.preventDefault();
        // 检查是否在顶部，如果是则刷新内容，否则滚动到顶部
        if (isAtTopPosition) {
          partialRefreshContent();
        } else {
          scrollToTop();
        }
      }
    });

    return unsubscribe;
  }, [navigation, isAtTopPosition]);

  const loadEvents = async (page = 0) => {
    try {
      if (page === 0) {
        setIsLoading(true);
      } else {
        // 如果已经没有更多数据，直接返回
        if (!hasMore) {
          setIsLoadingMore(false);
          return;
        }
        setIsLoadingMore(true);
      }

      // 首次加载时，先尝试使用缓存数据
      if (page === 0) {
        // 获取趋势活动（使用缓存）
        const result = await eventService.getTrendingEvents(page, PAGE_SIZE, true);
        
        // 处理数据
        let allEvents: TrendingEvent[] = [];
        
        // 首次加载，找出特色事件
        const featured = result.data.find(event => event.is_featured);
        
        if (featured) {
          // 直接使用趋势活动中的信息，不再单独获取完整信息
          setFeaturedEvent(featured as unknown as Event);
        } else {
          setFeaturedEvent(null);
        }
        
        // 过滤掉特色事件，只显示普通事件
        const nonFeatured = result.data.filter(event => !event.is_featured);
        setEvents(nonFeatured as unknown as TrendingEvent[]);
        
        // 收集所���事件ID（包括特色事件）
        allEvents = (featured ? [featured, ...nonFeatured] : nonFeatured) as unknown as TrendingEvent[];

        // 批量获取事件的收藏状态
        if (allEvents.length > 0) {
          const eventIds = allEvents.map(event => event.event_id);
          const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
          setFavoriteStatus(prev => ({
            ...prev,
            ...batchFavoriteStatus
          }));
        }

        // 更新分页状态
        setCurrentPage(0);
        setHasMore(result.hasMore);

        // 在后台重新获取最新数据
        setTimeout(async () => {
          try {
            // 获取趋势活动（不使用缓存）
            const latestResult = await eventService.getTrendingEvents(page, PAGE_SIZE, false);
            
            // 处理数据
            const latestFeatured = latestResult.data.find(event => event.is_featured);
            
            if (latestFeatured) {
              // 直接使用趋势活动中的信息
              setFeaturedEvent(latestFeatured as unknown as Event);
            }
            
            // 过滤掉特色事件，只显示普通事件
            const latestNonFeatured = latestResult.data.filter(event => !event.is_featured);
            setEvents(latestNonFeatured as unknown as TrendingEvent[]);
            
            // 更新收藏状态
            const latestAllEvents = latestFeatured ? [latestFeatured, ...latestNonFeatured] : latestNonFeatured;
            if (latestAllEvents.length > 0) {
              const eventIds = latestAllEvents.map(event => event.event_id);
              const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
              setFavoriteStatus(prev => ({
                ...prev,
                ...batchFavoriteStatus
              }));
            }

            // 更新分页状态
            setHasMore(latestResult.hasMore);
          } catch (error) {
            console.error('Failed to update data in background:', error);
          }
        }, 100);
      } else {
        // 加载更多时，直接从服务器获取数据
        const result = await eventService.getTrendingEvents(page, PAGE_SIZE, false);
        
        // 如果没有新数据，更新状态并返回
        if (result.data.length === 0) {
          setHasMore(false);
          return;
        }

        // 加载更多时，追加数据（过滤掉特色事件）
        const nonFeatured = result.data.filter(event => !event.is_featured) as unknown as TrendingEvent[];
        setEvents(prev => [...prev, ...nonFeatured]);
        
        // 收集新加载的事件ID
        if (nonFeatured.length > 0) {
          const eventIds = nonFeatured.map(event => event.event_id);
          const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
          setFavoriteStatus(prev => ({
            ...prev,
            ...batchFavoriteStatus
          }));
        }

        // 更新分页状态
        setCurrentPage(page);
        setHasMore(result.hasMore);
      }
    } catch (error) {
      console.error('Failed to load events:', error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  // 更新收藏状态的函数
  const updateFavoriteStatus = useCallback((eventId: string, status: boolean) => {
    setFavoriteStatus(prev => ({
      ...prev,
      [eventId]: status
    }));
  }, []);

  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadEvents(currentPage + 1);
    }
  };

  const handleEventPress = (event: Event) => {
    navigation.navigate('EventDetails', { eventId: event.event_id });
  };

  // 将数据库事件转换为UI所需的格式
  const convertTrendingEventForUI = (event: TrendingEvent): UITrendingEvent => {
    // 获取最早的场次
    const earliestSession = event.sessions.reduce((earliest, current) => {
      if (!earliest) return current;
      return new Date(current.start_time) < new Date(earliest.start_time) ? current : earliest;
    }, event.sessions[0]);

    return {
      ...event,
      date: earliestSession?.start_time 
        ? new Date(earliestSession.start_time).toLocaleDateString('en-AU', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : undefined,
      price: event.price ?? 0,
      is_preview: event.is_preview,
    };
  };

  // 渲染列表头部（特色事件）
  const renderHeader = useCallback(() => {
    return (
      <FeaturedEvent 
        event={featuredEvent || undefined}
        onTicketPress={() => featuredEvent && handleEventPress(featuredEvent as Event)}
      />
    );
  }, [featuredEvent]);

  // 渲染列表项
  const renderItem = useCallback(({ item }: { item: TrendingEvent }) => {
    const uiEvent = convertTrendingEventForUI(item);
    return (
      <EventCard
        event={uiEvent}
        onPress={() => navigation.navigate('EventDetails', { eventId: item.event_id })}
        isFavorite={favoriteStatus[item.event_id] || false}
        onFavoriteChange={(status) => updateFavoriteStatus(item.event_id, status)}
      />
    );
  }, [favoriteStatus, updateFavoriteStatus]);

  // 渲染列表底部（加载更多指示器）
  const renderFooter = () => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.loadingMoreContainer}>
        <LoadingAnimation size="small" text="Loading more..." />
      </View>
    );
  };

  // 渲染列表为空时的组件
  const renderEmpty = useCallback(() => {
    if (isLoading) return null;
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="calendar-outline" size={48} color={COLORS.PRIMARY} />
        <Text style={styles.emptyTitle}>Stay Tuned!</Text>
        <Text style={styles.emptyText}>Exciting events are coming soon.</Text>
        <Text style={styles.emptySubText}>We're curating amazing experiences just for you.</Text>
      </View>
    );
  }, [isLoading]);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LoadingAnimation text="Loading events..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent backgroundColor="transparent" />
      {isPartialRefreshing && (
        <View style={styles.partialRefreshContainer}>
          <LoadingAnimation size="small" text="Refreshing..." />
        </View>
      )}
      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        <FlatList
          ref={flatListRef}
          data={events}
          renderItem={renderItem}
          keyExtractor={item => item.event_id}
          onScroll={handleScroll}
          scrollEventThrottle={16} // 确保性能的同时获取准确的滚动位置
          ListHeaderComponent={
            <>
              {renderHeader()}
              <View style={styles.trendingSection}>
                <Text style={styles.sectionTitle}>
                  TRENDING <Text style={{color: COLORS.PRIMARY}}>EVENTS</Text>
                </Text>
              </View>
            </>
          }
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmpty}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONT_SIZES.MEDIUM,
    marginTop: 12
  },
  listContent: {
    paddingBottom: Platform.OS === 'android' ? 20 : 80,
    paddingHorizontal: 20,
    alignItems: 'center'
  },
  trendingSection: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.TITLE,
    fontWeight: 'bold',
    color: '#fff',
    letterSpacing: 1,
    marginTop: 16,
    marginBottom: 16,
  },
  footerLoader: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },
  loadingMoreText: {
    color: COLORS.TEXT_LIGHT,
    marginLeft: 10
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
    borderRadius: RADIUS.MEDIUM,
    marginTop: 20,
    backgroundColor: COLORS.BACKGROUND_CARD,
    paddingVertical: 40,
  },
  emptyTitle: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: 4,
    textAlign: 'center',
  },
  emptySubText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    textAlign: 'center',
  },
  partialRefreshContainer: {
    position: 'absolute',
    top: 50,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: RADIUS.MEDIUM,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 9999
  },
  partialRefreshText: {
    color: COLORS.TEXT_WHITE,
    marginLeft: 10,
    fontSize: FONT_SIZES.SMALL
  },
  loadingMoreContainer: {
    padding: 20,
    alignItems: 'center',
  },
}); 