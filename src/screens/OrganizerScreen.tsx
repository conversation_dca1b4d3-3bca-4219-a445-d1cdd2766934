import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, SafeAreaView, Platform, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { OrganizerScreenProps } from '../types/navigation';
import { useEffect, useState, useCallback } from 'react';
import { eventService } from '../services/eventService';
import { Database } from '../types/database';
import EventCard from '../components/EventCard';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';
import LoadingAnimation from '../components/LoadingAnimation';
import { favoriteService } from '../services/favoriteService';

type Organizer = Database['public']['Tables']['organizer_profiles']['Row'] & {
  user_profiles?: {
    avatar_url: string | null;
  };
  events: (Database['public']['Tables']['events']['Row'] & {
      sessions: Database['public']['Tables']['event_sessions']['Row'][];
  })[];
};

type UIOrganizer = Organizer & {
  totalEvents: number;
  totalSessions: number;
};

export default function OrganizerScreen({ route, navigation }: OrganizerScreenProps) {
  const { organizerId } = route.params;
  const [organizer, setOrganizer] = useState<UIOrganizer | null>(null);
  const [favoriteStatus, setFavoriteStatus] = useState<Record<string, boolean>>({});
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  useEffect(() => {
    loadOrganizer();
  }, [organizerId]);

  // 更新收藏状态的函数
  const updateFavoriteStatus = useCallback((eventId: string, status: boolean) => {
    setFavoriteStatus(prev => ({
      ...prev,
      [eventId]: status
    }));
  }, []);

  const loadOrganizer = async () => {
    try {
      const data = await eventService.getOrganizerById(organizerId);
      if (data) {
        const events = data.events;

        setOrganizer({
          ...data,
          totalEvents: events.length,
          totalSessions: events.reduce((sum, event) => sum + event.sessions.length, 0)
        });

        // 批量获取收藏状态
        if (events.length > 0) {
          const eventIds = events.map(event => event.event_id);
          const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
          setFavoriteStatus(batchFavoriteStatus);
        }
      }
    } catch (error) {
      console.error('Failed to load organizer details:', error);
    }
  };

  if (!organizer) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LoadingAnimation text="Loading organizer profile..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent backgroundColor="transparent" />
      <SafeAreaView style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <View style={styles.placeholder} />
        <View style={styles.placeholder} />
      </SafeAreaView>

      <ScrollView style={styles.content}>
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Image
              source={organizer.user_profiles?.avatar_url
                ? { uri: organizer.user_profiles.avatar_url }
                : require('../../assets/default-avatar-1.png')
              }
              style={styles.avatar}
              resizeMode="cover"
            />
          </View>

          <Text style={styles.organizerName}>{organizer.display_name || 'Organizer'}</Text>
          <Text style={[
            styles.verificationStatus,
            organizer.verification_status === 'verified' && styles.verifiedStatus
          ]}>
            {organizer.verification_status === 'verified' ? 'Verified Organizer' : 'Organizer'}
          </Text>

          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{organizer.totalEvents}</Text>
              <Text style={styles.statLabel}>Total Events</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{organizer.totalSessions}</Text>
              <Text style={styles.statLabel}>Total Sessions</Text>
            </View>
          </View>
        </View>

        <View style={styles.eventsSection}>
          <Text style={styles.sectionTitle}>
            <Text style={{color: COLORS.PRIMARY}}>Events</Text>
          </Text>
          <View style={styles.eventsList}>
            {organizer.events.length > 0 ? (
              organizer.events.map((event) => (
                <EventCard
                  key={event.event_id}
                  event={{
                    ...event,
                    date: event.sessions[0]?.start_time,
                    fullAddress: event.full_address,
                    isFeatured: event.is_featured,
                    participants: {
                      current: 0, // 这里需要从实际的订单数据计算
                      total: event.sessions.reduce((sum: number, session) => sum + session.capacity, 0)
                    },
                    price: event.sessions.length > 0 ? event.sessions[0].price ?? 'Free' : 'Free',
                    organizer: {
                      id: organizer.organizer_id,
                      display_name: organizer.display_name || 'Unknown Organizer',
                      avatar_url: organizer.avatar_url,
                      verification_status: organizer.verification_status
                    }
                  }}
                  onPress={() => navigation.navigate('EventDetails', { eventId: event.event_id })}
                  isFavorite={favoriteStatus[event.event_id] || false}
                  onFavoriteChange={(status) => updateFavoriteStatus(event.event_id, status)}
                />
              ))
            ) : (
              <Text style={styles.noEvents}>No events available</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONT_SIZES.MEDIUM
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    paddingTop: SPACING.MEDIUM,
    paddingBottom: SPACING.MEDIUM,
  },
  backButton: {
    width: 64,
    height: 48,
    borderRadius: RADIUS.LARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        marginHorizontal: 16,
      }
    }),
  },
  headerTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1
  },
  profileSection: {
    alignItems: 'center',
    padding: SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.PRIMARY_TRANSPARENT
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: SPACING.MEDIUM,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    borderRadius: 55,
    padding: 3,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center'
  },

  organizerName: {
    fontSize: FONT_SIZES.XXLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL
  },
  verificationStatus: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.LARGE
  },
  verifiedStatus: {
    color: COLORS.SUCCESS
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: SPACING.LARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM
  },
  statItem: {
    alignItems: 'center',
    marginHorizontal: SPACING.LARGE
  },
  statNumber: {
    fontSize: FONT_SIZES.XXLARGE,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 4
  },
  statLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_LIGHT
  },
  statDivider: {
    width: 1,
    height: '100%',
    backgroundColor: COLORS.PRIMARY_TRANSPARENT
  },
  eventsSection: {
    padding: SPACING.LARGE
  },
  sectionTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM
  },
  eventsList: {
    // gap: SPACING.MEDIUM
  },
  noEvents: {
    textAlign: 'center',
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.MEDIUM,
    marginTop: SPACING.LARGE
  }
}); 
