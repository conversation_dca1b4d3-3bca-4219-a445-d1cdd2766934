import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Dimensions,
  Share,
  FlatList,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { eventService } from '../services/eventService';
import { orderService } from '../services/orderService';
import { ticketService, Ticket } from '../services/ticketService';
import { LinearGradient } from 'expo-linear-gradient';
import QRCode from 'react-native-qrcode-svg';
import { Database } from '../types/database';
import { v4 as uuidv4 } from 'uuid';
import { COLORS, SPACING, RADIUS, FONT_SIZES, GRADIENTS } from '../config/theme';
import LoadingAnimation from '../components/LoadingAnimation';
import FastImage from 'react-native-fast-image';

const { width } = Dimensions.get('window');
const TICKET_WIDTH = width * 0.85;

type Event = Database['public']['Tables']['events']['Row'] & {
  sessions: Database['public']['Tables']['event_sessions']['Row'][];
  organizer: Database['public']['Tables']['organizer_profiles']['Row'];
};

type Order = Database['public']['Tables']['orders']['Row'] & {
  event_sessions: any;
};

type TicketStatus = {
  status: 'valid' | 'used' | 'expired' | 'cancelled';
  label: string;
  color: string;
};

const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return 'TBD';
  
  const date = new Date(dateTimeStr);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return 'Invalid Date';
  
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  
  return date.toLocaleString('en-US', options);
};

const formatDate = (dateStr: string | null) => {
  if (!dateStr) return 'TBD';
  
  const date = new Date(dateStr);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return 'Invalid Date';
  
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  
  return date.toLocaleString('en-US', options);
};

const DashedLine = () => {
  const dashCount = Math.floor(TICKET_WIDTH / 12); // 每个虚线段的宽度为12
  return (
    <View style={styles.dashedLineContainer}>
      {Array(dashCount).fill(0).map((_, index) => (
        <View key={index} style={styles.dash} />
      ))}
    </View>
  );
};

const getTicketStatus = (ticket: Ticket, event: Event, session: Database['public']['Tables']['event_sessions']['Row'] | null = null): TicketStatus => {
  const now = new Date();
  // 使用提供的session或event的第一个session
  const eventDate = session?.start_time 
    ? new Date(session.start_time) 
    : (event.sessions[0]?.start_time ? new Date(event.sessions[0].start_time) : null);
  
  // 根据票证状态返回对应的显示信息
  switch (ticket.status) {
    case 'used':
      return {
        status: 'used',
        label: 'Used',
        color: COLORS.WARNING  // 使用主题色
      };
    case 'cancelled':
      return {
        status: 'cancelled',
        label: 'Cancelled',
        color: COLORS.ERROR  // 使用主题色
      };
    default:
      // 如果活动已结束（24小时后）
      if (eventDate && now.getTime() > eventDate.getTime() + 24 * 60 * 60 * 1000) {
        return {
          status: 'expired',
          label: 'Expired',
          color: COLORS.ERROR  // 使用主题色
        };
      }
      
      // 默认为有效状态
      return {
        status: 'valid',
        label: 'Valid',
        color: COLORS.SUCCESS  // 使用主题色
      };
  }
};

const TicketScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { eventId, orderId } = route.params as { eventId: string; orderId: string };
  const [event, setEvent] = useState<Event | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [currentTicketIndex, setCurrentTicketIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [purchasedSession, setPurchasedSession] = useState<Database['public']['Tables']['event_sessions']['Row'] | null>(null);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    loadEventAndOrderData();
  }, [eventId, orderId]);

  const loadEventAndOrderData = async () => {
    try {
      setLoading(true);
      
      // 并行加载活动和订单数据
      const [eventData, orderData] = await Promise.all([
        eventService.getEventById(eventId),
        orderService.getOrderById(orderId),
      ]);
      
      setEvent(eventData);
      setOrder(orderData);
      
      // 找到用户购买的session
      if (eventData && eventData.sessions && orderData) {
        const orderedSessionId = orderData.session_id;
        const session = eventData.sessions.find(
          (session: Database['public']['Tables']['event_sessions']['Row']) => 
          session.id === orderedSessionId
        );
        if (session) {
          setPurchasedSession(session);
        } else {
          // 如果找不到，回退到第一个session
          setPurchasedSession(eventData.sessions[0] || null);
        }
      }
      
      // 获取该订单下的所有票据
      let ticketsData = await ticketService.getTicketsByOrderId(orderId);
      
      if (ticketsData.length === 0) {
        // 如果没有具体票据记录，根据订单数量创建临时票据对象用于显示
        const tempTickets = Array(orderData.quantity || 1).fill(0).map((_, index) => ({
          ticket_id: uuidv4(),
          order_id: orderId,
          status: 'valid',
          check_in_time: null,
          created_at: orderData.created_at,
          updated_at: orderData.updated_at
        }));
        setTickets(tempTickets);
      } else {
        setTickets(ticketsData);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToTicket = (index: number) => {
    setCurrentTicketIndex(index);
    flatListRef.current?.scrollToIndex({ index, animated: true });
  };

  const handleShare = async () => {
    if (!event || !tickets[currentTicketIndex]) return;
    
    const formattedTicketId = ticketService.formatTicketId(tickets[currentTicketIndex].ticket_id);
    
    try {
      await Share.share({
        message: `My event ticket: ${event.title}\nTicket number: ${formattedTicketId}`,
      });
    } catch (error) {
      console.error(error);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LoadingAnimation text="Loading ticket details..." />
      </View>
    );
  }

  if (!event || !tickets.length) return null;
  
  const currentTicket = tickets[currentTicketIndex];
  const ticketStatus = getTicketStatus(currentTicket, event, purchasedSession);
  const ticketCode = currentTicket.ticket_id;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>E-Ticket</Text>
        <TouchableOpacity 
          style={styles.shareButton}
          onPress={handleShare}
        >
          <Ionicons name="share-outline" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
      </View>

      {tickets.length > 1 && (
        <View style={styles.ticketPagination}>
          <Text style={styles.paginationText}>
            Ticket {currentTicketIndex + 1} / {tickets.length}
          </Text>
          <View style={styles.paginationButtons}>
            {tickets.map((_, index) => (
              <TouchableOpacity
                key={`pagination-${index}`}
                style={[
                  styles.paginationButton,
                  currentTicketIndex === index && styles.activePaginationButton
                ]}
                onPress={() => navigateToTicket(index)}
              >
                <Text 
                  style={[
                    styles.paginationButtonText,
                    currentTicketIndex === index && styles.activePaginationButtonText
                  ]}
                >
                  {index + 1}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      <FlatList
        ref={flatListRef}
        data={tickets}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        initialScrollIndex={currentTicketIndex}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(
            event.nativeEvent.contentOffset.x / width
          );
          setCurrentTicketIndex(index);
        }}
        getItemLayout={(_, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        renderItem={({ item: ticket, index }) => {
          const ticketStatus = getTicketStatus(ticket, event, purchasedSession);
          // 格式化票号为更易读的形式
          const formattedTicketId = ticketService.formatTicketId(ticket.ticket_id);
          
          return (
            <View style={styles.content} key={`ticket-${ticket.ticket_id}`}>
              <ScrollView 
                contentContainerStyle={styles.scrollableTicketContainer}
                showsVerticalScrollIndicator={false}
              >
                <View style={styles.ticket}>
                  <LinearGradient
                    colors={['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)']}
                    style={styles.ticketHeader}
                  >
                    <FastImage 
                      source={{
                        uri: event.image,
                        priority: FastImage.priority.normal,
                        cache: FastImage.cacheControl.immutable
                      }} 
                      style={styles.eventImage}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                    <View style={styles.eventInfo}>
                      <Text style={styles.eventTitle}>{event.title}</Text>
                      {purchasedSession && (
                        <Text style={styles.sessionName}>{purchasedSession.title}</Text>
                      )}
                      <Text style={styles.eventDate}>
                        {formatDateTime(purchasedSession ? purchasedSession.start_time : null)}
                      </Text>
                      <Text style={styles.eventLocation}>{event.location}</Text>
                    </View>
                  </LinearGradient>

                  <DashedLine />

                  <View style={styles.ticketBody}>
                    <View style={styles.leftCircle} />
                    <View style={styles.rightCircle} />
                    <View style={styles.ticketDetail}>
                      <View style={styles.detailItem}>
                        <Text style={styles.detailLabel}>Ticket Number</Text>
                        <Text style={styles.detailValue}>
                          #{index + 1}
                        </Text>
                      </View>
                      <View style={styles.detailItem}>
                        <Text style={styles.detailLabel}>Status</Text>
                        <View style={[styles.statusBadge, { backgroundColor: `${ticketStatus.color}20` }]}>
                          <Text style={[styles.statusText, { color: ticketStatus.color }]}>
                            {ticketStatus.label}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View style={styles.barcodeContainer}>
                      <QRCode
                        value={ticket.ticket_id}
                        size={200}
                        backgroundColor={COLORS.TEXT_WHITE}
                        color={COLORS.BACKGROUND_DARK}
                      />
                      <Text style={styles.barcodeText}>{formattedTicketId}</Text>
                    </View>

                    <Text style={styles.ticketNote}>
                      Please present this e-ticket at the event venue
                    </Text>
                  </View>
                </View>
              </ScrollView>
            </View>
          );
        }}
        keyExtractor={(ticket) => ticket.ticket_id}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: SPACING.MEDIUM,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  shareButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ticketPagination: {
    paddingHorizontal: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    alignItems: 'center',
  },
  paginationText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.SMALL,
    marginBottom: SPACING.SMALL,
  },
  paginationButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: SPACING.SMALL,
  },
  paginationButton: {
    width: 28,
    height: 28,
    borderRadius: RADIUS.ROUND,
    backgroundColor: COLORS.BACKGROUND_CARD,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activePaginationButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  paginationButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.XSMALL,
  },
  activePaginationButtonText: {
    color: COLORS.TEXT_WHITE,
    fontWeight: 'bold',
  },
  content: {
    width: width,
    height: '100%',
  },
  scrollableTicketContainer: {
    paddingVertical: SPACING.MEDIUM,
    alignItems: 'center',
    paddingBottom: SPACING.XLARGE,
  },
  ticket: {
    width: TICKET_WIDTH,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    shadowColor: COLORS.BACKGROUND_DARK,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_BORDER,
  },
  ticketHeader: {
    padding: SPACING.MEDIUM,
  },
  eventImage: {
    width: '100%',
    height: 150,
    borderRadius: RADIUS.SMALL,
    marginBottom: SPACING.MEDIUM,
  },
  eventInfo: {
    gap: 8,
  },
  eventTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  sessionName: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
  eventDate: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
  },
  eventLocation: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
  },
  ticketBody: {
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND_CARD,
    position: 'relative',
    borderTopWidth: 1,
    borderTopColor: COLORS.PRIMARY_TRANSPARENT,
  },
  leftCircle: {
    position: 'absolute',
    left: -10,
    top: '50%',
    width: 20,
    height: 20,
    backgroundColor: COLORS.BACKGROUND_DARK,
    borderRadius: RADIUS.ROUND,
    transform: [{ translateY: -10 }],
  },
  rightCircle: {
    position: 'absolute',
    right: -10,
    top: '50%',
    width: 20,
    height: 20,
    backgroundColor: COLORS.BACKGROUND_DARK,
    borderRadius: RADIUS.ROUND,
    transform: [{ translateY: -10 }],
  },
  ticketDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.LARGE,
  },
  detailItem: {
    gap: SPACING.XSMALL,
  },
  detailLabel: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
  },
  detailValue: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_WHITE,
  },
  statusBadge: {
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
  },
  statusText: {
    fontSize: FONT_SIZES.SMALL,
  },
  barcodeContainer: {
    backgroundColor: COLORS.TEXT_WHITE,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.SMALL,
    alignItems: 'center',
    gap: SPACING.SMALL,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  barcodeText: {
    color: COLORS.BACKGROUND_DARK,
    fontSize: FONT_SIZES.XSMALL,
  },
  ticketNote: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginTop: SPACING.MEDIUM,
  },
  dashedLineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 2,
    height: 2,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  dash: {
    width: 8,
    height: 2,
    backgroundColor: COLORS.BACKGROUND_CARD,
    marginHorizontal: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TicketScreen;