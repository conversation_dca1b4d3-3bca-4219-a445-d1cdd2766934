import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  // Image,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  Alert,
  StatusBar,
  Dimensions,
  Platform
} from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context'; // 移除导入
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { orderService } from '../services/orderService';
import { COLORS, FONT_SIZES, RADIUS, SPACING, GRADIENTS } from '../config/theme';
import { useAuth } from '../context/AuthContext';
import Button from '../components/Button';
import { OrdersScreenProps } from '../types/navigation';
import { LinearGradient } from 'expo-linear-gradient';
import LoadingAnimation from '../components/LoadingAnimation';
import FastImage from 'react-native-fast-image';
import SupportModal from '../components/SupportModal';

// 获取屏幕宽度
const { width } = Dimensions.get('window');

// 格式化时间函数
const formatTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// 格式化日期函数
const formatDate = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr);
  return date.toLocaleDateString([], { month: 'numeric', day: 'numeric', year: 'numeric' });
};

// 每页加载的订单数量
const PAGE_SIZE = 10;

export default function OrdersScreen() {
  const navigation = useNavigation<OrdersScreenProps['navigation']>();
  const { session } = useAuth();
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [isSupportModalVisible, setSupportModalVisible] = useState(false);

  // 加载订单数据
  const loadOrders = useCallback(async (pageNum = 0, refresh = false) => {
    try {
      if (!session?.user) {
        setLoading(false);
        return;
      }

      if (refresh) {
        setOrders([]);
        setPage(0);
        pageNum = 0;
        setHasMore(true);
      }

      if (pageNum === 0) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // 获取用户订单 - 使用分页API
      const { data: ordersData, hasMore: moreOrders, totalCount: total } = 
        await orderService.getUserOrdersPaginated(session.user.id, pageNum, PAGE_SIZE);
      
      setTotalCount(total);
      setHasMore(moreOrders);
      
      if (refresh || pageNum === 0) {
        setOrders(ordersData);
      } else {
        setOrders(prev => [...prev, ...ordersData]);
      }
      
      setPage(pageNum);
    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('Error', 'Failed to load order data');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [session]);

  // 首次加载和刷新
  useEffect(() => {
    if (session) {
      loadOrders(0);
    } else {
      setLoading(false);
    }
  }, [session, loadOrders]);

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadOrders(0, true);
    } finally {
      setRefreshing(false);
    }
  }, [loadOrders]);

  // 加载更多
  const loadMoreOrders = () => {
    if (!loadingMore && hasMore) {
      loadOrders(page + 1);
    }
  };

  // 重新设计的登录提示组件
  const renderLoginPrompt = () => (
    <LinearGradient
      colors={['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)']} // 直接使用数组字面量
      style={styles.loginPromptContainer} // 使用与 ProfileScreen 类似的样式
    >
      <View style={styles.loginContent}> 
        <Ionicons 
          name="receipt-outline" // 使用订单相关图标
          size={120} // 增大图标尺寸
          color={COLORS.PRIMARY_LIGHT} // 使用浅一点的主题色
          style={styles.loginIcon} 
        />
        <Text style={styles.loginTitle}>View Your Orders</Text>
        <Text style={styles.loginSubtitle}>Sign in to see your order history and access your event tickets.</Text>
        <Button
          title="Login / Register"
          style={styles.loginButton}
          onPress={() => navigation.navigate('SignIn', { 
            redirectScreen: 'Home',
            redirectParams: { screen: 'Orders' }
          })}
        />
      </View>
    </LinearGradient>
  );

  // 渲染订单项
  const renderOrderItem = ({ item }: { item: any }) => {
    return (
      <TouchableOpacity 
        style={styles.orderCard}
        activeOpacity={0.8}
        onPress={() => navigation.navigate('Ticket', { 
          eventId: item.event_sessions.event.event_id,
          orderId: item.order_id
        })}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderHeaderLeft}>
            <Ionicons name="receipt-outline" size={16} color={COLORS.PRIMARY} />
            <Text style={styles.orderNumber}>#{item.order_id.substring(0, 8)}</Text>
          </View>
          <View style={styles.orderHeaderRight}>
            <Ionicons name="time-outline" size={14} color="#999" style={styles.dateIcon} />
            <Text style={styles.orderDate}>{formatDate(item.created_at)}</Text>
          </View>
        </View>
        
        <View style={styles.orderContent}>
          <FastImage
            source={{
              uri: item.event_sessions.event.image,
              priority: FastImage.priority.normal,
              cache: FastImage.cacheControl.immutable
            }}
            style={styles.eventImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          
          <View style={styles.orderDetails}>
            <Text style={styles.eventTitle} numberOfLines={2}>{item.event_sessions.event.title}</Text>
            <Text style={styles.sessionTitle} numberOfLines={1}>{item.event_sessions.title}</Text>
            
            <View style={styles.sessionInfo}>
              <View style={styles.infoRow}>
                <Ionicons name="calendar-outline" size={14} color={COLORS.PRIMARY} />
                <Text style={styles.infoText}>{formatDate(item.event_sessions.start_time)}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="time-outline" size={14} color={COLORS.PRIMARY} />
                <Text style={styles.infoText}>{formatTime(item.event_sessions.start_time)}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="ticket-outline" size={14} color={COLORS.PRIMARY} />
                <Text style={styles.infoText}>{item.quantity} ticket(s)</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.orderStatus}>
            <Text style={styles.totalAmount}>${item.total_amount}</Text>
            <View style={styles.paidBadge}>
              <Text style={styles.paidText}>Paid</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.orderFooter}>
          <TouchableOpacity 
            style={styles.viewTicketButton}
            onPress={() => navigation.navigate('Ticket', { 
              eventId: item.event_sessions.event.event_id,
              orderId: item.order_id
            })}
          >
            <Text style={styles.viewTicketText}>View Ticket</Text>
            <Ionicons name="chevron-forward" size={16} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  // 渲染列表底部
  const renderFooter = () => {
    if (!hasMore) return (
      <View style={styles.endTextContainer}>
        <View style={styles.endLine} />
        <Text style={styles.endText}>No more orders</Text>
        <View style={styles.endLine} />
      </View>
    );
    
    return (
      <View style={styles.loadMoreContainer}>
        {loadingMore ? (
          <View style={styles.loadingMore}>
            <LoadingAnimation size="small" text="Loading more..." />
          </View>
        ) : (
          <TouchableOpacity 
            style={styles.loadMoreButton}
            onPress={loadMoreOrders}
            activeOpacity={0.7}
          >
            <Text style={styles.loadMoreText}>Load More Orders</Text>
            <Ionicons name="chevron-down" size={16} color={COLORS.PRIMARY} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 渲染空订单状态
  const renderEmptyComponent = () => {
    if (loading) return null;
    
    return (
      <View style={styles.emptyContainer}>
        <LinearGradient
          colors={['rgba(50, 50, 50, 0.3)', 'rgba(30, 30, 30, 0.5)']}
          style={[styles.emptyGradient, { borderRadius: RADIUS.LARGE }]}
        >
          <Ionicons name="receipt-outline" size={80} color={COLORS.PRIMARY} style={styles.emptyIcon} />
          <Text style={styles.emptyText}>No Orders Yet</Text>
          <Text style={styles.emptySubtext}>Your order history will appear here</Text>
          <Button
            title="Browse Events"
            onPress={() => navigation.navigate('HomeTab')}
            style={styles.browseButton}
          />
        </LinearGradient>
      </View>
    );
  };

  // 如果用户未登录，显示登录提示
  if (!session) {
    return (
      // <SafeAreaView style={styles.safeArea}> // 替换为 View
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.container}>
          {renderLoginPrompt()}
        </View>
      {/* </SafeAreaView> */}
      </View>
    );
  }

  return (
    // <SafeAreaView style={styles.safeArea}> // 替换为 View
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerTitleContainer}>
            <Text style={styles.headerTitle}>
              MY <Text style={styles.highlightText}>ORDERS</Text>
            </Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.supportButton}
              onPress={() => setSupportModalVisible(true)}
            >
              <Ionicons name="help-circle-outline" size={22} color={COLORS.TEXT_WHITE} />
            </TouchableOpacity>
          </View>
          <Text style={styles.headerSubtitle}>Manage all your event tickets</Text>
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingAnimation text="Loading orders..." />
          </View>
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.order_id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={COLORS.PRIMARY}
                colors={[COLORS.PRIMARY]}
              />
            }
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmptyComponent}
            showsVerticalScrollIndicator={false}
          />
        )}

        <SupportModal 
          isVisible={isSupportModalVisible}
          onClose={() => setSupportModalVisible(false)}
        />
      </View>
    {/* </SafeAreaView> */}
    </View>
  );
}

const styles = StyleSheet.create({
  // safeArea: { // 移除 safeArea 样式
  //   flex: 1,
  //   backgroundColor: COLORS.BACKGROUND_DARK,
  // },
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
    // 可能需要根据移除 SafeAreaView 后的效果添加 paddingTop
    // paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 20,
  },
  header: {
    // padding: SPACING.MEDIUM,
    backgroundColor: 'transparent',
    paddingTop: 60,
    padding: SPACING.LARGE,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: FONT_SIZES.SMALL,
    color: '#999',
    marginTop: 4,
  },
  listContainer: {
    padding: SPACING.SMALL,
    paddingBottom: SPACING.XLARGE,
  },
  orderCard: {
    backgroundColor: '#111',
    borderRadius: RADIUS.LARGE,
    marginVertical: SPACING.MEDIUM,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    backgroundColor: 'rgba(30, 30, 30, 0.6)',
  },
  orderHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 4,
  },
  orderNumber: {
    color: COLORS.PRIMARY,
    fontWeight: '600',
    fontSize: FONT_SIZES.SMALL,
    marginLeft: 6,
  },
  orderDate: {
    color: '#999',
    fontSize: FONT_SIZES.XSMALL,
  },
  orderContent: {
    flexDirection: 'row',
    padding: SPACING.MEDIUM,
  },
  eventImage: {
    width: 80,
    height: 80,
    borderRadius: RADIUS.MEDIUM,
    marginRight: SPACING.MEDIUM,
  },
  orderDetails: {
    flex: 1,
    marginRight: SPACING.SMALL,
  },
  eventTitle: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  sessionTitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: FONT_SIZES.SMALL,
    marginBottom: SPACING.SMALL,
  },
  sessionInfo: {
    marginTop: SPACING.SMALL,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoText: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: FONT_SIZES.SMALL,
    marginLeft: 8,
  },
  orderStatus: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  totalAmount: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: FONT_SIZES.LARGE,
    marginBottom: 8,
  },
  paidBadge: {
    backgroundColor: 'rgba(46, 204, 113, 0.2)',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: RADIUS.MEDIUM,
    marginTop: 'auto',
    borderWidth: 1,
    borderColor: 'rgba(46, 204, 113, 0.3)',
  },
  paidText: {
    color: '#2ecc71',
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
  },
  orderFooter: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.05)',
    padding: SPACING.SMALL,
    alignItems: 'flex-end',
  },
  viewTicketButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  viewTicketText: {
    color: COLORS.PRIMARY,
    fontWeight: '600',
    fontSize: FONT_SIZES.SMALL,
    marginRight: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#999',
    marginTop: SPACING.MEDIUM,
    fontSize: FONT_SIZES.SMALL,
  },
  loadingMore: {
    padding: SPACING.MEDIUM,
    alignItems: 'center',
  },
  endTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.MEDIUM,
    paddingHorizontal: SPACING.LARGE,
  },
  endLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  endText: {
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.4)',
    paddingHorizontal: SPACING.MEDIUM,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '500',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.LARGE,
    minHeight: 400,
  },
  emptyGradient: {
    width: width * 0.85,
    padding: SPACING.XLARGE,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  emptyIcon: {
    marginBottom: SPACING.MEDIUM,
    opacity: 0.8,
  },
  emptyText: {
    color: '#fff',
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    marginTop: SPACING.SMALL,
    letterSpacing: 0.5,
  },
  emptySubtext: {
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    marginTop: SPACING.SMALL,
    marginBottom: SPACING.LARGE,
    fontSize: FONT_SIZES.MEDIUM,
  },
  browseButton: {
    width: '80%',
    marginTop: SPACING.MEDIUM,
  },
  loginPromptContainer: {
    flex: 1,
    justifyContent: 'center', // 垂直居中
    alignItems: 'center', // 水平居中
    padding: SPACING.LARGE,
    // backgroundColor: 'transparent', // 背景由 LinearGradient 提供
  },
  loginContent: {
    alignItems: 'center', // 内容居中对齐
    width: '90%', // 限制内容宽度
    // backgroundColor: 'rgba(0, 0, 0, 0.2)', // 可选：添加轻微背景增加对比度
    // padding: SPACING.LARGE,
    // borderRadius: RADIUS.MEDIUM,
  },
  loginIcon: {
    marginBottom: SPACING.LARGE, // 图标下方间距
    opacity: 0.9,
  },
  loginTitle: {
    fontSize: FONT_SIZES.XXLARGE, // 增大标题字号
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    textAlign: 'center',
    marginBottom: SPACING.MEDIUM, // 标题下方间距
    letterSpacing: 0.5,
  },
  loginSubtitle: {
    fontSize: FONT_SIZES.MEDIUM, // 调整副标题字号
    color: COLORS.TEXT_LIGHT, // 使用浅色文本
    textAlign: 'center',
    lineHeight: FONT_SIZES.MEDIUM * 1.5, // 调整行高
    marginBottom: SPACING.XLARGE, // 副标题下方间距
  },
  loginButton: {
    width: '100%', // 按钮宽度
  },
  highlightText: {
    color: COLORS.PRIMARY,
    fontWeight: '800',
  },
  loadMoreContainer: {
    padding: SPACING.MEDIUM,
    paddingBottom: SPACING.XLARGE,
  },
  loadMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(30, 30, 30, 0.8)',
    borderRadius: RADIUS.MEDIUM,
    marginVertical: SPACING.SMALL,
    paddingVertical: SPACING.MEDIUM,
    paddingHorizontal: SPACING.LARGE,
    borderWidth: 1,
    borderColor: 'rgba(231, 76, 38, 0.5)',
  },
  loadMoreText: {
    color: COLORS.PRIMARY,
    fontWeight: '600',
    fontSize: FONT_SIZES.MEDIUM,
    marginRight: SPACING.SMALL,
  },
  headerActions: {
    position: 'absolute',
    top: 60 + SPACING.MEDIUM, // 与 header 的 paddingTop 对齐
    right: SPACING.LARGE,
  },
  supportButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.PRIMARY,
    width: 80,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  supportButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
    marginLeft: SPACING.XSMALL,
  },
}); 