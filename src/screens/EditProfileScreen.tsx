import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import Button from '../components/Button';
import BackButton from '../components/BackButton';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/navigation';
import LoadingAnimation from '../components/LoadingAnimation';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';

type EditProfileScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList>;
};

export default function EditProfileScreen({ navigation }: EditProfileScreenProps) {
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [displayName, setDisplayName] = useState('');
  const [nameError, setNameError] = useState('');

  useEffect(() => {
    loadUserProfile();
  }, []);

  const handleNameChange = (text: string) => {
    setDisplayName(text);
    if (text.trim()) {
      setNameError('');
    }
  };

  const handleChangePassword = () => {
    navigation.navigate('ChangePassword');
  };

  const handleAccountDeletion = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone and will permanently remove all your data.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Continue to Support',
          style: 'destructive',
          onPress: () => {
            navigation.navigate('WebView', { 
              url: 'https://support.inthepond.com.au/info/lezigo/account-deletion',
              title: 'Account Deletion'
            });
          },
        },
      ]
    );
  };

  const loadUserProfile = async () => {
    try {
      setInitialLoading(true);
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigation.navigate('SignIn', {});
        return;
      }

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('display_name')
        .eq('user_id', user.id)
        .single();

      if (profileError) throw profileError;
      if (profile?.display_name) {
        setDisplayName(profile.display_name);
      }
    } catch (error: any) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!displayName.trim()) {
      setNameError('Display name cannot be empty');
      Alert.alert('Error', 'Display name cannot be empty');
      return;
    }

    try {
      setLoading(true);

      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigation.navigate('SignIn', {});
        return;
      }

      const { error } = await supabase
        .from('user_profiles')
        .update({
          display_name: displayName.trim(),
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (error) throw error;

      Alert.alert('Success', 'Profile updated successfully');
      navigation.goBack();
    } catch (error: any) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LoadingAnimation text="Loading profile data..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <View style={styles.header}>
        <BackButton />
        <Text style={styles.headerTitle}>Account Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.form}>
            <Text style={styles.label}>Display Name <Text style={styles.required}>*</Text></Text>
            <TextInput
              style={[styles.input, nameError ? styles.inputError : null]}
              value={displayName}
              onChangeText={handleNameChange}
              placeholder="Enter your display name"
              placeholderTextColor="#666"
              maxLength={30}
            />
            {nameError ? (
              <Text style={styles.errorText}>{nameError}</Text>
            ) : (
              <Text style={styles.hint}>
                This is the name that will be visible to other users (required)
              </Text>
            )}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Account Settings</Text>
            
            <TouchableOpacity 
              style={styles.settingButton} 
              onPress={handleChangePassword}
            >
              <Ionicons name="key-outline" size={24} color={COLORS.PRIMARY} />
              <Text style={styles.settingText}>Change Password</Text>
              <Ionicons name="chevron-forward" size={20} color={COLORS.TEXT_GRAY} />
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.settingButton, styles.dangerButton]} 
              onPress={handleAccountDeletion}
            >
              <Ionicons name="trash-outline" size={24} color={COLORS.ERROR} />
              <Text style={[styles.settingText, styles.dangerText]}>Delete Account</Text>
              <Ionicons name="chevron-forward" size={20} color={COLORS.ERROR} />
            </TouchableOpacity>
          </View>

          <Button
            title="Save Changes"
            onPress={handleSubmit}
            loading={loading}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.LARGE,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: SPACING.LARGE,
  },
  
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  form: {
    padding: SPACING.LARGE,
  },
  label: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    fontWeight: '600',
  },
  input: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: SPACING.SMALL,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  hint: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.LARGE,
  },
  submitButton: {
    marginHorizontal: SPACING.LARGE,
    marginTop: 'auto',
    marginBottom: Platform.OS === 'ios' ? 40 : SPACING.LARGE,
  },
  required: {
    color: COLORS.ERROR,
    fontWeight: 'bold',
  },
  inputError: {
    borderColor: COLORS.ERROR,
    borderWidth: 1,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: FONT_SIZES.SMALL,
    marginBottom: SPACING.LARGE,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    padding: SPACING.LARGE,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.LARGE,
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
  },
  settingText: {
    flex: 1,
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
    marginLeft: SPACING.MEDIUM,
  },
  dangerButton: {
    borderColor: COLORS.ERROR + '40',
    backgroundColor: 'transparent',
  },
  dangerText: {
    color: COLORS.ERROR,
  },
}); 