import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  Alert,
  ActivityIndicator,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { ticketService } from '../../services/ticketService';
import { supabase } from '../../lib/supabase';
import { CameraView, BarcodeScanningResult, CameraType, useCameraPermissions } from 'expo-camera';

type TicketValidationScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'TicketValidation'
>;

export default function TicketValidationScreen() {
  const navigation = useNavigation<TicketValidationScreenNavigationProp>();
  const [ticketId, setTicketId] = useState('');
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    message: string;
    ticketData?: any;
  } | null>(null);
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
  
  // 更新相机相关状态
  const [permission, requestPermission] = useCameraPermissions();
  const [scanning, setScanning] = useState(false);
  const [scanned, setScanned] = useState(false);
  const [flashOn, setFlashOn] = useState(false);
  const [facing, setFacing] = useState<CameraType>('back');
  const cameraRef = useRef<CameraView>(null);

  const handleValidationError = (error: any) => {
    console.error('Ticket validation failed:', error);
    
    // Provide more specific error messages based on error type
    let errorMessage = 'Error during validation, please try again';
    
    if (error.message) {
      if (error.message.includes('network')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (error.message.includes('permission')) {
        errorMessage = 'Permission denied. You may not have access to validate this ticket.';
      }
    }
    
    setValidationResult({
      valid: false,
      message: errorMessage
    });
  };

  const handleBarCodeScanned = async (scanResult: BarcodeScanningResult) => {
    if (scanned || validating) return;
    
    setScanned(true);
    setScanning(false);
    
    // 从扫描的二维码获取票据ID
    const scannedTicketId = scanResult.data;
    if (scannedTicketId) {
      setTicketId(scannedTicketId);
      // 自动触发验证
      await validateWithId(scannedTicketId);
    } else {
      setValidationResult({
        valid: false,
        message: 'Invalid QR code. Please try again.',
      });
    }
  };

  const startScanning = () => {
    setScanned(false);
    setValidationResult(null);
    setScanning(true);
  };

  const stopScanning = () => {
    setScanning(false);
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };

  const validateWithId = async (id: string) => {
    if (!id.trim()) {
      setValidationResult({
        valid: false,
        message: 'Please enter a ticket ID',
      });
      return;
    }

    setValidating(true);

    try {
      // 获取当前用户
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        throw new Error('Please log in to validate tickets');
      }

      console.log('Current user:', user.id);

      // 格式化票据ID
      const formattedTicketId = await formatTicketId(id);
      if (!ticketService.validateTicket(formattedTicketId)) {
        throw new Error('Invalid ticket ID format');
      }

      console.log('Formatted ticket ID:', formattedTicketId);

      // 获取票据验证所需的最小信息集
      const ticketInfo = await ticketService.getTicketValidationInfo(formattedTicketId);
      if (!ticketInfo) {
        throw new Error('Ticket not found');
      }

      console.log('Ticket info:', JSON.stringify(ticketInfo, null, 2));

      // 检查当前用户是否为活动组织者
      if (!ticketInfo.organizerUserId || ticketInfo.organizerUserId !== user.id) {
        setValidationResult({
          valid: false,
          message: 'You are not the organizer of this event and cannot validate its tickets',
          ticketData: {
            ticket: ticketInfo.ticket,
            eventTitle: ticketInfo.eventTitle
          }
        });
        return;
      }

      // 检查票据状态
      if (ticketInfo.ticket.status === 'used') {
        setValidationResult({
          valid: false,
          message: 'This ticket has already been used',
          ticketData: {
            ticket: ticketInfo.ticket,
            eventTitle: ticketInfo.eventTitle,
            checkInTime: ticketInfo.ticket.check_in_time
              ? new Date(ticketInfo.ticket.check_in_time).toLocaleString()
              : undefined
          }
        });
        return;
      }

      if (ticketInfo.ticket.status === 'cancelled') {
        setValidationResult({
          valid: false,
          message: 'This ticket has been cancelled',
          ticketData: {
            ticket: ticketInfo.ticket,
            eventTitle: ticketInfo.eventTitle
          }
        });
        return;
      }

      // 更新票据状态
      const currentTime = new Date().toISOString();
      const { error: updateError } = await supabase
        .from('tickets')
        .update({
          status: 'used',
          check_in_time: currentTime
        })
        .eq('ticket_id', ticketInfo.ticket.ticket_id);

      if (updateError) {
        throw updateError;
      }

      // --- 新增的验证步骤 ---
      // 再次从数据库读取票据信息以确认更新是否真的成功
      const { data: verifiedTicket, error: verificationError } = await supabase
        .from('tickets')
        .select('status, check_in_time')
        .eq('ticket_id', ticketInfo.ticket.ticket_id)
        .single();

      if (verificationError) {
        console.error('Error verifying ticket update:', verificationError);
        // 可以抛出错误，让全局错误处理捕获，或者直接设置特定的验证结果
        setValidationResult({
          valid: false,
          message: 'Failed to verify ticket status after update. Please check your connection and try again.',
          ticketData: {
            ticket: ticketInfo.ticket,
            eventTitle: ticketInfo.eventTitle
          }
        });
        return;
      }

      if (!verifiedTicket || verifiedTicket.status !== 'used') {
        console.warn('Ticket status mismatch after update attempt. DB status:', verifiedTicket?.status);
        setValidationResult({
          valid: false,
          message: 'Verification failed. Ticket status could not be confirmed as used. Please try again or check manually.',
          ticketData: {
            ticket: ticketInfo.ticket, // 显示原始票据信息
            eventTitle: ticketInfo.eventTitle
          }
        });
        return;
      }
      // --- 验证步骤结束 ---

      // 使用更新后的状态 (实际上是刚从数据库验证过的状态)
      const updatedTicketData = {
        ...ticketInfo.ticket,
        status: verifiedTicket.status as 'used', // 确保类型正确
        check_in_time: verifiedTicket.check_in_time 
      };

      // 验证成功
      setValidationResult({
        valid: true,
        message: 'Ticket validated successfully, check-in completed',
        ticketData: {
          ticket: updatedTicketData,
          eventTitle: ticketInfo.eventTitle,
          checkInTime: verifiedTicket.check_in_time ? new Date(verifiedTicket.check_in_time).toLocaleString() : undefined
        }
      });
      
      // 重置输入以准备下一次验证
      setTicketId('');
    } catch (error) {
      console.error('Validation error:', error);
      handleValidationError(error);
    } finally {
      setValidating(false);
    }
  };

  const validateTicket = () => validateWithId(ticketId);

  const formatTicketId = async (input: string): Promise<string> => {
    const parsedId = await ticketService.parseTicketId(input);
    if (parsedId) {
      return parsedId;
    }
    
    // If parsing fails, return the original input
    return input;
  };

  // 渲染扫描界面
  const renderScanner = () => {
    if (!permission) {
      return (
        <View style={styles.cameraContainer}>
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }
    
    if (!permission.granted) {
      return (
        <View style={styles.cameraContainer}>
          <Text style={styles.permissionText}>No access to camera</Text>
          <TouchableOpacity 
            style={styles.permissionButton}
            onPress={requestPermission}
          >
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ["qr"],
          }}
          facing={facing}
          enableTorch={flashOn}
        />
        
        <View style={styles.scanOverlay}>
          <View style={styles.scanMarker} />
        </View>
        
        <View style={styles.cameraControls}>
          <TouchableOpacity 
            style={styles.cameraButton}
            onPress={toggleFlash}
          >
            <Ionicons 
              name={flashOn ? "flash" : "flash-off"} 
              size={24} 
              color={COLORS.TEXT_WHITE} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.cameraButton}
            onPress={() => setFacing(current => (current === 'back' ? 'front' : 'back'))}
          >
            <Ionicons 
              name="camera-reverse" 
              size={24} 
              color={COLORS.TEXT_WHITE} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.cameraButton}
            onPress={stopScanning}
          >
            <Text style={styles.cameraButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ticket Validation</Text>
        <View style={styles.placeholder} />
      </View>
      
      {scanning ? (
        renderScanner()
      ) : (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <KeyboardAvoidingView 
            style={styles.content}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          >
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Validate Ticket</Text>
              <Text style={styles.inputDescription}>
                Scan the QR code on the ticket or enter the ticket ID manually.
              </Text>
              
              <View style={styles.validationOptions}>
                <TouchableOpacity 
                  style={styles.scanButton}
                  onPress={startScanning}
                >
                  <Ionicons name="qr-code" size={24} color={COLORS.TEXT_WHITE} />
                  <Text style={styles.scanButtonText}>Scan QR Code</Text>
                </TouchableOpacity>
                
                <Text style={styles.orText}>OR</Text>
                
                <View style={styles.searchBar}>
                  <TextInput
                    style={styles.input}
                    value={ticketId}
                    onChangeText={setTicketId}
                    placeholder="Enter ticket ID, e.g.: T123ABC456DEF"
                    placeholderTextColor={COLORS.TEXT_GRAY}
                    autoCapitalize="characters"
                    autoCorrect={false}
                    numberOfLines={1}
                    multiline={false}
                  />
                  <TouchableOpacity
                    style={styles.searchButton}
                    onPress={validateTicket}
                    disabled={validating}
                  >
                    {validating ? (
                      <ActivityIndicator size="small" color={COLORS.TEXT_WHITE} />
                    ) : (
                      <Ionicons name="search" size={24} color={COLORS.TEXT_WHITE} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {validationResult && (
              <View style={[
                styles.resultContainer,
                validationResult.valid ? styles.validResult : styles.invalidResult
              ]}>
                <View style={styles.resultHeader}>
                  <Ionicons 
                    name={validationResult.valid ? "checkmark-circle" : "close-circle"} 
                    size={40} 
                    color={validationResult.valid ? COLORS.SUCCESS : COLORS.ERROR} 
                  />
                  <Text style={styles.resultTitle}>
                    {validationResult.valid ? 'Validation Successful' : 'Validation Failed'}
                  </Text>
                </View>
                
                <Text style={styles.resultMessage}>{validationResult.message}</Text>
                
                {validationResult.ticketData && (
                  <View style={styles.ticketDetails}>
                    {validationResult.ticketData.eventTitle && (
                      <View style={styles.detailItem}>
                        <Text style={styles.detailLabel}>Event Name</Text>
                        <Text style={styles.detailValue}>
                          {validationResult.ticketData.eventTitle}
                        </Text>
                      </View>
                    )}
                    
                    {validationResult.ticketData.checkInTime && (
                      <View style={styles.detailItem}>
                        <Text style={styles.detailLabel}>Check-in Time</Text>
                        <Text style={styles.detailValue}>
                          {validationResult.ticketData.checkInTime}
                        </Text>
                      </View>
                    )}
                    
                    {validationResult.ticketData.ticket && (
                      <View style={styles.detailItem}>
                        <Text style={styles.detailLabel}>Ticket ID</Text>
                        <Text style={styles.detailValue}>
                          {ticketService.formatTicketId(validationResult.ticketData.ticket.ticket_id)}
                        </Text>
                      </View>
                    )}
                  </View>
                )}
              </View>
            )}
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      )}
    </SafeAreaView>
  );
}

const { width } = Dimensions.get('window');
const SCAN_MARKER_SIZE = width * 0.7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.MEDIUM,
  },
  inputContainer: {
    marginBottom: SPACING.LARGE,
  },
  inputLabel: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
  },
  inputDescription: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.MEDIUM,
  },
  validationOptions: {
    width: '100%',
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
  },
  scanButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    marginLeft: SPACING.SMALL,
  },
  orText: {
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginVertical: SPACING.SMALL,
  },
  searchBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY + '30',
    marginTop: SPACING.SMALL,
  },
  input: {
    flex: 1,
    color: COLORS.TEXT_WHITE,
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
  },
  searchButton: {
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.MEDIUM,
    justifyContent: 'center',
    alignItems: 'center',
    width: 60,
  },
  resultContainer: {
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    marginVertical: SPACING.MEDIUM,
  },
  validResult: {
    backgroundColor: COLORS.SUCCESS + '20',
    borderWidth: 1,
    borderColor: COLORS.SUCCESS + '40',
  },
  invalidResult: {
    backgroundColor: COLORS.ERROR + '20',
    borderWidth: 1,
    borderColor: COLORS.ERROR + '40',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.MEDIUM,
  },
  resultTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginLeft: SPACING.SMALL,
  },
  resultMessage: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
    paddingHorizontal: SPACING.MEDIUM,
  },
  ticketDetails: {
    backgroundColor: COLORS.BACKGROUND_CARD + '80',
    borderRadius: RADIUS.SMALL,
    padding: SPACING.MEDIUM,
  },
  detailItem: {
    marginBottom: SPACING.SMALL,
  },
  detailLabel: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.XSMALL,
  },
  detailValue: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
  },
  cameraContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  camera: {
    width: '100%',
    height: '100%',
  },
  scanOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  scanMarker: {
    width: SCAN_MARKER_SIZE,
    height: SCAN_MARKER_SIZE,
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    backgroundColor: 'transparent',
    borderRadius: RADIUS.SMALL,
  },
  cameraControls: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: SPACING.MEDIUM,
  },
  cameraButton: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  cameraButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
  },
  permissionText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    marginBottom: SPACING.MEDIUM,
  },
  permissionButton: {
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
  },
  permissionButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },
});