import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Alert,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  FlatList,
  Switch,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { supabase } from '../../lib/supabase';
import { eventService } from '../../services/eventService';
import BackButton from '../../components/BackButton';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';

type EventFormScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'EventForm'
>;

type EventFormScreenRouteProp = RouteProp<
  RootStackParamList,
  'EventForm'
>;

// Session 类型定义
type Session = {
  id?: string;
  title: string;
  start_time: string;
  price: number | null;
  capacity: number;
  max_tickets_per_user?: number;
  status: 'available' | 'pause';
  sale_start_time?: string;
  sale_end_time?: string;
}

// 预设的标签选项
const AVAILABLE_TAGS = [
  'Free Events', 
  'Indoor', 
  'Outdoor', 
  'Performance', 
  'Social', 
  'Sports'
];

// 最大可选标签数
const MAX_TAGS = 3;

export default function EventFormScreen() {
  const navigation = useNavigation<EventFormScreenNavigationProp>();
  const route = useRoute<EventFormScreenRouteProp>();
  const { event } = route.params || {};
  
  const [formData, setFormData] = useState({
    title: '',
    location: '',
    full_address: '',
    description: '',
  });
  const [isPreview, setIsPreview] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  
  // Session 相关状态
  const [sessions, setSessions] = useState<Session[]>([]);
  
  // 图片相关状态
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [descriptionImages, setDescriptionImages] = useState<string[]>([]);
  const [uploadingDescriptionImage, setUploadingDescriptionImage] = useState(false);

  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title || '',
        location: event.location || '',
        full_address: event.full_address || '',
        description: event.description || '',
      });
      
      if (Array.isArray(event.tags)) {
        setSelectedTags(event.tags.filter((tag: string) => AVAILABLE_TAGS.includes(tag)));
      }
      
      if (event.image) {
        setImageUri(event.image);
      }

      if (event.description_images) {
        setDescriptionImages(event.description_images);
      }

      if (event.is_preview !== undefined) {
        setIsPreview(event.is_preview);
      }
      
      loadEventSessions();
    }
  }, [event]);

  const loadEventSessions = async () => {
    if (!event || !event.event_id) return;
    
    try {
      const { data, error } = await supabase
        .from('event_sessions')
        .select('*')
        .eq('event_id', event.event_id)
        .order('start_time', { ascending: true });
        
      if (error) throw error;
      
      const typedSessions = data?.map((session: any): Session => ({
        id: session.id,
        title: session.title,
        start_time: session.start_time,
        price: session.price,
        capacity: session.capacity,
        status: session.status,
        max_tickets_per_user: session.max_tickets_per_user,
        sale_start_time: session.sale_start_time,
        sale_end_time: session.sale_end_time,
      })) || [];
      
      setSessions(typedSessions);
    } catch (error) {
      console.error('Failed to load event sessions:', error);
      Alert.alert('Error', 'Failed to load event sessions.');
    }
  };

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      if (selectedTags.length < MAX_TAGS) {
        setSelectedTags([...selectedTags, tag]);
      } else {
        Alert.alert('Warning', `You can only select up to ${MAX_TAGS} tags.`);
      }
    }
  };

  const handleAddSession = () => {
    navigation.navigate('SessionForm', {
      isEditing: false,
      onSave: (newSession: Session) => {
        setSessions([...sessions, newSession]);
      }
    });
  };

  const handleEditSession = (session: Session, index: number) => {
    navigation.navigate('SessionForm', {
      session,
      isEditing: true,
      onSave: (updatedSession: Session) => {
        setSessions(sessions.map((s, i) => 
          i === index ? updatedSession : s
        ));
      }
    });
  };

  const toggleSessionStatus = async (session: Session, index: number) => {
    const newStatus: 'available' | 'pause' = session.status === 'available' ? 'pause' : 'available';
    const updatedSessions = sessions.map((s, i) => 
      i === index ? { ...s, status: newStatus } : s
    );
    setSessions(updatedSessions);

    if (session.id) {
        try {
            const { error } = await supabase
              .from('event_sessions')
              .update({ status: newStatus })
              .eq('id', session.id);
      
            if (error) {
                // Revert state on failure
                setSessions(sessions);
                throw error;
            };
            Alert.alert('Status Updated', `Session "${session.title}" is now ${newStatus}.`);
          } catch (error) {
            console.error('Failed to update session status:', error);
            Alert.alert('Error', 'Failed to update session status. Please try again.');
          }
    }
  };

  const handleSelectImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions to upload an image');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const { fileSize } = result.assets[0];
        const MAX_FILE_SIZE = 6 * 1024 * 1024; // 6MB
        
        if (fileSize && fileSize > MAX_FILE_SIZE) {
          Alert.alert('Image too large', 'The selected image exceeds the 6MB size limit.');
          return;
        }
        
        setImageUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const uploadImage = async (): Promise<string | null> => {
    if (!imageUri) return null;

    try {
      setUploadingImage(true);

      const response = await fetch(imageUri);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = async () => {
          try {
            if (typeof reader.result === 'string') {
              const base64Data = reader.result.split(',')[1];
              const fileName = `event_${new Date().getTime()}.jpg`;

              const { error } = await supabase
                .storage
                .from('event_images')
                .upload(fileName, decode(base64Data), {
                  contentType: 'image/jpeg',
                  upsert: true
                });

              if (error) throw error;

              const { data: { publicUrl } } = supabase
                .storage
                .from('event_images')
                .getPublicUrl(fileName);

              resolve(publicUrl);
            } else {
              reject(new Error('Failed to convert image to base64'));
            }
          } catch (uploadError) {
            console.error('Error uploading image:', uploadError);
            reject(uploadError);
          }
        };
        reader.onerror = () => reject(new Error('Failed to read image'));
      });
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Error', 'Failed to upload image');
      return null;
    } finally {
      setUploadingImage(false);
    }
  };

  const handleSelectDescriptionImage = async () => {
    if (descriptionImages.length >= 3) {
      Alert.alert('Limit Reached', 'You can only add up to 3 description images.');
      return;
    }

    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions to upload an image');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // 不要求裁切，支持长图
        quality: 0.7, // 压缩质量
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const { fileSize } = result.assets[0];
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB限制

        if (fileSize && fileSize > MAX_FILE_SIZE) {
          Alert.alert('Image too large', 'The selected image exceeds the 5MB size limit.');
          return;
        }

        const uploadedUrl = await uploadDescriptionImage(result.assets[0].uri);
        if (uploadedUrl) {
          setDescriptionImages([...descriptionImages, uploadedUrl]);
        }
      }
    } catch (error) {
      console.error('Error selecting description image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const uploadDescriptionImage = async (imageUri: string): Promise<string | null> => {
    try {
      setUploadingDescriptionImage(true);

      const response = await fetch(imageUri);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = async () => {
          try {
            if (typeof reader.result === 'string') {
              const base64Data = reader.result.split(',')[1];
              const { data: { user } } = await supabase.auth.getUser();
              const userId = user?.id || 'anonymous';
              const fileName = `${userId}/event_desc_${new Date().getTime()}_${Math.random().toString(36).substring(2, 11)}.jpg`;

              const { error } = await supabase
                .storage
                .from('event-description-images')
                .upload(fileName, decode(base64Data), {
                  contentType: 'image/jpeg',
                  upsert: true
                });

              if (error) throw error;

              const { data: { publicUrl } } = supabase
                .storage
                .from('event-description-images')
                .getPublicUrl(fileName);

              resolve(publicUrl);
            } else {
              reject(new Error('Failed to convert image to base64'));
            }
          } catch (uploadError) {
            console.error('Error uploading description image:', uploadError);
            reject(uploadError);
          }
        };
        reader.onerror = () => reject(new Error('Failed to read image'));
      });
    } catch (error) {
      console.error('Error processing description image:', error);
      Alert.alert('Error', 'Failed to upload description image');
      return null;
    } finally {
      setUploadingDescriptionImage(false);
    }
  };

  const removeDescriptionImage = (index: number) => {
    const newImages = [...descriptionImages];
    newImages.splice(index, 1);
    setDescriptionImages(newImages);
  };

  const handleSubmit = async () => {
    if (!formData.title || !formData.location || !formData.description || !formData.full_address) {
      Alert.alert('Error', 'Title, location, full address and description are required.');
      return;
    }
    if (!imageUri) {
      Alert.alert('Error', 'Event image is required.');
      return;
    }
    if (!isPreview && sessions.length === 0) {
      Alert.alert('Error', 'At least one session is required for non-preview events.');
      return;
    }

    setSubmitting(true);

    try {
      let imageUrl = event?.image || '';
      if (imageUri && (imageUri !== event?.image)) {
        const uploadedUrl = await uploadImage();
        if (uploadedUrl) {
          imageUrl = uploadedUrl;
        }
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        navigation.navigate('Home', { screen: 'HomeTab' });
        return;
      }

      const { data: organizerData } = await supabase
        .from('organizer_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (!organizerData) {
        Alert.alert('Error', 'Organizer profile not found.');
        return;
      }

      if (event) {
        await eventService.updateEvent(event.event_id, {
          ...formData,
          tags: selectedTags,
          image: imageUrl,
          description_images: descriptionImages,
          is_preview: isPreview,
        });

        const { data: existingSessions } = await supabase
          .from('event_sessions')
          .select('id')
          .eq('event_id', event.event_id);

        const existingSessionIds = new Set(existingSessions?.map(s => s.id) || []);
        const sessionsToUpdate = sessions.filter(s => s.id && existingSessionIds.has(s.id));
        const sessionsToCreate = sessions.filter(s => !s.id);

        for (const session of sessionsToUpdate) {
          const { id, ...updateData } = session;
          await supabase.from('event_sessions').update(updateData).eq('id', id);
        }

        if (sessionsToCreate.length > 0) {
          await supabase.from('event_sessions').insert(
            sessionsToCreate.map(({ id, ...s }) => ({
              ...s,
              event_id: event.event_id,
              status: s.status || 'available',
            }))
          );
        }

        Alert.alert('Success', 'Event updated successfully.', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      } else {
        await eventService.createEvent({
          ...formData,
          tags: selectedTags,
          image: imageUrl,
          description_images: descriptionImages,
          is_featured: false,
          is_verified: false,
          is_preview: isPreview,
          organizer_id: organizerData.organizer_id,
        }, sessions, organizerData);
        Alert.alert('Success', 'Event created successfully.', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Failed to save event:', error);
      Alert.alert('Error', 'Unable to save event. Please try again later.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderTagSelector = () => (
    <View style={styles.tagsContainer}>
      {AVAILABLE_TAGS.map((tag) => (
        <TouchableOpacity
          key={tag}
          style={[styles.tagButton, selectedTags.includes(tag) && styles.tagButtonSelected]}
          onPress={() => toggleTag(tag)}
        >
          <Text style={[styles.tagButtonText, selectedTags.includes(tag) && styles.tagButtonTextSelected]}>
            {tag}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
  
  const renderSessionItem = ({ item, index }: { item: Session, index: number }) => {
    const isAvailable = item.status === 'available';
    const isUnsaved = !item.id; // Sessions without ID are newly created and unsaved

    return (
      <View style={styles.sessionItem}>
        <View style={styles.sessionHeader}>
          <Text style={styles.sessionTitle} numberOfLines={1}>{item.title}</Text>
          <TouchableOpacity
            style={[styles.editButton, isUnsaved && styles.unsavedButton]}
            onPress={() => handleEditSession(item, index)}
          >
            <Ionicons
              name="pencil"
              size={16}
              color={isUnsaved ? COLORS.TEXT_WHITE : COLORS.TEXT_LIGHT}
            />
            <Text style={[styles.editButtonText, isUnsaved && styles.unsavedButtonText]}>
              {isUnsaved ? 'Unsaved' : 'Edit'}
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.sessionBody}>
          <View style={styles.sessionDetailRow}>
            <Ionicons name="calendar-outline" size={14} color={COLORS.TEXT_GRAY} />
            <Text style={styles.sessionDetailText}>{new Date(item.start_time).toLocaleString([], { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</Text>
          </View>
          <View style={styles.sessionDetailRow}>
            <Ionicons name="ticket-outline" size={14} color={COLORS.TEXT_GRAY} />
            <Text style={styles.sessionDetailText}>
              {item.price ? `${item.price}` : 'Free'} | Capacity: {item.capacity}
            </Text>
          </View>
        </View>
        <View style={styles.sessionFooter}>
          <TouchableOpacity 
            style={[styles.statusButton, isAvailable ? styles.pauseButton : styles.playButton]} 
            onPress={() => toggleSessionStatus(item, index)}
          >
            <Ionicons name={isAvailable ? "pause" : "play"} size={16} color={COLORS.TEXT_WHITE} />
            <Text style={styles.statusButtonText}>{isAvailable ? 'Pause Sales' : 'Resume Sales'}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.BACKGROUND_DARK} />
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={styles.header}>
          <BackButton style={styles.backButton} />
          <Text style={styles.headerTitle}>{event ? 'Edit Event' : 'Create Event'}</Text>
          <View style={styles.placeholder} />
        </View>
        
        <ScrollView 
          style={styles.content}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.formContainer}>
              <Text style={styles.sectionTitle}>Event Details</Text>
              
              <Text style={styles.inputLabel}>Event Title *</Text>
              <TextInput 
                style={[styles.input, focusedInput === 'title' && styles.inputFocused]} 
                value={formData.title} 
                onChangeText={(text) => setFormData({...formData, title: text})} 
                placeholder="e.g., Summer Music Festival" 
                placeholderTextColor={COLORS.TEXT_GRAY}
                onFocus={() => setFocusedInput('title')}
                onBlur={() => setFocusedInput(null)}
              />
              
              <Text style={styles.inputLabel}>Location (Display Address) *</Text>
              <TextInput 
                style={[styles.input, focusedInput === 'location' && styles.inputFocused]} 
                value={formData.location} 
                onChangeText={(text) => setFormData({...formData, location: text})} 
                placeholder="e.g., Central Park" 
                placeholderTextColor={COLORS.TEXT_GRAY}
                onFocus={() => setFocusedInput('location')}
                onBlur={() => setFocusedInput(null)}
              />
              
              <Text style={styles.inputLabel}>Full Address (For Navigation) *</Text>
              <TextInput 
                style={[styles.input, styles.textArea, focusedInput === 'full_address' && styles.inputFocused]} 
                value={formData.full_address} 
                onChangeText={(text) => setFormData({...formData, full_address: text})} 
                placeholder="e.g., 123 Main St, New York, NY 10001" 
                placeholderTextColor={COLORS.TEXT_GRAY} 
                multiline 
                onFocus={() => setFocusedInput('full_address')}
                onBlur={() => setFocusedInput(null)}
              />
              
              <Text style={styles.inputLabel}>Event Image *</Text>
              <TouchableOpacity style={styles.imageUploadContainer} onPress={handleSelectImage} disabled={uploadingImage}>
                {imageUri ? (
                  <Image source={{ uri: imageUri }} style={styles.previewImage} resizeMode="cover" />
                ) : (
                  <View style={styles.imagePlaceholder}>
                    <Ionicons name="image-outline" size={48} color={COLORS.TEXT_GRAY} />
                    <Text style={styles.imagePlaceholderText}>Tap to select image</Text>
                    <Text style={styles.imagePlaceholderSubText}>1:1 Ratio Recommended</Text>
                  </View>
                )}
                {uploadingImage && (
                  <View style={styles.uploadingOverlay}>
                    <ActivityIndicator size="large" color={COLORS.PRIMARY} />
                  </View>
                )}
              </TouchableOpacity>
              
              <Text style={styles.inputLabel}>Tags (Select up to {MAX_TAGS})</Text>
              {renderTagSelector()}
              
              <Text style={styles.inputLabel}>Description *</Text>
              <TextInput
                style={[styles.input, styles.textArea, {minHeight: 120}, focusedInput === 'description' && styles.inputFocused]}
                value={formData.description}
                onChangeText={(text) => setFormData({...formData, description: text})}
                placeholder="Tell everyone about your event..."
                placeholderTextColor={COLORS.TEXT_GRAY}
                multiline
                onFocus={() => setFocusedInput('description')}
                onBlur={() => setFocusedInput(null)}
              />

              <Text style={[styles.inputLabel, { marginTop: SPACING.MEDIUM }]}>
                Description Images (Optional, max 3)
              </Text>
              <View style={styles.descriptionImagesContainer}>
                {descriptionImages.map((imageUrl, index) => (
                  <View key={index} style={styles.descriptionImageItem}>
                    <Image
                      source={{ uri: imageUrl }}
                      style={styles.descriptionImageThumbnail}
                      resizeMode="cover"
                    />
                    <TouchableOpacity
                      style={styles.removeImageButton}
                      onPress={() => removeDescriptionImage(index)}
                    >
                      <Ionicons name="close-circle" size={20} color={COLORS.ERROR} />
                    </TouchableOpacity>
                  </View>
                ))}
                {descriptionImages.length < 3 && (
                  <TouchableOpacity
                    style={styles.addDescriptionImageButton}
                    onPress={handleSelectDescriptionImage}
                    disabled={uploadingDescriptionImage}
                  >
                    {uploadingDescriptionImage ? (
                      <ActivityIndicator size="small" color={COLORS.PRIMARY} />
                    ) : (
                      <>
                        <Ionicons name="add" size={24} color={COLORS.TEXT_GRAY} />
                        <Text style={styles.addImageText}>Add Image</Text>
                      </>
                    )}
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.divider} />

              <Text style={styles.sectionTitle}>Event Status</Text>
              <View style={styles.previewSwitchContainer}>
                <View style={styles.previewSwitchInfo}>
                  <Text style={styles.previewSwitchLabel}>Coming Soon Mode</Text>
                  <Text style={styles.previewSwitchHelper}>When enabled, a "Coming Soon" notice will be displayed on the event details page.</Text>
                </View>
                <Switch value={isPreview} onValueChange={setIsPreview} trackColor={{ false: COLORS.BACKGROUND_CARD, true: COLORS.PRIMARY_TRANSPARENT }} thumbColor={isPreview ? COLORS.PRIMARY : COLORS.TEXT_GRAY} />
              </View>

              <View style={styles.divider} />
              
              <Text style={styles.sectionTitle}>Event Sessions</Text>
              {isPreview && (
                <Text style={styles.previewSessionNote}>Sessions are optional for "Coming Soon" events.</Text>
              )}
              
              {sessions.length > 0 && (
                <FlatList
                  data={sessions}
                  renderItem={renderSessionItem}
                  keyExtractor={(item, index) => item.id || `session-${index}`}
                  scrollEnabled={false}
                />
              )}
              
              <TouchableOpacity style={styles.addSessionButton} onPress={handleAddSession}>
                <Ionicons name="add" size={22} color={COLORS.PRIMARY} />
                <Text style={styles.addSessionButtonText}>Add Session</Text>
              </TouchableOpacity>
            
              <TouchableOpacity style={[styles.submitButton, submitting && styles.disabledButton]} onPress={handleSubmit} disabled={submitting}>
                {submitting ? <ActivityIndicator color={COLORS.TEXT_WHITE} /> : <Text style={styles.submitButtonText}>{event ? 'Update Event' : 'Create Event'}</Text>}
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    paddingTop: Platform.OS === 'ios' ? SPACING.SMALL : StatusBar.currentHeight,
    paddingBottom: SPACING.SMALL,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  content: {
    flex: 1,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 44,
  },
  formContainer: {
    paddingHorizontal: SPACING.MEDIUM,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginTop: SPACING.XLARGE,
    marginBottom: SPACING.MEDIUM,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  inputLabel: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
    marginBottom: SPACING.SMALL,
    marginTop: SPACING.MEDIUM,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  input: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    paddingHorizontal: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    height: 52,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
  },
  inputFocused: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 1.5,
  },
  textArea: {
    minHeight: 80,
    height: 'auto',
    textAlignVertical: 'top',
    paddingTop: SPACING.MEDIUM,
  },
  submitButton: {
    backgroundColor: COLORS.PRIMARY,
    height: 52,
    borderRadius: RADIUS.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.XLARGE,
  },
  disabledButton: {
    opacity: 0.7,
  },
  submitButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.SMALL,
    marginBottom: SPACING.MEDIUM,
  },
  tagButton: {
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.SMALL,
    borderRadius: RADIUS.ROUND,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  tagButtonSelected: {
    backgroundColor: COLORS.PRIMARY_TRANSPARENT,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  tagButtonText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONT_SIZES.SMALL,
  },
  tagButtonTextSelected: {
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
  },
  sessionItem: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    overflow: 'hidden',
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.TRANSPARENT_LIGHT,
  },
  sessionTitle: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    flex: 1,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_DARK,
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.SMALL,
    borderRadius: RADIUS.ROUND,
    gap: SPACING.SMALL,
  },
  editButtonText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
  },
  unsavedButton: {
    backgroundColor: COLORS.ERROR,
  },
  unsavedButtonText: {
    color: COLORS.TEXT_WHITE,
  },
  sessionBody: {
    padding: SPACING.MEDIUM,
    gap: SPACING.MEDIUM,
  },
  sessionDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.SMALL,
  },
  sessionDetailText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    flex: 1,
  },
  sessionFooter: {
    paddingHorizontal: SPACING.MEDIUM,
    paddingBottom: SPACING.MEDIUM,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.SMALL,
    borderRadius: RADIUS.MEDIUM,
    gap: SPACING.SMALL,
  },
  pauseButton: {
    backgroundColor: COLORS.WARNING,
  },
  playButton: {
    backgroundColor: COLORS.SUCCESS,
  },
  statusButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },
  imageUploadContainer: {
    width: '100%',
    aspectRatio: 1,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.BORDER_COLOR,
    borderStyle: 'dashed',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholderText: {
    color: COLORS.TEXT_GRAY,
    marginTop: SPACING.SMALL,
    fontSize: FONT_SIZES.MEDIUM,
  },
  imagePlaceholderSubText: {
    color: COLORS.TEXT_GRAY,
    marginTop: SPACING.XSMALL,
    fontSize: FONT_SIZES.XSMALL,
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addSessionButton: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    height: 48,
    borderRadius: RADIUS.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.SMALL,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
    borderStyle: 'dashed',
  },
  addSessionButtonText: {
    color: COLORS.PRIMARY,
    fontWeight: 'bold',
    marginLeft: SPACING.SMALL,
    fontSize: FONT_SIZES.MEDIUM,
  },
  previewSwitchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_CARD,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
  },
  previewSwitchInfo: {
    flex: 1,
    marginRight: SPACING.MEDIUM,
  },
  previewSwitchLabel: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    marginBottom: SPACING.XSMALL,
  },
  previewSwitchHelper: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    lineHeight: 18,
  },
  previewSessionNote: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.SMALL,
    fontStyle: 'italic',
    marginBottom: SPACING.MEDIUM,
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.PRIMARY_TRANSPARENT,
    borderRadius: RADIUS.MEDIUM,
    textAlign: 'center',
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.BORDER_COLOR,
    marginVertical: SPACING.LARGE,
  },
  descriptionImagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.SMALL,
    marginTop: SPACING.SMALL,
  },
  descriptionImageItem: {
    position: 'relative',
    width: 80,
    height: 80,
  },
  descriptionImageThumbnail: {
    width: 80,
    height: 80,
    borderRadius: RADIUS.SMALL,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: COLORS.TEXT_WHITE,
    borderRadius: 10,
  },
  addDescriptionImageButton: {
    width: 80,
    height: 80,
    borderRadius: RADIUS.SMALL,
    borderWidth: 2,
    borderColor: COLORS.TEXT_GRAY,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  addImageText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    marginTop: 4,
  },
});