import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { orderService } from '../../services/orderService';

type SessionAttendeesScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SessionAttendees'
>;

type SessionAttendeesScreenRouteProp = RouteProp<
  RootStackParamList,
  'SessionAttendees'
>;

type CheckInStatus = 'checked_in' | 'not_checked_in' | 'partial';

type TicketInfo = {
  ticket_id: string;
  status: 'valid' | 'used' | 'cancelled';
  check_in_time: string | null;
};

type Attendee = {
  order_id: string;
  quantity: number;
  is_paid: boolean;
  user_profiles: {
    user_id?: string;
    display_name: string;
    email: string;
  };
};

type AttendeeWithCheckIn = {
  order_id: string;
  quantity: number;
  is_paid: boolean;
  user_profiles: {
    user_id?: string;
    display_name: string;
    email: string;
  };
  tickets: TicketInfo[];
  check_in_status: CheckInStatus;
  checked_in_count: number;
  total_tickets: number;
  latest_check_in_time?: string;
};

export default function SessionAttendeesScreen() {
  const navigation = useNavigation<SessionAttendeesScreenNavigationProp>();
  const route = useRoute<SessionAttendeesScreenRouteProp>();
  const { sessionId, sessionTitle } = route.params;

  const [attendees, setAttendees] = useState<AttendeeWithCheckIn[]>([]);
  const [filteredAttendees, setFilteredAttendees] = useState<AttendeeWithCheckIn[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'all' | CheckInStatus>('all');
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  useEffect(() => {
    loadAttendees();
  }, [sessionId]);

  const loadAttendees = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const data = await orderService.getSessionAttendeesWithCheckIn(sessionId);
      setAttendees(data);

      // 应用当前筛选条件
      if (filterStatus === 'all') {
        setFilteredAttendees(data);
      } else {
        setFilteredAttendees(data.filter(attendee => attendee.check_in_status === filterStatus));
      }
    } catch (error) {
      console.error('Failed to load attendees:', error);
      Alert.alert('Error', 'Unable to load attendee data. Please try again later.');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  const onRefresh = () => {
    loadAttendees(true);
  };

  // 筛选功能
  const filterAttendees = (status: 'all' | CheckInStatus) => {
    setFilterStatus(status);
    if (status === 'all') {
      setFilteredAttendees(attendees);
    } else {
      setFilteredAttendees(attendees.filter(attendee => attendee.check_in_status === status));
    }
  };

  // 计算统计数据
  const getStatistics = () => {
    const totalAttendees = attendees.length;
    const checkedInAttendees = attendees.filter(a => a.check_in_status === 'checked_in').length;
    const partialAttendees = attendees.filter(a => a.check_in_status === 'partial').length;
    const notCheckedInAttendees = attendees.filter(a => a.check_in_status === 'not_checked_in').length;

    const totalTickets = attendees.reduce((sum, attendee) => sum + attendee.total_tickets, 0);
    const checkedInTickets = attendees.reduce((sum, attendee) => sum + attendee.checked_in_count, 0);

    const checkInRate = totalAttendees > 0 ? Math.round((checkedInAttendees / totalAttendees) * 100) : 0;

    return {
      totalAttendees,
      checkedInAttendees,
      partialAttendees,
      notCheckedInAttendees,
      totalTickets,
      checkedInTickets,
      checkInRate
    };
  };

  // 获取签到状态的颜色和图标
  const getCheckInStatusInfo = (status: CheckInStatus) => {
    switch (status) {
      case 'checked_in':
        return {
          color: COLORS.SUCCESS,
          icon: 'checkmark-circle' as const,
          label: 'Checked In',
          bgColor: COLORS.SUCCESS + '20'
        };
      case 'partial':
        return {
          color: COLORS.WARNING,
          icon: 'warning' as const,
          label: 'Partial',
          bgColor: COLORS.WARNING + '20'
        };
      case 'not_checked_in':
        return {
          color: COLORS.ERROR,
          icon: 'close-circle' as const,
          label: 'Not Checked In',
          bgColor: COLORS.ERROR + '20'
        };
    }
  };

  const renderAttendeeItem = ({ item }: { item: AttendeeWithCheckIn }) => {
    const statusInfo = getCheckInStatusInfo(item.check_in_status);

    return (
      <View style={[styles.attendeeCard, { borderLeftColor: statusInfo.color }]}>
        <View style={styles.attendeeMainInfo}>
          <View style={styles.attendeeHeader}>
            <Ionicons name={statusInfo.icon} size={20} color={statusInfo.color} />
            <Text style={styles.attendeeName}>{item.user_profiles.display_name}</Text>
          </View>
          <Text style={styles.attendeeEmail}>{item.user_profiles.email}</Text>
          {item.latest_check_in_time && (
            <Text style={styles.checkInTime}>
              Check-in: {new Date(item.latest_check_in_time).toLocaleString('en-US')}
            </Text>
          )}
        </View>
        <View style={styles.attendeeRightInfo}>
          <View style={styles.ticketInfo}>
            <Text style={styles.ticketCount}>
              Tickets: {item.checked_in_count}/{item.total_tickets}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}>
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Check-in Status</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          disabled={refreshing}
        >
          <Ionicons
            name="refresh"
            size={24}
            color={refreshing ? COLORS.TEXT_GRAY : COLORS.TEXT_WHITE}
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.sessionInfo}>
        <Text style={styles.sessionTitle}>{sessionTitle}</Text>
        {!loading && (
          <>
            {/* 统计面板 */}
            <View style={styles.statisticsPanel}>
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Ionicons name="people" size={20} color={COLORS.PRIMARY} />
                </View>
                <Text style={styles.statNumber}>{getStatistics().checkedInAttendees}/{getStatistics().totalAttendees}</Text>
                <Text style={styles.statLabel}>Attendees</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Ionicons name="ticket" size={20} color={COLORS.PRIMARY} />
                </View>
                <Text style={styles.statNumber}>{getStatistics().checkedInTickets}/{getStatistics().totalTickets}</Text>
                <Text style={styles.statLabel}>Tickets</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <View style={styles.statIconContainer}>
                  <Ionicons name="analytics" size={20} color={COLORS.PRIMARY} />
                </View>
                <Text style={styles.statNumber}>{getStatistics().checkInRate}%</Text>
                <Text style={styles.statLabel}>Check-in Rate</Text>
              </View>
            </View>

            {/* 筛选标签 */}
            <View style={styles.filterContainer}>
              <TouchableOpacity
                style={[styles.filterButton, filterStatus === 'all' && styles.filterButtonActive]}
                onPress={() => filterAttendees('all')}
              >
                <Text style={[styles.filterText, filterStatus === 'all' && styles.filterTextActive]}>
                  All ({getStatistics().totalAttendees})
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterButton, filterStatus === 'checked_in' && styles.filterButtonActive]}
                onPress={() => filterAttendees('checked_in')}
              >
                <Text style={[styles.filterText, filterStatus === 'checked_in' && styles.filterTextActive]}>
                  Checked In ({getStatistics().checkedInAttendees})
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterButton, filterStatus === 'partial' && styles.filterButtonActive]}
                onPress={() => filterAttendees('partial')}
              >
                <Text style={[styles.filterText, filterStatus === 'partial' && styles.filterTextActive]}>
                  Partial ({getStatistics().partialAttendees})
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.filterButton, filterStatus === 'not_checked_in' && styles.filterButtonActive]}
                onPress={() => filterAttendees('not_checked_in')}
              >
                <Text style={[styles.filterText, filterStatus === 'not_checked_in' && styles.filterTextActive]}>
                  Not Checked In ({getStatistics().notCheckedInAttendees})
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.PRIMARY} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : filteredAttendees.length > 0 ? (
        <FlatList
          data={filteredAttendees}
          renderItem={renderAttendeeItem}
          keyExtractor={(item) => item.order_id}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[COLORS.PRIMARY]}
              tintColor={COLORS.PRIMARY}
            />
          }
        />
      ) : attendees.length > 0 ? (
        <View style={styles.emptyContainer}>
          <View style={styles.emptyIconContainer}>
            <Ionicons name="filter-outline" size={48} color={COLORS.TEXT_GRAY} />
          </View>
          <Text style={styles.emptyTitle}>No Attendees Found</Text>
          <Text style={styles.emptySubtitle}>No attendees match the current filter</Text>
          <TouchableOpacity
            style={styles.resetFilterButton}
            onPress={() => filterAttendees('all')}
          >
            <Text style={styles.resetFilterText}>Show All</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <View style={styles.emptyIconContainer}>
            <Ionicons name="people-outline" size={48} color={COLORS.TEXT_GRAY} />
          </View>
          <Text style={styles.emptyTitle}>No Attendees Yet</Text>
          <Text style={styles.emptySubtitle}>No one has registered for this event yet</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  refreshButton: {
    padding: SPACING.SMALL,
  },
  sessionInfo: {
    padding: SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BACKGROUND_CARD,
  },
  statisticsPanel: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginTop: SPACING.MEDIUM,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.PRIMARY + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.XSMALL,
  },
  statNumber: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: SPACING.XSMALL,
  },
  statLabel: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: COLORS.TEXT_GRAY + '30',
  },
  filterContainer: {
    flexDirection: 'row',
    marginTop: SPACING.MEDIUM,
    gap: SPACING.SMALL,
  },
  filterButton: {
    flex: 1,
    paddingVertical: SPACING.SMALL,
    paddingHorizontal: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: COLORS.PRIMARY,
  },
  filterText: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
  },
  filterTextActive: {
    color: COLORS.TEXT_WHITE,
    fontWeight: 'bold',
  },
  sessionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.XSMALL,
  },
  attendeeCount: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
  },
  listContent: {
    padding: SPACING.MEDIUM,
  },
  attendeeCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  attendeeMainInfo: {
    flex: 1,
    marginRight: SPACING.MEDIUM,
  },
  attendeeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.XSMALL,
  },
  attendeeName: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginLeft: SPACING.SMALL,
    flex: 1,
  },
  attendeeEmail: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.XSMALL,
  },
  checkInTime: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.SUCCESS,
    fontStyle: 'italic',
  },
  attendeeRightInfo: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    minHeight: 60,
  },
  ticketInfo: {
    backgroundColor: COLORS.PRIMARY + '20',
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
    marginBottom: SPACING.SMALL,
  },
  ticketCount: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_WHITE,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: FONT_SIZES.XSMALL,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: SPACING.MEDIUM,
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_GRAY,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.LARGE,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.MEDIUM,
  },
  emptyTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginBottom: SPACING.LARGE,
  },
  resetFilterButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LARGE,
    paddingVertical: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
  },
  resetFilterText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },
}); 