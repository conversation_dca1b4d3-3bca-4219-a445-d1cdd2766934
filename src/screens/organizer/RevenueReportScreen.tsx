import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { supabase } from '../../lib/supabase';
import { eventService } from '../../services/eventService';
import LoadingAnimation from '../../components/LoadingAnimation';

type RevenueReportScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'RevenueReport'
>;

type OrganizerData = {
  revenue: number;
  revenue_paid: number;
  events: EventRevenueData[];
};

type EventRevenueData = {
  event_id: string;
  title: string;
  revenue: number;
  sessions: SessionRevenueData[];
};

type SessionRevenueData = {
  id: string;
  title: string;
  revenue: number;
  price: number;
  soldTickets: number;
};

export default function RevenueReportScreen() {
  const navigation = useNavigation<RevenueReportScreenNavigationProp>();
  const [orgData, setOrgData] = useState<OrganizerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedEvents, setExpandedEvents] = useState<{[key: string]: boolean}>({});
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  useEffect(() => {
    loadOrganizerData();
  }, []);

  const loadOrganizerData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigation.navigate('Home', { screen: 'HomeTab' });
        return;
      }

      // Get organizer profile with revenue
      const { data: organizerData, error: organizerError } = await supabase
        .from('organizer_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (organizerError) throw organizerError;

      // Get events with revenue
      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select('*')
        .eq('organizer_id', organizerData.organizer_id)
        .order('created_at', { ascending: false });
      
      if (eventsError) throw eventsError;

      // Get sessions with revenue for each event
      const eventsWithRevenue: EventRevenueData[] = [];

      for (const event of eventsData) {
        const { data: sessionsData, error: sessionsError } = await supabase
          .from('event_sessions')
          .select('*')
          .eq('event_id', event.event_id);
        
        if (sessionsError) throw sessionsError;

        // Get sold tickets for each session
        const sessionsWithRevenue: SessionRevenueData[] = [];
        
        for (const session of sessionsData) {
          const sessionSoldTickets = await eventService.getSessionSoldTickets(session.id);
          
          sessionsWithRevenue.push({
            id: session.id,
            title: session.title,
            revenue: session.revenue || 0,
            price: session.price || 0,
            soldTickets: sessionSoldTickets
          });
        }
        
        eventsWithRevenue.push({
          event_id: event.event_id,
          title: event.title,
          revenue: event.revenue || 0,
          sessions: sessionsWithRevenue
        });
      }

      setOrgData({
        revenue: organizerData.revenue || 0,
        revenue_paid: organizerData.revenue_paid || 0,
        events: eventsWithRevenue
      });
    } catch (error) {
      console.error('Failed to load revenue data:', error);
      Alert.alert('Error', 'Unable to load revenue data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const toggleEventExpand = (eventId: string) => {
    setExpandedEvents(prev => ({
      ...prev,
      [eventId]: !prev[eventId]
    }));
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Revenue Report</Text>
        <View style={styles.placeholder} />
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <LoadingAnimation />
        </View>
      ) : (
        <ScrollView style={styles.content}>
          <View style={styles.totalRevenueCard}>
            <Text style={styles.totalRevenueLabel}>Total Revenue</Text>
            <Text style={styles.totalRevenueValue}>
              {formatCurrency(orgData?.revenue || 0)}
            </Text>
            
            <View style={styles.paymentContainer}>
              <View style={styles.paymentItem}>
                <Text style={styles.paymentLabel}>Paid</Text>
                <Text style={styles.paymentValue}>{formatCurrency(orgData?.revenue_paid || 0)}</Text>
              </View>
              <View style={styles.paymentDivider} />
              <View style={styles.paymentItem}>
                <Text style={styles.paymentLabel}>Pending</Text>
                <Text style={styles.paymentValue}>{formatCurrency((orgData?.revenue || 0) - (orgData?.revenue_paid || 0))}</Text>
              </View>
            </View>
            
            <View style={styles.totalRevenueDetails}>
              <Text style={styles.totalRevenueDetailsText}>
                {orgData?.events.length || 0} Events
              </Text>
              <Text style={styles.totalRevenueDetailsText}>
                {orgData?.events.reduce((total, event) => total + event.sessions.length, 0) || 0} Sessions
              </Text>
            </View>
          </View>

          <Text style={styles.sectionTitle}>Events Revenue</Text>
          
          {orgData?.events.length === 0 ? (
            <Text style={styles.noEventsText}>No events found</Text>
          ) : (
            orgData?.events.map((event) => (
              <View key={event.event_id} style={styles.eventCard}>
                <TouchableOpacity 
                  style={styles.eventHeader}
                  onPress={() => toggleEventExpand(event.event_id)}
                >
                  <View style={styles.eventTitleContainer}>
                    <Text style={styles.eventTitle}>{event.title}</Text>
                    <Text style={styles.eventRevenue}>{formatCurrency(event.revenue)}</Text>
                  </View>
                  <Ionicons 
                    name={expandedEvents[event.event_id] ? "chevron-up" : "chevron-down"} 
                    size={24} 
                    color={COLORS.TEXT_GRAY} 
                  />
                </TouchableOpacity>

                {expandedEvents[event.event_id] && (
                  <View style={styles.sessionsContainer}>
                    <Text style={styles.sessionsTitle}>Sessions</Text>
                    {event.sessions.map((session) => (
                      <View key={session.id} style={styles.sessionItem}>
                        <View style={styles.sessionInfo}>
                          <Text style={styles.sessionTitle}>{session.title}</Text>
                          <Text style={styles.sessionDetails}>
                            Price: {formatCurrency(session.price)} • 
                            Sold: {session.soldTickets}
                          </Text>
                        </View>
                        <View style={styles.sessionRevenue}>
                          <Text style={styles.sessionRevenueValue}>
                            {formatCurrency(session.revenue)}
                          </Text>
                        </View>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            ))
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.MEDIUM,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalRevenueCard: {
    backgroundColor: COLORS.PRIMARY,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.LARGE,
    marginBottom: SPACING.LARGE,
    alignItems: 'center',
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
  totalRevenueLabel: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
    opacity: 0.8,
  },
  totalRevenueValue: {
    fontSize: 40,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  paymentContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: SPACING.MEDIUM,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: RADIUS.SMALL,
    padding: SPACING.MEDIUM,
  },
  paymentItem: {
    flex: 1,
    alignItems: 'center',
  },
  paymentLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_WHITE,
    opacity: 0.8,
    marginBottom: 4,
  },
  paymentValue: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  paymentDivider: {
    width: 1,
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: SPACING.SMALL,
  },
  totalRevenueDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_WHITE + '30',
    paddingTop: SPACING.MEDIUM,
  },
  totalRevenueDetailsText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  noEventsText: {
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginTop: SPACING.LARGE,
  },
  eventCard: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PRIMARY,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTitleContainer: {
    flex: 1,
  },
  eventTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.XSMALL,
  },
  eventRevenue: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
  sessionsContainer: {
    marginTop: SPACING.MEDIUM,
    paddingTop: SPACING.MEDIUM,
    borderTopWidth: 1,
    borderTopColor: COLORS.PRIMARY_TRANSPARENT,
  },
  sessionsTitle: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.SMALL,
  },
  sessionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.SMALL,
    marginBottom: SPACING.SMALL,
    backgroundColor: COLORS.BACKGROUND_DARK + '50',
    borderRadius: RADIUS.SMALL,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionTitle: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '500',
    color: COLORS.TEXT_WHITE,
    marginBottom: 2,
  },
  sessionDetails: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
  },
  sessionRevenue: {
    backgroundColor: COLORS.BACKGROUND_DARK,
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: 4,
    borderRadius: RADIUS.SMALL,
  },
  sessionRevenueValue: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
    color: COLORS.SUCCESS,
  },
}); 