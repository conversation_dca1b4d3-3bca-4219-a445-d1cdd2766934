import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Alert,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { supabase } from '../../lib/supabase';
import LoadingAnimation from '../../components/LoadingAnimation';
import { eventService } from '../../services/eventService';

type ManageEventsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'OrganizerManagement'
>;

type Event = {
  event_id: string;
  title: string;
  location: string;
  full_address: string;
  image: string;
  is_featured: boolean;
  is_verified: boolean;
  is_archived: boolean;
  tags: string[];
  description: string;
  created_at: string;
  updated_at: string;
  organizer_id: string;
  sessions: any[];
};

export default function ManageEventsScreen() {
  const navigation = useNavigation<ManageEventsScreenNavigationProp>();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  // 使用useFocusEffect而不是useEffect，确保每次屏幕获得焦点时都刷新数据
  useFocusEffect(
    React.useCallback(() => {
      loadOrganizerEvents();
    }, [])
  );

  const loadOrganizerEvents = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigation.navigate('Home', { screen: 'HomeTab' });
        return;
      }

      // Get organizer events
      const organizerEvents = await eventService.getOrganizerEvents(user.id);
      setEvents(organizerEvents || []);
    } catch (error) {
      console.error('Failed to load organizer events:', error);
      Alert.alert('Error', 'Unable to load event data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEvent = () => {
    navigation.navigate('EventForm', {});
  };

  const handleEditEvent = (event: Event) => {
    navigation.navigate('EventForm', { event });
  };


  const renderEventCard = ({ item }: { item: Event }) => (
    <View style={styles.eventCard}>
      <View style={styles.eventHeader}>
        <Text style={styles.eventTitle} numberOfLines={1} ellipsizeMode="tail">
          {item.title}
        </Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleEditEvent(item)}
          >
            <Ionicons name="pencil" size={16} color={COLORS.PRIMARY} />
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.eventDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="location-outline" size={16} color={COLORS.TEXT_GRAY} />
          <Text style={styles.detailText}>{item.location}</Text>
        </View>

        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, item.is_verified ? styles.verifiedBadge : styles.unverifiedBadge]}>
            <Ionicons
              name={item.is_verified ? "checkmark-circle" : "time"}
              size={12}
              color={COLORS.TEXT_WHITE}
            />
            <Text style={styles.statusText}>
              {item.is_verified ? 'Verified' : 'Pending Verification'}
            </Text>
          </View>
        </View>

        {item.tags && item.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {Array.isArray(item.tags) && item.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        )}

        <Text style={styles.descriptionText} numberOfLines={2}>
          {item.description}
        </Text>

        <Text style={styles.dateText}>
          Created: {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Manage Events</Text>
        <View style={styles.placeholder} />
      </View>
      
      <View style={styles.content}>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={handleAddEvent}
        >
          <Ionicons name="add" size={24} color={COLORS.TEXT_WHITE} />
          <Text style={styles.addButtonText}>Create New Event</Text>
        </TouchableOpacity>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingAnimation text="Loading your events..." />
          </View>
        ) : (
          <FlatList
            data={events}
            renderItem={renderEventCard}
            keyExtractor={(item) => item.event_id}
            contentContainerStyle={styles.eventsList}
            ListHeaderComponent={
              <View style={styles.noticeContainer}>
                <View style={styles.noticeItem}>
                  <Ionicons name="information-circle-outline" size={18} color={COLORS.TEXT_GRAY} />
                  <Text style={styles.noticeText}>
                    Event created will be in "Pending Verification" status. Please wait for admin approval.
                  </Text>
                </View>
                <View style={styles.noticeItem}>
                  <Ionicons name="alert-circle-outline" size={18} color={COLORS.TEXT_GRAY} />
                  <Text style={styles.noticeText}>
                    Events cannot be deleted once created.
                  </Text>
                </View>
              </View>
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="calendar-outline" size={48} color={COLORS.TEXT_GRAY} />
                <Text style={styles.emptyText}>You haven't created any events yet</Text>
              </View>
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  content: {
    paddingHorizontal: SPACING.MEDIUM,
  },
  addButton: {
    flexDirection: 'row',
    backgroundColor: COLORS.PRIMARY,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.MEDIUM,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  addButtonText: {
    color: COLORS.TEXT_WHITE,
    fontWeight: 'bold',
    marginLeft: SPACING.SMALL,
    fontSize: FONT_SIZES.MEDIUM,
  },
  noticeContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: RADIUS.SMALL,
    padding: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.WARNING || '#ffc107',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  noticeItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.SMALL,
  },
  noticeText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    marginLeft: SPACING.SMALL,
    flex: 1,
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  eventCard: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PRIMARY,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SMALL,
    paddingBottom: SPACING.SMALL,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  eventTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    flex: 1,
    marginRight: SPACING.MEDIUM,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.MEDIUM,
    paddingVertical: SPACING.SMALL,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: RADIUS.SMALL,
    gap: SPACING.XSMALL,
  },
  editButtonText: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
  },
  eventDetails: {
    marginTop: SPACING.SMALL,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SMALL,
  },
  detailText: {
    color: COLORS.TEXT_GRAY,
    marginLeft: SPACING.XSMALL,
    fontSize: FONT_SIZES.SMALL,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SMALL,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: RADIUS.SMALL,
    marginRight: SPACING.SMALL,
    marginBottom: SPACING.SMALL,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  verifiedBadge: {
    backgroundColor: '#28a745',
  },
  unverifiedBadge: {
    backgroundColor: '#ffc107',
  },
  statusText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.XSMALL,
    fontWeight: 'bold',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.SMALL,
    gap: SPACING.XSMALL,
  },
  tag: {
    backgroundColor: COLORS.PRIMARY_TRANSPARENT,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: RADIUS.SMALL,
  },
  tagText: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.XSMALL,
  },
  descriptionText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    marginBottom: SPACING.SMALL,
  },
  dateText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.XSMALL,
    marginTop: SPACING.XSMALL,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.XLARGE,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    borderRadius: RADIUS.MEDIUM,
    marginTop: SPACING.LARGE,
  },
  emptyText: {
    color: COLORS.TEXT_GRAY,
    marginTop: SPACING.MEDIUM,
    textAlign: 'center',
    fontSize: FONT_SIZES.MEDIUM,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: SPACING.SMALL,
  },

  disabledButton: {
    opacity: 0.5,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  eventsList: {
    paddingBottom: 180,
  },
});