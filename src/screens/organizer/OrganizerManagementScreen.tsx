// Organizer Portal Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import { supabase } from '../../lib/supabase';
import { eventService } from '../../services/eventService';
import LoadingAnimation from '../../components/LoadingAnimation';

type OrganizerManagementScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'OrganizerManagement'
>;

type EventWithSoldTickets = {
  event_id: string;
  title: string;
  sessions: SessionWithSoldTickets[];
  soldTickets: number;
  totalCapacity: number;
};

type SessionWithSoldTickets = {
  id: string;
  title: string;
  start_time: string;
  capacity: number;
  soldTickets: number;
};

export default function OrganizerManagementScreen() {
  const navigation = useNavigation<OrganizerManagementScreenNavigationProp>();
  const [events, setEvents] = useState<EventWithSoldTickets[]>([]);
  const [expandedEvents, setExpandedEvents] = useState<{[key: string]: boolean}>({});
  const [loading, setLoading] = useState(true);
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;

  useEffect(() => {
    loadOrganizerEvents();
  }, []);

  const loadOrganizerEvents = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        navigation.navigate('Home', { screen: 'HomeTab' });
        return;
      }

      // Get organizer ID
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (profileError) throw profileError;
      
      // Get all events from this organizer
      const organizerEvents = await eventService.getOrganizerEvents(user.id);
      
      // Get sold tickets for each event and session
      const eventsWithSoldTickets: EventWithSoldTickets[] = [];
      
      for (const event of organizerEvents || []) {
        // Get event sold tickets
        const soldTickets = await eventService.getEventSoldTickets(event.event_id);
        
        // Calculate total capacity
        const totalCapacity = event.sessions.reduce((sum: number, session: any) => 
          sum + (session.capacity || 0), 0);
        
        // Get sold tickets for each session
        const sessionsWithSoldTickets: SessionWithSoldTickets[] = [];
        
        for (const session of event.sessions) {
          const sessionSoldTickets = await eventService.getSessionSoldTickets(session.id);
          
          sessionsWithSoldTickets.push({
            ...session,
            soldTickets: sessionSoldTickets
          });
        }
        
        eventsWithSoldTickets.push({
          ...event,
          sessions: sessionsWithSoldTickets,
          soldTickets,
          totalCapacity
        });
      }
      
      setEvents(eventsWithSoldTickets);
    } catch (error) {
      console.error('Failed to load organizer events:', error);
      Alert.alert('Error', 'Unable to load event data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const navigateToValidateTickets = () => {
    navigation.navigate('TicketValidation');
  };

  const navigateToManageEvents = () => {
    navigation.navigate('ManageEvents');
  };

  const navigateToRevenueReport = () => {
    navigation.navigate('RevenueReport');
  };

  const toggleEventExpand = (eventId: string) => {
    setExpandedEvents(prev => ({
      ...prev,
      [eventId]: !prev[eventId]
    }));
  };

  const navigateToSessionAttendees = (sessionId: string, sessionTitle: string) => {
    navigation.navigate('SessionAttendees', { sessionId, sessionTitle });
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Organizer Portal</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.buttonsContainer}>
          <TouchableOpacity 
            style={styles.managementButton}
            onPress={navigateToManageEvents}
          >
            <Ionicons name="calendar-outline" size={32} color={COLORS.PRIMARY} />
            <Text style={styles.buttonTitle}>Manage Events</Text>
            <Text style={styles.buttonSubtitle}>View and manage all your events</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.managementButton}
            onPress={navigateToValidateTickets}
          >
            <Ionicons name="qr-code-outline" size={32} color={COLORS.PRIMARY} />
            <Text style={styles.buttonTitle}>Validate Tickets</Text>
            <Text style={styles.buttonSubtitle}>Validate attendee e-tickets</Text>
          </TouchableOpacity>
        </View>
        
        {/* <TouchableOpacity 
          style={styles.revenueButton}
          onPress={navigateToRevenueReport}
        >
          <Ionicons name="stats-chart" size={32} color={COLORS.PRIMARY} />
          <Text style={styles.buttonTitle}>Revenue Report</Text>
          <Text style={styles.buttonSubtitle}>Check your income and session details</Text>
        </TouchableOpacity> */}

        <Text style={styles.sectionTitle}>Your Events</Text>
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingAnimation />
          </View>
        ) : events.length > 0 ? (
          events.map((event, index) => (
            <View key={event.event_id} style={styles.eventCard}>
              <TouchableOpacity 
                style={styles.eventHeader}
                onPress={() => toggleEventExpand(event.event_id)}
              >
                <View style={styles.eventTitleContainer}>
                  <Text style={styles.eventTitle}>{event.title}</Text>
                  <Text style={styles.eventDate}>
                    {new Date(event.sessions[0]?.start_time).toLocaleDateString()}
                  </Text>
                </View>
                <Ionicons 
                  name={expandedEvents[event.event_id] ? "chevron-up" : "chevron-down"} 
                  size={24} 
                  color={COLORS.TEXT_GRAY} 
                />
              </TouchableOpacity>
              
              <View style={styles.eventStats}>
                <Text style={styles.statsText}>
                  Tickets Sold: {event.soldTickets}
                </Text>
                <Text style={styles.statsText}>
                  Total Capacity: {event.totalCapacity}
                </Text>
              </View>

              {expandedEvents[event.event_id] && (
                <View style={styles.sessionsContainer}>
                  <Text style={styles.sessionsTitle}>Event Sessions</Text>
                  {event.sessions.map((session: SessionWithSoldTickets) => (
                    <TouchableOpacity 
                      key={session.id}
                      style={styles.sessionItem}
                      onPress={() => navigateToSessionAttendees(session.id, session.title || event.title)}
                    >
                      <View style={styles.sessionInfo}>
                        <Text style={styles.sessionTitle}>{session.title || event.title}</Text>
                        <Text style={styles.sessionTime}>
                          {formatDateTime(session.start_time)}
                        </Text>
                      </View>
                      <View style={styles.sessionStats}>
                        <Text style={styles.sessionStatsText}>
                          {session.soldTickets}/{session.capacity || 0}
                        </Text>
                        <Ionicons name="people" size={16} color={COLORS.TEXT_WHITE} />
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.noEventsText}>You haven't created any events yet</Text>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40,
  },
  content: {
    paddingHorizontal: SPACING.MEDIUM,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.MEDIUM,
  },
  managementButton: {
    width: '48%',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY + '30',
  },
  revenueButton: {
    width: '100%',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.PRIMARY + '30',
    marginBottom: SPACING.LARGE,
  },
  buttonTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginTop: SPACING.SMALL,
    marginBottom: SPACING.XSMALL,
  },
  buttonSubtitle: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  eventCard: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PRIMARY,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTitleContainer: {
    flex: 1,
  },
  eventTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.XSMALL,
  },
  eventDate: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.SMALL,
  },
  eventStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statsText: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_LIGHT,
  },
  sessionsContainer: {
    marginTop: SPACING.MEDIUM,
    borderTopWidth: 1,
    borderTopColor: COLORS.BACKGROUND_CARD,
    paddingTop: SPACING.MEDIUM,
  },
  sessionsTitle: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.SMALL,
  },
  sessionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_DARK,
    borderRadius: RADIUS.SMALL,
    padding: SPACING.SMALL,
    marginBottom: SPACING.SMALL,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.PRIMARY,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionTitle: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.XSMALL,
  },
  sessionTime: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_GRAY,
  },
  sessionStats: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.PRIMARY + '20',
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
  },
  sessionStatsText: {
    fontSize: FONT_SIZES.XSMALL,
    color: COLORS.TEXT_WHITE,
    fontWeight: 'bold',
    marginRight: SPACING.XSMALL,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },
  noEventsText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginTop: SPACING.LARGE,
  },
}); 