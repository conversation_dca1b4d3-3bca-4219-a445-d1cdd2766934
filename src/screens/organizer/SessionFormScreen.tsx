import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types/navigation';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../../config/theme';
import BackButton from '../../components/BackButton';
import DateTimePicker from '@react-native-community/datetimepicker';

type SessionFormScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SessionForm'
>;

type SessionFormScreenRouteProp = RouteProp<
  RootStackParamList,
  'SessionForm'
>;

type Session = {
  id?: string;
  title: string;
  start_time: string;
  price: number | null;
  capacity: number;
  max_tickets_per_user?: number;
  status: 'available' | 'pause';
  sale_start_time?: string;
  sale_end_time?: string;
}

const emptySessionForm: Session = {
  title: '',
  start_time: new Date().toISOString(),
  price: null,
  capacity: 0,
  status: 'available',
};

export default function SessionFormScreen() {
  const navigation = useNavigation<SessionFormScreenNavigationProp>();
  const route = useRoute<SessionFormScreenRouteProp>();
  const { session, isEditing } = route.params || {};

  const [sessionForm, setSessionForm] = useState<Session>(session || emptySessionForm);
  const [priceInputText, setPriceInputText] = useState<string>(
    session && session.price !== null ? session.price.toString() : ''
  );
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'date' | 'time'>('date');
  const [tempDate, setTempDate] = useState<Date>(new Date(sessionForm.start_time));
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  useEffect(() => {
    setPriceInputText(sessionForm.price !== null ? sessionForm.price.toString() : '');
  }, [sessionForm.price]);

  const showDatePickerHandler = (mode: 'date' | 'time') => {
    setDatePickerMode(mode);
    setTempDate(new Date(sessionForm.start_time));
    setShowDatePicker(true);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
    if (selectedDate) {
      setTempDate(selectedDate);
      if (Platform.OS === 'android') {
        confirmDate(selectedDate);
      }
    }
  };

  const confirmDate = (dateToConfirm: Date) => {
    const currentDate = new Date(sessionForm.start_time);
    let newDate;

    if (datePickerMode === 'date') {
      newDate = new Date(
        dateToConfirm.getFullYear(),
        dateToConfirm.getMonth(),
        dateToConfirm.getDate(),
        currentDate.getHours(),
        currentDate.getMinutes()
      );
    } else {
      newDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        currentDate.getDate(),
        dateToConfirm.getHours(),
        dateToConfirm.getMinutes()
      );
    }
    setSessionForm({ ...sessionForm, start_time: newDate.toISOString() });
    if (Platform.OS === 'ios') {
      setShowDatePicker(false);
    }
  };

  const handleSave = () => {
    if (!sessionForm.title) {
      Alert.alert('Error', 'Session title is required.');
      return;
    }
    if (!sessionForm.capacity || sessionForm.capacity <= 0) {
      Alert.alert('Error', 'Capacity must be greater than 0.');
      return;
    }

    let validatedPrice: number | null = null;
    if (priceInputText.trim() !== '') {
      const trimmedPrice = priceInputText.trim();
      // 使用正则表达式验证价格格式：一个或多个数字，可选地跟着一个点和一到两位小数。
      // 这能确保价格是非负的，并且格式正确。
      const priceRegex = /^\d+(\.\d{1,2})?$/;

      if (!priceRegex.test(trimmedPrice)) {
        Alert.alert(
          'Invalid Price',
          'Please enter a valid, non-negative price with up to two decimal places (e.g., 10 or 10.50).'
        );
        return;
      }
      
      // 正则表达式已确保格式正确，此处直接转换
      validatedPrice = parseFloat(trimmedPrice);
    }

    const finalSessionData = { ...sessionForm, price: validatedPrice };

    const onConfirm = () => {
      if (route.params?.onSave) {
        route.params.onSave(finalSessionData);
      }
      navigation.goBack();
    };

    if (!isEditing) {
      Alert.alert(
        'Important Notice',
        'After creation, you CANNOT modify:\n\n• Session time\n• Price\n• Capacity\n\nPlease ensure all details are correct.',
        [
          { text: 'Review Again', style: 'cancel' },
          { text: 'Confirm & Create', style: 'destructive', onPress: onConfirm },
        ]
      );
    } else {
      onConfirm();
    }
  };

  const renderDatePicker = () => {
    if (!showDatePicker) return null;

    const picker = (
      <DateTimePicker
        value={tempDate}
        mode={datePickerMode}
        is24Hour={true}
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        onChange={handleDateChange}
        textColor={Platform.OS === 'ios' ? COLORS.TEXT_WHITE : undefined}
      />
    );

    if (Platform.OS === 'ios') {
      return (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.datePickerModalContainer}>
            <View style={styles.datePickerContainer}>
              <View style={styles.datePickerHeader}>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <Text style={styles.datePickerCancelText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => confirmDate(tempDate)}>
                  <Text style={styles.datePickerDoneText}>Done</Text>
                </TouchableOpacity>
              </View>
              {picker}
            </View>
          </View>
        </Modal>
      );
    }

    return picker;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.BACKGROUND_DARK} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={styles.header}>
          <BackButton style={styles.backButton} />
          <Text style={styles.headerTitle}>{isEditing ? 'Edit Session' : 'Add Session'}</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView
          style={styles.content}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.formContainer}>
              <Text style={styles.inputLabel}>Title *</Text>
              <TextInput
                style={[styles.input, focusedInput === 'title' && styles.inputFocused]}
                value={sessionForm.title}
                onChangeText={(text) => setSessionForm({ ...sessionForm, title: text })}
                placeholder="e.g., Morning Session, VIP Access"
                placeholderTextColor={COLORS.TEXT_GRAY}
                onFocus={() => setFocusedInput('title')}
                onBlur={() => setFocusedInput(null)}
              />

              <Text style={styles.inputLabel}>Start Time *</Text>
              <View style={styles.dateInputContainer}>
                <TouchableOpacity
                  style={[styles.dateInput, isEditing && styles.disabledInput]}
                  onPress={() => !isEditing ? showDatePickerHandler('date') : Alert.alert('Notice', 'Contact support to modify session time.')}
                >
                  <Text style={[styles.dateText, isEditing && styles.disabledText]}>{new Date(sessionForm.start_time).toLocaleDateString()}</Text>
                  <Ionicons name="calendar-outline" size={20} color={isEditing ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.dateInput, isEditing && styles.disabledInput]}
                  onPress={() => !isEditing ? showDatePickerHandler('time') : Alert.alert('Notice', 'Contact support to modify session time.')}
                >
                  <Text style={[styles.dateText, isEditing && styles.disabledText]}>{new Date(sessionForm.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</Text>
                  <Ionicons name="time-outline" size={20} color={isEditing ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
                </TouchableOpacity>
              </View>

              <Text style={styles.inputLabel}>Price (AUD)</Text>
              <TextInput
                style={[styles.input, isEditing && styles.disabledInput, focusedInput === 'price' && styles.inputFocused]}
                value={priceInputText}
                onChangeText={setPriceInputText}
                placeholder="e.g., 10 or 10.50 (Free if empty)"
                placeholderTextColor={COLORS.TEXT_GRAY}
                keyboardType="decimal-pad"
                editable={!isEditing}
                onFocus={() => setFocusedInput('price')}
                onBlur={() => setFocusedInput(null)}
              />

              <Text style={styles.inputLabel}>Capacity *</Text>
              <TextInput
                style={[styles.input, isEditing && styles.disabledInput, focusedInput === 'capacity' && styles.inputFocused]}
                value={sessionForm.capacity > 0 ? sessionForm.capacity.toString() : ''}
                onChangeText={(text) => setSessionForm({ ...sessionForm, capacity: Number(text) || 0 })}
                placeholder="Maximum attendees"
                placeholderTextColor={COLORS.TEXT_GRAY}
                keyboardType="numeric"
                editable={!isEditing}
                onFocus={() => setFocusedInput('capacity')}
                onBlur={() => setFocusedInput(null)}
              />

              <Text style={styles.inputLabel}>Max Tickets Per User</Text>
              <TextInput
                style={[styles.input, focusedInput === 'max_tickets' && styles.inputFocused]}
                value={sessionForm.max_tickets_per_user ? sessionForm.max_tickets_per_user.toString() : ''}
                onChangeText={(text) => setSessionForm({ ...sessionForm, max_tickets_per_user: Number(text) || undefined })}
                placeholder="Leave empty for unlimited"
                placeholderTextColor={COLORS.TEXT_GRAY}
                keyboardType="numeric"
                onFocus={() => setFocusedInput('max_tickets')}
                onBlur={() => setFocusedInput(null)}
              />



              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save Session</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </KeyboardAvoidingView>
      {renderDatePicker()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    paddingTop: Platform.OS === 'ios' ? SPACING.SMALL : StatusBar.currentHeight,
    paddingBottom: SPACING.SMALL,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  backButton: {
    padding: SPACING.SMALL,
    // The new component has a fixed size, so we just apply padding here
    // and rely on the component's internal styling for the rest.
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 44,
  },
  content: {
    flex: 1,
  },
  formContainer: {
    paddingHorizontal: SPACING.MEDIUM,
    paddingBottom: SPACING.XLARGE,
  },
  inputLabel: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
    marginBottom: SPACING.SMALL,
    marginTop: SPACING.MEDIUM,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  input: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    paddingHorizontal: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    height: 52,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
  },
  inputFocused: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 1.5,
  },
  disabledInput: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    opacity: 0.6,
  },
  disabledText: {
    color: COLORS.TEXT_GRAY,
  },
  dateInputContainer: {
    flexDirection: 'row',
    gap: SPACING.MEDIUM,
  },
  dateInput: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_CARD,
    paddingHorizontal: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    height: 52,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
  },
  saveButton: {
    backgroundColor: COLORS.PRIMARY,
    height: 52,
    borderRadius: RADIUS.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: SPACING.XLARGE,
  },
  saveButtonText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },

  datePickerModalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  datePickerContainer: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderTopLeftRadius: RADIUS.LARGE,
    borderTopRightRadius: RADIUS.LARGE,
    paddingBottom: SPACING.LARGE,
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER_COLOR,
  },
  datePickerCancelText: {
    color: COLORS.TEXT_LIGHT,
    fontSize: FONT_SIZES.MEDIUM,
  },
  datePickerDoneText: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
  },
});