import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, ImageBackground, Platform, TextInput, FlatList, ActivityIndicator, RefreshControl, Animated } from 'react-native';
import EventCard from '../components/EventCard';
import { useTheme } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FindScreenProps } from '../types/navigation';
import { Ionicons } from '@expo/vector-icons';
import { Database } from '../types/database';
import { eventService, PaginatedResult } from '../services/eventService';
import { COLORS, FONT_SIZES } from '../config/theme';
import LoadingAnimation from '../components/LoadingAnimation';
import { cacheService } from '../services/cacheService';
import FastImage from 'react-native-fast-image';
import { BlurView } from 'expo-blur';

type Event = Database['public']['Tables']['events']['Row'] & {
  sessions: Database['public']['Tables']['event_sessions']['Row'][];
  organizer: Database['public']['Tables']['organizer_profiles']['Row'];
};

type UIEvent = Event & {
  fullAddress: string | null;
  isFeatured: boolean;
  participants: {
    current: number;
    total: number;
  };
  price: number;
  organizer?: {
    id: string;
    name: string;
    avatar_url: string | null;
    verification_status: string | null;
  };
};

const { width } = Dimensions.get('window');
const CARD_MARGIN = 8;
const CARD_WIDTH = (width - 32 - CARD_MARGIN) / 2;

// Define a default image for each tag
const TAG_IMAGES: Record<string, string> = {
  'Outdoor': 'https://images.unsplash.com/photo-1531180488878-b785d2ce8f71?q=80&w=2630&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  'Sports': 'https://images.unsplash.com/photo-1597769555495-c54a15cd8c3f?q=80&w=2670&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  'Indoor': 'https://images.unsplash.com/photo-1532702145468-4041ff9ccf56?q=80&w=2671&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  'Social': 'https://images.unsplash.com/photo-1513118172236-00b7cc57e1fa?q=80&w=2380&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  'Free Events': 'https://images.unsplash.com/photo-1593283590172-adfce2adf213?q=80&w=2574&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  'Performance': 'https://images.unsplash.com/photo-1549046701-6bd11cf71796?q=80&w=2462&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
};

const TagCard = ({ tag, onPress }: { tag: string; onPress: () => void }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const blurAnim = useRef(new Animated.Value(20)).current;

  const handleImageLoad = () => {
    setImageLoaded(true);
    Animated.timing(blurAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      style={styles.tagCard}
    >
      <View style={styles.tagImageContainer}>
        <FastImage
          source={{ 
            uri: TAG_IMAGES[tag] || 'https://images.unsplash.com/photo-1593283590172-adfce2adf213?q=80&w=2574&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            priority: FastImage.priority.normal,
            cache: FastImage.cacheControl.immutable,
          }}
          style={[StyleSheet.absoluteFill, styles.tagImageStyle]}
          resizeMode={FastImage.resizeMode.cover}
          onLoadEnd={handleImageLoad}
        />
        {!imageLoaded && (
          <View style={[StyleSheet.absoluteFill, styles.blurContainer]}>
            <BlurView intensity={100} style={StyleSheet.absoluteFill} />
          </View>
        )}
        <Animated.View style={[
          StyleSheet.absoluteFill,
          {
            opacity: blurAnim.interpolate({
              inputRange: [0, 20],
              outputRange: [0, 1],
            }),
          }
        ]}>
          <BlurView intensity={100} style={StyleSheet.absoluteFill} />
        </Animated.View>
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
          style={StyleSheet.absoluteFill}
        />
        <Text style={styles.tagText}>{tag}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default function FindScreen({ navigation }: FindScreenProps) {
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const { colors } = useTheme();
  const PAGE_SIZE = 10;
  const [allTags, setAllTags] = useState<string[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(true);
  const [isRefreshingTags, setIsRefreshingTags] = useState(false);

  // Get all unique tags with cache support
  useEffect(() => {
    const fetchAllTags = async () => {
      try {
        setIsLoadingTags(true);
        
        // 尝试从缓存获取标签
        const cachedTags = await cacheService.getCachedTags();
        
        if (cachedTags) {
          // 如果有缓存数据，直接使用
          setAllTags(cachedTags);
          setIsLoadingTags(false);
          
          // 在后台刷新标签数据
          refreshTagsInBackground();
        } else {
          // 如果没有缓存数据，从服务器获取
          await fetchTagsFromServer();
        }
      } catch (error) {
        console.error('Failed to fetch tags:', error);
        setAllTags([]);
        setIsLoadingTags(false);
      }
    };

    fetchAllTags();
  }, []);

  // 从服务器获取标签数据
  const fetchTagsFromServer = async () => {
    try {
      // 获取事件数据以提取标签
      const result = await eventService.getTrendingEvents(0, 100);
      const tags = new Set<string>();
      
      // 从所有事件中提取唯一的标签
      result.data.forEach(event => {
        event.tags.forEach(tag => tags.add(tag));
      });
      
      const tagsArray = Array.from(tags).sort();
      setAllTags(tagsArray);
      
      // 缓存标签数据
      await cacheService.cacheTags(tagsArray);
    } catch (error) {
      console.error('Failed to fetch tags from server:', error);
    } finally {
      setIsLoadingTags(false);
      setIsRefreshingTags(false);
    }
  };

  // 在后台刷新标签数据
  const refreshTagsInBackground = async () => {
    setIsRefreshingTags(true);
    await fetchTagsFromServer();
  };

  // Search events
  useEffect(() => {
    const searchEvents = async () => {
      if (!searchQuery) {
        setSearchResults([]);
        setCurrentPage(0);
        setHasMore(true);
        return;
      }

      try {
        setIsLoading(true);
        setCurrentPage(0);
        const result = await eventService.searchEvents(searchQuery, 0, PAGE_SIZE);
        
        // Fix type error: ensure results include sessions and organizer
        const eventsWithRelations = result.data.map(event => ({
          ...event,
          sessions: [],  // Initialize as empty array
          organizer: {} as Database['public']['Tables']['organizer_profiles']['Row'] // Initialize as empty object
        }));
        
        setSearchResults(eventsWithRelations);
        setHasMore(result.hasMore);
      } catch (error) {
        console.error('Failed to search events:', error);
        setSearchResults([]);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimeout = setTimeout(searchEvents, 300);
    return () => clearTimeout(debounceTimeout);
  }, [searchQuery]);

  const loadMoreSearchResults = async () => {
    if (!searchQuery || isLoadingMore || !hasMore) return;

    try {
      setIsLoadingMore(true);
      const nextPage = currentPage + 1;
      const result = await eventService.searchEvents(searchQuery, nextPage, PAGE_SIZE);
      
      // Fix type error: ensure results include sessions and organizer
      const eventsWithRelations = result.data.map(event => ({
        ...event,
        sessions: [],  // Initialize as empty array
        organizer: {} as Database['public']['Tables']['organizer_profiles']['Row'] // Initialize as empty object
      }));
      
      setSearchResults(prev => [...prev, ...eventsWithRelations]);
      setCurrentPage(nextPage);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error('Failed to load more search results:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleTagPress = (tag: string) => {
    setSelectedTag(tag);
    navigation.navigate('TagEvents', { tagId: tag });
  };

  const handleEventPress = (eventId: string) => {
    navigation.navigate('EventDetails', { eventId });
  };

  // Render search result item
  const renderSearchResultItem = useCallback(({ item }: { item: Event }) => (
    <TouchableOpacity
      style={styles.eventCard}
      onPress={() => handleEventPress(item.event_id)}
    >
      <ImageBackground
        source={{ uri: item.image }}
        style={styles.eventImage}
        imageStyle={styles.eventImageStyle}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
          style={StyleSheet.absoluteFill}
        />
        <View style={styles.eventInfo}>
          <Text style={styles.eventTitle}>{item.title}</Text>
          <View style={styles.eventTags}>
            {item.tags.slice(0, 2).map((tag, tagIndex) => (
              <View key={`${tag}-${tagIndex}`} style={styles.eventTagBadge}>
                <Text style={styles.eventTagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>
      </ImageBackground>
    </TouchableOpacity>
  ), []);

  // Render loading more indicator
  const renderFooter = () => {
    if (!isLoadingMore) return null;
    
    return (
      <View style={styles.loadingMoreContainer}>
        <LoadingAnimation size="small" text="Loading more..." />
      </View>
    );
  };

  // 手动刷新标签数据
  const handleRefreshTags = async () => {
    setIsRefreshingTags(true);
    await fetchTagsFromServer();
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      {searchQuery ? (
        // Search results view - keep existing structure for search
        <View style={styles.container}>
          {/* Search section */}
          <View style={styles.searchSection}>
            <Text style={styles.sectionTitle}>
              SEARCH <Text style={styles.highlightText}>EVENTS</Text>
            </Text>
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search events..."
                placeholderTextColor="#666"
                value={searchQuery}
                onChangeText={setSearchQuery}
                returnKeyType="search"
                autoCapitalize="none"
              />
              {searchQuery ? (
                <TouchableOpacity
                  onPress={() => setSearchQuery('')}
                  style={styles.clearButton}
                >
                  <Ionicons name="close-circle" size={20} color="#666" />
                </TouchableOpacity>
              ) : null}
            </View>
          </View>

          <View style={styles.contentContainer}>
            {isLoading ? (
              <View style={styles.noResults}>
                <LoadingAnimation text="Searching..." />
              </View>
            ) : searchResults.length > 0 ? (
              <FlatList
                data={searchResults}
                renderItem={renderSearchResultItem}
                keyExtractor={(item, index) => `${item.event_id}-${index}`}
                onEndReached={loadMoreSearchResults}
                onEndReachedThreshold={0.3}
                ListFooterComponent={renderFooter}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.searchResultsContent}
              />
            ) : (
              <View style={styles.noResults}>
                <Text style={styles.noResultsText}>No events found</Text>
              </View>
            )}
          </View>
        </View>
      ) : (
        // Tags browsing view - unified scroll view
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.unifiedScrollContent}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshingTags}
              onRefresh={handleRefreshTags}
              tintColor={COLORS.PRIMARY}
              colors={[COLORS.PRIMARY]}
            />
          }
        >
          {/* Search section */}
          <View style={styles.searchSection}>
            <Text style={styles.sectionTitle}>
              SEARCH <Text style={styles.highlightText}>EVENTS</Text>
            </Text>
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search events..."
                placeholderTextColor="#666"
                value={searchQuery}
                onChangeText={setSearchQuery}
                returnKeyType="search"
                autoCapitalize="none"
              />
              {searchQuery ? (
                <TouchableOpacity
                  onPress={() => setSearchQuery('')}
                  style={styles.clearButton}
                >
                  <Ionicons name="close-circle" size={20} color="#666" />
                </TouchableOpacity>
              ) : null}
            </View>
          </View>

          {/* Tags section */}
          <View style={styles.tagsSection}>
            <Text style={styles.sectionTitle}>
              OR EXPLORE <Text style={styles.highlightText}>TAGS</Text>
            </Text>
            {isLoadingTags ? (
              <View style={styles.loadingContainer}>
                <LoadingAnimation text="Loading tags..." />
              </View>
            ) : (
              <View style={styles.tagGrid}>
                {allTags.map((tag) => (
                  <TagCard
                    key={tag}
                    tag={tag}
                    onPress={() => handleTagPress(tag)}
                  />
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#000',
  },
  container: {
    flex: 1,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    letterSpacing: 1,
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1c1c1e',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: '#fff',
    fontSize: 16,
  },
  clearButton: {
    padding: 4,
  },
  noResults: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  noResultsText: {
    color: '#999',
    fontSize: 16,
  },
  eventCard: {
    width: '100%',
    height: 180,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  eventImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
  },
  eventImageStyle: {
    borderRadius: 12,
  },
  eventInfo: {
    padding: 16,
  },
  eventTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  eventTags: {
    flexDirection: 'row',
  },
  eventTagBadge: {
    backgroundColor: 'rgba(231, 76, 38, 0.15)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  eventTagText: {
    color: COLORS.PRIMARY,
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#999',
    marginTop: 8,
    fontSize: 16,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  categoryCard: {
    width: '100%',
    height: 160,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    padding: 16,
  },
  categoryImageStyle: {
    borderRadius: 12,
  },
  categoryTitle: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
  },
  categoriesContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  sectionHeader: {
    marginBottom: 12,
  },
  sectionHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  loadingMoreContainer: {
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  loadingMoreText: {
    color: '#999',
    marginLeft: 8,
  },
  searchResultsContent: {
    paddingVertical: 10,
    paddingBottom: 96,
  },
  scrollViewContent: {
    paddingBottom: 50,
  },
  unifiedScrollContent: {
    paddingBottom: 50,
  },
  tagsSection: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 40,
  },
  tagCard: {
    width: (width - 48) / 2,
    height: (width - 48) / 2,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  tagImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  tagImageStyle: {
    borderRadius: 12,
  },
  blurContainer: {
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  tagText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    zIndex: 2,
  },
  tagGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 0,
  },
  highlightText: {
    color: COLORS.PRIMARY,
  },
}); 