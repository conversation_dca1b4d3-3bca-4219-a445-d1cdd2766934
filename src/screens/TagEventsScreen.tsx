import { StyleSheet, Text, View, FlatList, TouchableOpacity, SafeAreaView, Platform, StatusBar, ActivityIndicator, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TagEventsScreenProps } from '../types/navigation';
import { useEffect, useState, useCallback } from 'react';
import { eventService, PaginatedResult } from '../services/eventService';
import { Database } from '../types/database';
import EventCard from '../components/EventCard';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';
import LoadingAnimation from '../components/LoadingAnimation';
import { cacheService } from '../services/cacheService';
import BackButton from '../components/BackButton';
import { favoriteService } from '../services/favoriteService';

type Event = Database['public']['Tables']['events']['Row'] & {
  sessions: Database['public']['Tables']['event_sessions']['Row'][];
  organizer: Database['public']['Tables']['organizer_profiles']['Row'];
};

type UIEvent = Event & {
  date?: string;
  fullAddress: string | null;
  isFeatured: boolean;
  participants: {
    current: number;
    total: number;
  };
  price: number;
  organizer?: {
    id: string;
    name: string;
    avatar_url: string | null;
    verification_status: string | null;
  };
};

export default function TagEventsScreen({ route, navigation }: TagEventsScreenProps) {
  const { tagId } = route.params;
  const [events, setEvents] = useState<UIEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [favoriteStatus, setFavoriteStatus] = useState<Record<string, boolean>>({});
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
  const PAGE_SIZE = 10;

  useEffect(() => {
    loadEvents();
  }, [tagId]);

  const loadEvents = async (page = 0, shouldRefresh = false) => {
    try {
      if (shouldRefresh) {
        setRefreshing(true);
      } else if (page === 0) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      // 尝试从缓存获取数据
      if (!shouldRefresh) {
        const cachedData = await cacheService.getCachedTagEvents(tagId, page);
        if (cachedData) {
          const uiEvents = mapEventsToUIEvents(cachedData.events);

          if (page === 0) {
            setEvents(uiEvents);
          } else {
            setEvents(prev => [...prev, ...uiEvents]);
          }

          // 批量获取收藏状态
          if (uiEvents.length > 0) {
            const eventIds = uiEvents.map(event => event.event_id);
            const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
            setFavoriteStatus(prev => ({
              ...prev,
              ...batchFavoriteStatus
            }));
          }

          setCurrentPage(cachedData.page);
          setHasMore(cachedData.hasMore);

          if (page === 0) {
            setIsLoading(false);
          } else {
            setIsLoadingMore(false);
          }

          // 如果是第一页，在后台刷新数据
          if (page === 0) {
            setTimeout(() => {
              fetchEventsFromServer(page, true);
            }, 1000);
          }

          return;
        }
      }

      // 如果没有缓存或需要刷新，从服务器获取数据
      await fetchEventsFromServer(page, shouldRefresh);
      
    } catch (error) {
      console.error('Failed to load tag events:', error);
      setIsLoading(false);
      setIsLoadingMore(false);
      setRefreshing(false);
    }
  };

  // 从服务器获取事件数据
  const fetchEventsFromServer = async (page = 0, shouldRefresh = false) => {
    try {
      const result: PaginatedResult<Event> = await eventService.getEventsByTag(tagId, page, PAGE_SIZE);
      
      // 判断活动是否已结束
      const isEventEnded = (event: Event) => {
        if (event.is_preview) return false; // Coming Soon活动永远不算已结束
        if (!event.sessions || event.sessions.length === 0) return false;
        const now = new Date();
        return event.sessions.every((session: any) => {
          const sessionStartTime = new Date(session.start_time);
          return sessionStartTime <= now || session.status === 'pause';
        });
      };

      // 分离已结束和未结束的活动
      const activeEvents = result.data.filter(event => !isEventEnded(event));
      const endedEvents = result.data.filter(event => isEventEnded(event));

      // 随机排序
      const shuffleArray = (array: Event[]) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };

      // 合并：随机排序的活跃活动 + 随机排序的已结束活动
      const sortedData = [
        ...shuffleArray(activeEvents),
        ...shuffleArray(endedEvents)
      ];

      const uiEvents = mapEventsToUIEvents(sortedData);

      if (shouldRefresh || page === 0) {
        setEvents(uiEvents);
      } else {
        setEvents(prev => [...prev, ...uiEvents]);
      }

      // 批量获取收藏状态
      if (uiEvents.length > 0) {
        const eventIds = uiEvents.map(event => event.event_id);
        const batchFavoriteStatus = await favoriteService.getBatchFavoriteStatus(eventIds);
        setFavoriteStatus(prev => ({
          ...prev,
          ...batchFavoriteStatus
        }));
      }

      setCurrentPage(page);
      setHasMore(result.hasMore);

      // 缓存数据
      await cacheService.cacheTagEvents(tagId, sortedData, page, result.hasMore);
    } catch (error) {
      console.error('Failed to fetch events from server:', error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
      setRefreshing(false);
    }
  };

  // 将事件数据映射为UI事件数据
  const mapEventsToUIEvents = (eventData: Event[]): UIEvent[] => {
    return eventData.map((event: Event) => ({
      ...event,
      date: event.sessions[0]?.start_time,
      fullAddress: event.full_address,
      isFeatured: event.is_featured,
      participants: {
        current: event.sessions.reduce((sum: number, session) => sum + (session.capacity - (session.available || 0)), 0),
        total: event.sessions.reduce((sum: number, session) => sum + session.capacity, 0)
      },
      price: event.sessions.length > 0 ? event.sessions[0].price ?? 'Free' : 'Free',
      organizer: event.organizer
        ? {
            id: event.organizer.organizer_id,
            display_name: event.organizer.display_name || 'Unknown Organizer',
            avatar_url: event.organizer.avatar_url,
            verification_status: event.organizer.verification_status
          }
        : undefined
    }));
  };

  // 更新收藏状态的函数
  const updateFavoriteStatus = useCallback((eventId: string, status: boolean) => {
    setFavoriteStatus(prev => ({
      ...prev,
      [eventId]: status
    }));
  }, []);

  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadEvents(currentPage + 1);
    }
  };

  const handleRefresh = () => {
    loadEvents(0, true);
  };

  // 渲染列表项
  const renderItem = useCallback(({ item }: { item: UIEvent }) => {
    return (
      <EventCard
        key={item.event_id}
        event={item}
        onPress={() => navigation.navigate('EventDetails', { eventId: item.event_id })}
        isFavorite={favoriteStatus[item.event_id] || false}
        onFavoriteChange={(status) => updateFavoriteStatus(item.event_id, status)}
      />
    );
  }, [favoriteStatus, updateFavoriteStatus]);

  // 渲染列表底部（加载更多指示器）
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.loadingMoreContainer}>
        <LoadingAnimation size="small" text="Loading more..." />
      </View>
    );
  }, [isLoadingMore]);

  // 渲染列表为空时的组件
  const renderEmpty = useCallback(() => {
    if (isLoading) return null;
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No events found</Text>
      </View>
    );
  }, [isLoading]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={[styles.header, { marginTop: statusBarHeight }]}>
        <BackButton style={styles.backButton} />
        <Text style={styles.headerTitle}>{tagId}</Text>
        <View style={styles.placeholder} />
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <LoadingAnimation text="Loading events..." />
        </View>
      ) : (
        <FlatList
          data={events}
          renderItem={renderItem}
          keyExtractor={item => item.event_id}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmpty}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={COLORS.PRIMARY}
              colors={[COLORS.PRIMARY]}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    height: 60,
  },
  backButton: {
    padding: SPACING.SMALL,
  },
  headerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  placeholder: {
    width: 40
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  content: {
    paddingHorizontal: SPACING.MEDIUM,
    paddingBottom: 40,
    alignItems: 'center'
  },
  loadingMoreContainer: {
    padding: SPACING.MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100
  },
  emptyText: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center'
  },
  listContent: {
    paddingHorizontal: SPACING.MEDIUM,
    paddingBottom: 40,
    alignItems: 'center'
  }
}); 
