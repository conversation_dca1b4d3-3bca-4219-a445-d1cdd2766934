import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, Dimensions, SafeAreaView, Linking, Platform, StatusBar, Alert, ActivityIndicator, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Button from '../components/Button';
import BackButton from '../components/BackButton';
import { EventDetailsScreenProps } from '../types/navigation';
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import TicketConfirmationSheet from '../components/TicketConfirmationSheet';
import PaymentSuccessModal from '../components/PaymentSuccessModal';
import { eventService } from '../services/eventService';
import { favoriteService } from '../services/favoriteService';
import { Database } from '../types/database';
import { supabase } from '../lib/supabase';
import { orderService, InsufficientInventoryError, UserLimitExceededError, SessionNotFoundError } from '../services/orderService';
import { ticketService } from '../services/ticketService';
import { Event, ExtendedEventSession } from '../types/event';
import { COLORS, RADIUS, FONT_SIZES, SPACING, GRADIENTS } from '../config/theme';
import { useStripe } from '@stripe/stripe-react-native';
import LoadingAnimation from '../components/LoadingAnimation';
import { feeService } from '../services/feeService';
import { cacheService } from '../services/cacheService';
import { useAuth } from '../context/AuthContext';
import FastImage from 'react-native-fast-image';
import { BlurView } from 'expo-blur';
import ImageViewer from '../components/ImageViewer';
import { toastService } from '../services/toastService';



type UIEvent = Event & {
  event_id: string;
  fullAddress: string | null;
  isFeatured: boolean;
  participants: {
    current: number;
    total: number;
  };
  price: number | 'Free';
  organizer?: {
    id: string;
    name: string;
    avatar_url: string | null;
    verification_status: string | null;
    user_profiles?: {
      avatar_url: string | null;
    };
  };
};

export default function EventDetailsScreen({ route, navigation }: EventDetailsScreenProps) {
  const { eventId } = route.params;
  const { session: authSession } = useAuth();
  const [event, setEvent] = useState<UIEvent | null>(null);
  const [selectedSession, setSelectedSession] = useState<ExtendedEventSession | null>(null);
  const [isConfirmationVisible, setIsConfirmationVisible] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [user, setUser] = useState<any>(null);
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
  const [imageLoaded, setImageLoaded] = useState(false);
  const blurAnim = useRef(new Animated.Value(20)).current;
  const [expandedSessions, setExpandedSessions] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshSuccess, setRefreshSuccess] = useState(false);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [imageViewerIndex, setImageViewerIndex] = useState(0);



  // 初始化Stripe hooks
  const { initPaymentSheet, presentPaymentSheet } = useStripe();

  // 获取当前用户
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    
    getCurrentUser();
  }, []);



  // ✅ 新增：立即取消预留的票（直接调用数据库函数）
  const cancelReservation = async (orderId: string) => {
    try {
      console.log('🚫 Cancelling reservation for order:', orderId);

      // 直接调用数据库函数取消预留
      const { data: result, error } = await supabase
        .rpc('cancel_reservation_atomic', {
          p_order_id: orderId
        });

      if (error) {
        throw new Error(error.message);
      }

      console.log('✅ Reservation cancelled successfully:', result);

      // 清理本地状态
      setIsConfirmationVisible(false);

      // 重新加载活动数据以更新库存
      loadEvent(true);

    } catch (error: any) {
      console.error('❌ Error cancelling reservation:', error);
      // 即使取消失败，也清理本地状态
      setIsConfirmationVisible(false);
    }
  };

  const getStaticMapUrl = (location: string) => {
    const encodedLocation = encodeURIComponent(location);
    const zoom = 15;
    const size = '600x300';
    const scale = 2; // 为Retina显示优化
    const maptype = 'roadmap';
    const markers = `markers=color:red%7C${encodedLocation}`;
    
    return `https://maps.googleapis.com/maps/api/staticmap?center=${encodedLocation}&zoom=${zoom}&size=${size}&scale=${scale}&maptype=${maptype}&${markers}&key=AIzaSyBy-kqs2Jn_k5yMsgvTs41vcYVPL4zZ9p0`;
  };

  useFocusEffect(
    useCallback(() => {
      loadEvent();
      checkFavoriteStatus();
    }, [eventId])
  );

  const checkFavoriteStatus = async () => {
    try {
      const status = await favoriteService.isFavorite(eventId);
      setIsFavorite(status);
    } catch (error) {
              console.error('Failed to check favorite status:', error);
    }
  };

  const handleFavoritePress = async () => {
    if (isLoading) return;

    // 检查用户是否已登录
    if (!authSession) {
      navigation.navigate('SignIn', {
        redirectScreen: 'EventDetails',
        redirectParams: {
          eventId: eventId
        }
      });
      return;
    }

    setIsLoading(true);
    try {
      if (isFavorite) {
        await favoriteService.removeFavorite(eventId);
        setIsFavorite(false);
        toastService.showFavoriteRemoved(event?.title);
      } else {
        await favoriteService.addFavorite(eventId);
        setIsFavorite(true);
        toastService.showFavoriteAdded(event?.title);
      }
    } catch (error) {
      console.error('Failed to update favorite status:', error);
      toastService.show({
        title: 'Error',
        message: 'Failed to update favorite status. Please try again.',
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    setRefreshSuccess(false);

    try {
      // 强制刷新事件数据和收藏状态
      await Promise.all([
        loadEvent(true),
        checkFavoriteStatus()
      ]);

      // 显示成功状态
      setRefreshSuccess(true);

      // 2秒后隐藏成功状态
      setTimeout(() => {
        setRefreshSuccess(false);
      }, 2000);

    } catch (error) {
      console.error('Failed to refresh:', error);
      Alert.alert('Error', 'Failed to refresh. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  };

  const loadEvent = async (forceRefresh = false) => {
    try {
      // 强制刷新时，跳过缓存读取
      const cachedData = !forceRefresh ? await cacheService.getCachedEventDetails(eventId) : null;

      if (cachedData) {
        // 使用缓存数据，但 sessions 部分留空，等待网络请求
        setEvent({
          ...cachedData,
          sessions: [], // 初始化为空数组
          fullAddress: cachedData.full_address,
          isFeatured: cachedData.is_featured,
          // 参与人数和价格依赖 session，在获取到新数据前先设为默认值
          participants: {
            current: 0,
            total: 0,
          },
          price: '...', // 临时显示占位符
          organizer: cachedData.organizer
            ? {
                id: cachedData.organizer.organizer_id,
                name: cachedData.organizer.display_name || 'Unknown Organizer',
                avatar_url: cachedData.organizer.user_profiles?.avatar_url || null,
                verification_status: cachedData.organizer.verification_status
              }
            : undefined
        });

        // 立即从服务器获取最新数据来填充sessions
        fetchAndUpdateEvent();
        
        return;
      }

      // 如果没有缓存数据或需要强制刷新，直接从服务器获取
      await fetchAndUpdateEvent();
    } catch (error) {
      console.error('Failed to load event:', error);
    }
  };

  // 从服务器获取事件数据并更新缓存
  const fetchAndUpdateEvent = async () => {
    try {
      const data = await eventService.getEventById(eventId);
      if (data) {
        const sortedSessions = [...data.sessions].sort((a, b) => {
          if (a.status === 'soldout' && b.status !== 'soldout') return 1;
          if (a.status !== 'soldout' && b.status === 'soldout') return -1;
          return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
        });

        const eventData = {
          ...data,
          sessions: sortedSessions,
          fullAddress: data.full_address,
          isFeatured: data.is_featured,
          participants: {
            current: sortedSessions.reduce((sum: number, session) => sum + (session.capacity - (session.available || 0)), 0),
            total: sortedSessions.reduce((sum: number, session) => sum + session.capacity, 0)
          },
          price: sortedSessions.length > 0 ? sortedSessions[0].price ?? 'Free' : 'Free',
          organizer: data.organizer
            ? {
                id: data.organizer.organizer_id,
                name: data.organizer.display_name || 'Unknown Organizer',
                avatar_url: data.organizer.user_profiles?.avatar_url || null,
                verification_status: data.organizer.verification_status
              }
            : undefined
        };

        setEvent(eventData);
        // 更新缓存
        await cacheService.cacheEventDetails(eventId, data);
      }
    } catch (error) {
      console.error('Failed to fetch event from server:', error);
    }
  };

  const openMap = () => {
    if (!event) return;
    
    const address = encodeURIComponent(event.fullAddress || event.location);
    const scheme = Platform.select({
      ios: `maps:0,0?q=${address}`,
      android: `geo:0,0?q=${address}`
    }) || `https://www.google.com/maps/search/?api=1&query=${address}`;
    
    Linking.canOpenURL(scheme).then(supported => {
      if (supported) {
        Linking.openURL(scheme);
      } else {
        // 如果无法打开默认地图，尝试打开 Google 地图
        const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${address}`;
        Linking.openURL(googleMapsUrl);
      }
    }).catch(err => {
      // 如果出现错误，直接打开 Google 地图
      const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${address}`;
      Linking.openURL(googleMapsUrl);
    });
  };

  const handlePurchase = async (
    totalAmountFromSheet: number,
    // subtotalFromSheet: number, // Potentially needed later
    // serviceFeeFromSheet: number, // Potentially needed later
    // appliedFeeConfigFromSheet: any, // Potentially needed later
  ) => {
    if (!event || !selectedSession) {
      Alert.alert('Error', 'Unable to complete purchase. Please try again.');
      setIsConfirmationVisible(false);
      return;
    }
    
    // 检查用户是否已登录
    if (!authSession) {
      navigation.navigate('SignIn', {
        redirectScreen: 'EventDetails',
        redirectParams: {
          eventId: event.event_id,
          sessionId: selectedSession.id,
          quantity: quantity
        }
      });
      setIsConfirmationVisible(false);
      return;
    }

    // 检查 session 状态
    if (selectedSession.status !== 'available') {
      Alert.alert('Error', 'This session is not available for purchase.');
      setIsConfirmationVisible(false);
      return;
    }

    // 价格计算已在 TicketConfirmationSheet 完成，直接使用 totalAmountFromSheet
    // 移除原有的价格计算逻辑：
    // let feeConfig = null;
    // if (event.organizer) { ... }
    // const price = selectedSession.price || 0;
    // const subtotal = price * quantity;
    // const serviceFee = feeConfig ? ... : ... ;
    // const totalAmount = feeConfig ? ... : ... ;
    
    // 为免费活动添加额外确认步骤
    if (totalAmountFromSheet === 0) {
      Alert.alert(
        'Free Event Confirmation',
        'This is a free event. Please confirm that you are committed to attending before reserving tickets.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              setIsProcessing(false);
            }
          },
          {
            text: 'Confirm Attendance',
            style: 'default',
            onPress: () => processPurchase(totalAmountFromSheet) // 使用 Sheet 传来的总价
          }
        ]
      );
      return;
    }

    // 付费活动直接处理
    processPurchase(totalAmountFromSheet); // 使用 Sheet 传来的总价
  };

  // 添加支付成功模态框状态
  const [isPaymentSuccessVisible, setIsPaymentSuccessVisible] = useState(false);

  // ✅ 重构：处理购买流程
  const processPurchase = async (totalAmount: number) => {
    if (!event || !selectedSession || !user) {
      Alert.alert('Error', 'Unable to complete purchase. Please try again.');
      setIsProcessing(false);
      return;
    }

    try {
      setIsProcessing(true);

      // ✅ 使用新的原子性订单创建函数
      const orderResult = await orderService.createOrderWithInventoryCheck({
        user_id: user.id,
        session_id: selectedSession.id,
        quantity: quantity,
        total_amount: totalAmount,
      });



      // 如果是免费活动，直接处理票据
      if (totalAmount === 0) {
        await processFreeTickets(orderResult.order_id);
      } else {
        // 付费活动，进入支付流程
        await processPaidTickets(orderResult.order_id, totalAmount);
      }

    } catch (error) {
      console.error('Purchase error:', error);

      if (error instanceof InsufficientInventoryError) {
        Alert.alert(
          'Tickets Unavailable',
          error.availableTickets === 0
            ? 'Sorry, this session is now sold out. Please refresh to check for any newly available tickets.'
            : `Sorry, only ${error.availableTickets} tickets are available now. Please refresh to check for updated availability.`,
          [
            {
              text: 'Refresh',
              onPress: () => {
                loadEvent(true); // 强制刷新事件数据
              }
            }
          ]
        );
      } else if (error instanceof UserLimitExceededError) {
        Alert.alert(
          'Purchase Limit Exceeded',
          `You have already purchased ${error.currentCount} tickets. The maximum allowed is ${error.maxAllowed} tickets per user.`,
          [{ text: 'OK', style: 'default' }]
        );
      } else if (error instanceof SessionNotFoundError) {
        Alert.alert(
          'Session Not Found',
          'This event session is no longer available.',
          [{ text: 'OK', style: 'default' }]
        );
      } else {
        Alert.alert(
          'Purchase Error',
          (error as Error).message || 'An unexpected error occurred. Please try again.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // ✅ 新增：处理免费票据
  const processFreeTickets = async (orderId: string) => {
    try {
      // ✅ 获取用户的 JWT token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('User not authenticated');
      }

      const response = await fetch(
        'https://ertvopdjtokzcqsivuba.supabase.co/functions/v1/process-free-tickets',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            order_id: orderId,
            quantity: quantity,
          }),
        }
      );

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to process tickets');
      }

      // 成功处理
      setIsConfirmationVisible(false);

      Alert.alert(
        'Booking Successful! 🎉',
        `You have successfully reserved ${quantity} ticket(s) for ${event?.title}. Check your tickets in the ORDERS tab.`,
        [
          {
            text: 'View Tickets',
            onPress: () => navigation.navigate('Home', { screen: 'Orders' })
          },
          { text: 'OK', style: 'default' }
        ]
      );

      // 重新加载活动数据
      loadEvent(true);
      setQuantity(1);

    } catch (error: any) {
      console.error('Free ticket processing error:', error);
      Alert.alert(
        'Processing Error',
        error.message || 'Failed to process your free tickets. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // ✅ 新增：处理付费票据
  const processPaidTickets = async (orderId: string, totalAmount: number) => {
    // ✅ 保存订单ID，防止currentOrder状态被清空
    const payingOrderId = orderId;

    try {
      console.log('💳 Starting payment process for order:', payingOrderId);

      // ✅ 获取用户的 JWT token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('User not authenticated');
      }

      // ✅ 创建支付意向（包含订单ID）
      const response = await fetch(
        'https://ertvopdjtokzcqsivuba.supabase.co/functions/v1/create-payment-intent',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            order_id: payingOrderId,
            amount: Math.round(totalAmount * 100),
            currency: 'aud',
            description: `Tickets for ${event?.title}`,
          }),
        }
      );

      const { clientSecret, paymentIntentId, error: paymentError } = await response.json();

      if (paymentError) {
        throw new Error(paymentError);
      }

      // 更新订单的payment_intent_id
      await orderService.updateOrderPaymentIntentId(payingOrderId, paymentIntentId);

      // 初始化支付表单
      const { error: initError } = await initPaymentSheet({
        paymentIntentClientSecret: clientSecret,
        merchantDisplayName: 'Lezigo by INTHEPOND',
        returnURL: 'lezigo://stripe-redirect',
        applePay: {
          merchantCountryCode: 'AU', // 对应您使用的货币aud
        },
        googlePay: {
          merchantCountryCode: 'AU', // 对应您使用的货币aud
          currencyCode: 'AUD',
        }
      });

      if (initError) {
        throw new Error(initError.message);
      }

      console.log('💳 Presenting payment sheet...');

      // 显示支付表单
      const { error: presentError } = await presentPaymentSheet();

      console.log('💳 Payment sheet result:', { error: presentError, errorCode: presentError?.code, errorMessage: presentError?.message, hasError: !!presentError });

      if (presentError) {
        console.log('💳 Payment error occurred:', presentError);

        if (presentError.code === 'Canceled') {
          // ✅ 用户主动取消支付，立即取消预留
          console.log('💳 User cancelled payment, cancelling reservation immediately');
          console.log('🔍 Paying order ID:', payingOrderId);

          // ✅ 使用保存的订单ID
          if (payingOrderId) {
            await cancelReservation(payingOrderId);
          } else {
            console.log('⚠️ No order ID found to cancel reservation');
          }

          Alert.alert(
            'Payment Cancelled',
            'Your ticket reservation has been cancelled and the tickets are now available for others.',
            [{ text: 'OK', style: 'default' }]
          );
          return;
        }
        throw new Error(presentError.message);
      }

      // 支付成功
      console.log('💳 Payment successful');
      setIsConfirmationVisible(false);
      setIsPaymentSuccessVisible(true);

      // 重新加载活动数据
      loadEvent(true);
      setQuantity(1);

    } catch (error: any) {
      console.error('Payment error:', error);
      Alert.alert(
        'Payment Error',
        error.message || 'Payment failed. Your reservation is still active for 5 minutes.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // 导航到票证页面
  const navigateToTickets = () => {
    setIsPaymentSuccessVisible(false);
    navigation.navigate('Home', { screen: 'Orders' });
  };
  
  // 关闭支付成功模态框
  const closePaymentSuccess = () => {
    setIsPaymentSuccessVisible(false);
  };

  // 判断 session 是否可购买
  const isSessionAvailable = (session: any) => {
    const now = new Date();
    const sessionStartTime = new Date(session.start_time);
    return (
      session.status === 'available' &&
      session.available > 0 &&
      sessionStartTime > now
    );
  };

  // 判断活动是否已结束（所有场次都已过期）
  const isEventEnded = () => {
    if (!event?.sessions || event.sessions.length === 0) return false;
    return event.sessions.every(session => !isSessionAvailable(session));
  };

  // 对 sessions 进行分组和排序
  const groupAndSortSessions = (sessions: any[]) => {
    const now = new Date();
    return sessions.sort((a, b) => {
      // 首先按可购买状态分组
      const aAvailable = isSessionAvailable(a);
      const bAvailable = isSessionAvailable(b);
      if (aAvailable && !bAvailable) return -1;
      if (!aAvailable && bAvailable) return 1;
      
      // 然后按开始时间排序
      return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
    });
  };

  // 处理会话选择
  const handleSessionSelect = (session: ExtendedEventSession) => {
    if (session.status === 'available' && session.available > 0) {
      if (!authSession) {
        navigation.navigate('SignIn', {
          redirectScreen: 'EventDetails',
          redirectParams: {
            eventId: eventId
          }
        });
        return;
      }
      
      setSelectedSession(session);
      setIsConfirmationVisible(true);
    }
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    Animated.timing(blurAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  if (!event) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <LoadingAnimation text="Loading event details..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent backgroundColor="transparent" />
      <SafeAreaView style={[styles.fixedHeader, { marginTop: statusBarHeight }]}>
        <BackButton variant="floating" style={styles.backButton} />
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.favoriteButton, isLoading && styles.favoriteButtonDisabled]}
            onPress={handleFavoritePress}
            disabled={isLoading}
          >
            <Ionicons
              name={isFavorite ? "heart" : "heart-outline"}
              size={24}
              color={isFavorite ? COLORS.PRIMARY : "#fff"}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.refreshButton, isRefreshing && styles.refreshButtonDisabled]}
            onPress={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : refreshSuccess ? (
              <Ionicons
                name="checkmark"
                size={24}
                color={COLORS.PRIMARY}
              />
            ) : (
              <Ionicons
                name="refresh"
                size={24}
                color="#fff"
              />
            )}
          </TouchableOpacity>
        </View>
      </SafeAreaView>

      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <FastImage
            source={{ 
              uri: event?.image,
              priority: FastImage.priority.high,
              cache: FastImage.cacheControl.immutable,
            }}
            style={styles.headerImage}
            resizeMode={FastImage.resizeMode.cover}
            onLoadEnd={handleImageLoad}
          />
          {!imageLoaded && (
            <View style={[StyleSheet.absoluteFill, styles.blurContainer]}>
              <BlurView intensity={100} style={StyleSheet.absoluteFill} />
            </View>
          )}
          <Animated.View style={[
            StyleSheet.absoluteFill,
            {
              opacity: blurAnim.interpolate({
                inputRange: [0, 20],
                outputRange: [0, 1],
              }),
            }
          ]}>
            <BlurView intensity={100} style={StyleSheet.absoluteFill} />
          </Animated.View>
          <LinearGradient
            colors={['rgba(0,0,0,0.8)', 'transparent']}
            style={[StyleSheet.absoluteFill, styles.topGradient]}
          />
          <LinearGradient
            colors={['transparent', 'rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 0.95)']}
            start={{ x: 0, y: 0.2 }}
            end={{ x: 0, y: 1 }}
            style={[StyleSheet.absoluteFill, styles.bottomGradient]}
          />
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{event?.title}</Text>
          </View>
        </View>

        <View style={styles.content}>
          {/* Preview banner */}
          {(event as any).is_preview && (
            <View style={styles.previewBanner}>
              <View style={styles.previewIcon}>
                <Ionicons name="time-outline" size={20} color={COLORS.TEXT_WHITE} />
              </View>
              <View style={styles.previewInfo}>
                <Text style={styles.previewText}>Coming Soon</Text>
                <Text style={styles.previewSubtext}>Stay tuned for available sessions!</Text>
              </View>
            </View>
          )}

          {/* Event Ended banner */}
          {!((event as any).is_preview) && isEventEnded() && (
            <View style={styles.endedBanner}>
              <View style={styles.endedIcon}>
                <Ionicons name="checkmark-circle-outline" size={20} color={COLORS.TEXT_WHITE} />
              </View>
              <View style={styles.endedInfo}>
                <Text style={styles.endedText}>Event Ended</Text>
                <Text style={styles.endedSubtext}>This event has concluded. Thank you for your interest!</Text>
              </View>
            </View>
          )}
          
          <TouchableOpacity 
            style={styles.locationContainer}
            onPress={openMap}
            activeOpacity={0.7}
          >
            <View style={[styles.locationIcon, styles.locationIconPlaceholder]}>
              <Ionicons name="location-outline" size={20} color="#666" />
            </View>
            <View style={styles.locationInfo}>
              <Text style={styles.locationName}>{event.location}</Text>
              {event.fullAddress && (
                <Text style={styles.locationAddress}>{event.fullAddress}</Text>
              )}
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>

          {event.organizer && (
            <TouchableOpacity 
              style={styles.organizerContainer}
              onPress={() => navigation.navigate('OrganizerScreen', { organizerId: event.organizer!.id })}
              activeOpacity={0.7}
            >
              <Image
                source={event.organizer.avatar_url
                  ? { uri: event.organizer.avatar_url }
                  : require('../../assets/default-avatar-1.png')
                }
                style={styles.organizerAvatar}
                resizeMode="cover"
              />
              <View style={styles.organizerInfo}>
                <Text style={styles.organizerName}>{event.organizer.name}</Text>
                <Text style={styles.organizerRole}>{'Organizer'}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          )}

          {/* About Section - Text */}
          <Text style={[styles.sectionTitle, { marginBottom: SPACING.MEDIUM }]}>
            About This <Text style={{color: COLORS.PRIMARY}}>Event:</Text>
          </Text>
          <View style={styles.aboutTextContainer}>
            <Text style={styles.description}>
              {event.description || 'No description available.'}
            </Text>
          </View>

          {/* About Section - Images */}
          {event.description_images && event.description_images.length > 0 && (
            <View style={styles.aboutImagesContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.descriptionImagesScrollView}
              >
                {event.description_images.map((imageUrl, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.descriptionImageThumbnail}
                    onPress={() => {
                      setImageViewerIndex(index);
                      setImageViewerVisible(true);
                    }}
                    activeOpacity={0.8}
                  >
                    <Image
                      source={{ uri: imageUrl }}
                      style={styles.descriptionImage}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
          {/* End of About Section */}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Event <Text style={{color: COLORS.PRIMARY}}>Sessions:</Text>
            </Text>
            {event.sessions && event.sessions.length > 0 ? (
              <View>
                <View style={styles.sessionsList}>
                  {groupAndSortSessions(event.sessions).map((session, index) => {
                    const sessionDate = new Date(session.start_time);
                    const formattedDate = sessionDate.toLocaleDateString('en-US', { 
                      day: 'numeric',
                      month: 'short'
                    });
                    const formattedTime = sessionDate.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: 'numeric',
                      hour12: true
                    });
                    
                    const isAvailable = isSessionAvailable(session);
                    
                    // 如果未展开且不可购买，则不显示
                    if (!expandedSessions && !isAvailable) {
                      return null;
                    }

                    return (
                      <View key={session.id} style={[
                        styles.sessionCard,
                        !isAvailable && styles.sessionCardDisabled
                      ]}>
                        <View style={styles.sessionDate}>
                          <Text style={[
                            styles.sessionDateNumber,
                            !isAvailable && styles.textDisabled
                          ]}>
                            {formattedDate.split(' ')[1]}
                          </Text>
                          <Text style={[
                            styles.sessionDateMonth,
                            !isAvailable && styles.textDisabled
                          ]}>
                            {formattedDate.split(' ')[0]}
                          </Text>
                        </View>
                        <View style={styles.sessionInfo}>
                          <Text style={[
                            styles.sessionTitle,
                            !isAvailable && styles.textDisabled
                          ]}>{session.title}</Text>
                          <Text style={[
                            styles.sessionTime,
                            !isAvailable && styles.textDisabled
                          ]}>{formattedTime}</Text>
                          <Text style={[
                            styles.sessionPrice,
                            !isAvailable && styles.textDisabled
                          ]}>
                            {session.price ? `$${session.price}` : 'Free'}
                          </Text>

                          <Text style={[
                            styles.sessionCapacity,
                            !isAvailable && styles.textDisabled
                          ]}>
                            {session.status === 'pause' ? (
                              <Text style={styles.pausedText}>Paused</Text>
                            ) : !session.available || session.available <= 0 ? (
                              <Text style={styles.soldOutText}>Sold Out</Text>
                            ) : sessionDate < new Date() ? (
                              <Text style={styles.expiredText}>Expired</Text>
                            ) : (
                              `${session.available}/${session.capacity} Available`
                            )}
                          </Text>


                        </View>
                        <Button
                          title="Go"
                          style={[
                            styles.sessionButton,
                            !isAvailable && styles.sessionButtonDisabled
                          ]}
                          variant={!isAvailable ? 'secondary' : 'primary'}
                          disabled={!isAvailable}
                          onPress={() => {
                            if (isAvailable) {
                              // 检查用户是否已登录
                              if (!authSession) {
                                navigation.navigate('SignIn', {
                                  redirectScreen: 'EventDetails',
                                  redirectParams: {
                                    eventId: eventId
                                  }
                                });
                              } else {
                                setSelectedSession(session);
                                setIsConfirmationVisible(true);
                              }
                            }
                          }}
                        />
                      </View>
                    );
                  })}
                </View>
                {/* 检查是否有不可用的 Session */}
                {event.sessions.some(session => !isSessionAvailable(session)) && (
                  <TouchableOpacity 
                    style={styles.expandButton}
                    onPress={() => setExpandedSessions(!expandedSessions)}
                  >
                    <Text style={styles.expandButtonText}>
                      {expandedSessions ? 'Hide Unavailable Sessions' : 'Show Unavailable Sessions'}
                    </Text>
                    <Ionicons 
                      name={expandedSessions ? 'chevron-up' : 'chevron-down'} 
                      size={16} 
                      color={COLORS.PRIMARY}
                    />
                  </TouchableOpacity>
                )}
              </View>
            ) : (
              <View style={styles.noSessionsContainer}>
                <Ionicons name="calendar-outline" size={48} color="#666" />
                <Text style={styles.noSessionsTitle}>No Sessions Available</Text>
                <Text style={styles.noSessionsDescription}>Stay tuned! New sessions will be announced soon.</Text>
              </View>
            )}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Venue & <Text style={{color: COLORS.PRIMARY}}>Location:</Text>
            </Text>
            <TouchableOpacity 
              style={styles.mapContainer}
              onPress={openMap}
              activeOpacity={0.7}
            >
              {event.location ? (
                <Image
                  source={{ uri: getStaticMapUrl(event.fullAddress || event.location) }}
                  style={styles.mapImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.mapPlaceholder}>
                  <Ionicons name="map-outline" size={32} color="#666" />
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* 票证确认表单 */}
      {event && selectedSession && (
        <TicketConfirmationSheet
          isVisible={isConfirmationVisible}
          onClose={() => setIsConfirmationVisible(false)}
          event={event}
          session={selectedSession}
          onPurchase={handlePurchase}
          quantity={quantity}
          setQuantity={setQuantity}
          isProcessing={isProcessing}
        />
      )}
      
      {/* 支付成功模态框 */}
      {event && (
        <PaymentSuccessModal
          isVisible={isPaymentSuccessVisible}
          onClose={closePaymentSuccess}
          onViewTickets={navigateToTickets}
          eventTitle={event.title}
          ticketQuantity={quantity}
        />
      )}

      {/* 图片查看器 */}
      {event && event.description_images && (
        <ImageViewer
          visible={imageViewerVisible}
          images={event.description_images}
          initialIndex={imageViewerIndex}
          onClose={() => setImageViewerVisible(false)}
        />
      )}
    </View>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM
  },
  fixedHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 12
  },
  backButton: {
    ...Platform.select({
      ios: {
        marginHorizontal: 16,
      }
    }),
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  favoriteButton: {
    width: 48,
    height: 48,
    borderRadius: RADIUS.LARGE,
    backgroundColor: COLORS.TRANSPARENT_DARK,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        marginHorizontal: 0,
      }
    }),
  },
  favoriteButtonDisabled: {
    opacity: 0.5,
  },
  refreshButton: {
    width: 48,
    height: 48,
    borderRadius: RADIUS.LARGE,
    backgroundColor: COLORS.TRANSPARENT_DARK,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        marginRight: 16,
      }
    }),
  },
  refreshButtonDisabled: {
    opacity: 0.5,
  },
  scrollView: {
    flex: 1
  },
  header: {
    height: width * 0.9,
    position: 'relative'
  },
  headerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover'
  },
  topGradient: {
    height: '40%'
  },
  bottomGradient: {
    height: '70%',
    top: '30%'
  },
  titleContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20
  },
  title: {
    fontSize: FONT_SIZES.TITLE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: 0
  },
  content: {
    flex: 1,
    padding: 20,
    backgroundColor: COLORS.BACKGROUND_DARK
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM
  },
  locationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12
  },
  locationIconPlaceholder: {
    backgroundColor: '#2c2c2e',
    alignItems: 'center',
    justifyContent: 'center'
  },
  locationInfo: {
    flex: 1
  },
  locationName: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4
  },
  locationAddress: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY
  },
  organizerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM
  },
  organizerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12
  },
  organizerInfo: {
    flex: 1
  },
  organizerName: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4
  },
  organizerRole: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY
  },
  section: {
    marginBottom: 32
  },
  sectionTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: 16
  },
  description: {
    fontSize: FONT_SIZES.MEDIUM,
    lineHeight: 24,
    color: COLORS.TEXT_LIGHT
  },
  aboutTextContainer: {
    marginBottom: SPACING.MEDIUM,
    padding: SPACING.LARGE,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
  },
  aboutImagesContainer: {
    marginBottom: SPACING.LARGE,
    padding: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
  },
  sessionsList: {
    gap: 12
  },
  sessionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    gap: 16
  },
  sessionCardDisabled: {
    opacity: 0.85,
    backgroundColor: '#2a2a2c',
  },
  sessionDate: {
    alignItems: 'center',
    minWidth: 48
  },
  sessionDateNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE
  },
  sessionDateMonth: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    textTransform: 'uppercase'
  },
  sessionInfo: {
    flex: 1
  },
  sessionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4
  },
  sessionTime: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: 4
  },
  sessionPrice: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.PRIMARY,
    marginBottom: 4,
    fontWeight: '600'
  },
  sessionCapacity: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY
  },
  sessionButton: {
    paddingHorizontal: 24,
    backgroundColor: COLORS.PRIMARY
  },
  sessionButtonDisabled: {
    opacity: 0.9,
    backgroundColor: '#2a2a2c'
  },
  noSessionsContainer: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noSessionsTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: '600',
    color: COLORS.PRIMARY,
    marginTop: 16,
    marginBottom: 8,
  },
  noSessionsDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
  },
  mapContainer: {
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    backgroundColor: COLORS.BACKGROUND_CARD
  },
  mapImage: {
    width: '100%',
    height: 200,
    backgroundColor: COLORS.BACKGROUND_CARD
  },
  mapPlaceholder: {
    height: 200,
    backgroundColor: COLORS.BACKGROUND_CARD,
    alignItems: 'center',
    justifyContent: 'center'
  },
  textDisabled: {
    color: '#aaa',
  },
  soldOutText: {
    color: COLORS.ERROR,
    fontWeight: 'bold',
  },
  pausedText: {
    color: COLORS.WARNING,
    fontWeight: 'bold',
  },
  blurContainer: {
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.MEDIUM,
    marginTop: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  expandButtonText: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.SMALL,
    marginRight: SPACING.XSMALL,
    fontWeight: '600',
  },
  expiredText: {
    color: COLORS.TEXT_GRAY,
    fontWeight: 'bold',
  },
  previewBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  previewIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  previewInfo: {
    flex: 1,
  },
  previewText: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4,
  },
  previewSubtext: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.PRIMARY,
  },
  endedBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    padding: 16,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  endedIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  endedInfo: {
    flex: 1,
  },
  endedText: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4,
  },
  endedSubtext: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.PRIMARY,
  },

  descriptionImagesScrollView: {
    paddingHorizontal: 0,
  },
  descriptionImageThumbnail: {
    marginRight: SPACING.SMALL,
    borderRadius: RADIUS.SMALL,
    overflow: 'hidden',
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  descriptionImage: {
    width: 100,
    height: 100,
    borderRadius: RADIUS.SMALL,
  },
});
