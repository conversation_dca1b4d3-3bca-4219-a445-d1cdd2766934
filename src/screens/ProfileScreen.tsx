import { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Platform,
  // SafeAreaView,
  Alert,
  RefreshControl,
  StatusBar,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/navigation';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { checkVersion } from 'react-native-check-version';
import { TabParamList } from '../types/navigation';
import { CompositeNavigationProp } from '@react-navigation/native';
import { orderService } from '../services/orderService';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';
import { toastService } from '../services/toastService';
import Constants from 'expo-constants';
import LoadingAnimation from '../components/LoadingAnimation';
import { useAuth } from '../context/AuthContext';
import Button from '../components/Button';
import { LinearGradient } from 'expo-linear-gradient';
import { notificationService } from '../services/notificationService';
import { Notification } from '../types/notification';
import NotificationCarousel from '../components/NotificationCarousel';
import { platform } from 'os';

type ProfileScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<TabParamList, 'Profile'>,
  NativeStackNavigationProp<RootStackParamList>
>;

interface UserProfile {
  user_id: string;
  email: string;
  display_name: string | null;
  avatar_url: string | null;
  is_organizer: boolean;
  updated_at: string;
}

// 格式化时间函数
const formatTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// 格式化日期函数
const formatDate = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr);
  return date.toLocaleDateString([], { month: 'numeric', day: 'numeric', year: 'numeric' });
};

export default function ProfileScreen() {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { signOut, user: authUser, session } = useAuth();
  const scrollViewRef = useRef<ScrollView>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [tickets, setTickets] = useState<any[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [checkingUpdate, setCheckingUpdate] = useState(false);

  // 将onRefresh定义为useCallback
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([loadUserData(), loadNotifications()]);
    } finally {
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    if (session) {
      loadUserData();
      loadNotifications();
    } else {
      setLoading(false);
    }
  }, [session]);

  // 监听页面焦点变化，当从其他页面返回时刷新数据
  useFocusEffect(
    useCallback(() => {
      if (session) {
        loadUserData();
      }
    }, [session])
  );

  // 分离数据加载函数
  const loadUserProfile = async (userId: string) => {
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (profileError) throw profileError;
    return profileData;
  };

  const loadUserTickets = async (userId: string) => {
    const { data: ticketsData } = await orderService.getUserOrdersPaginated(userId, 0, 100);
    return ticketsData || [];
  };

  const loadUserData = async () => {
    try {
      setLoading(true);
      
      // 获取当前用户
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) {
        return;
      }
      setUser(user);

      // 并行加载用户资料和票据
      const [profileData, ticketsData] = await Promise.all([
        loadUserProfile(user.id),
        loadUserTickets(user.id)
      ]);

      setProfile(profileData);
      setTickets(ticketsData);

    } catch (error: any) {
      console.error('Error loading user data:', error);
      Alert.alert('Error', 'Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const loadNotifications = async () => {
    try {
      const notificationsData = await notificationService.getActiveNotifications();
      setNotifications(notificationsData);
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const handleAvatarPress = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions to upload an avatar');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const { fileSize } = result.assets[0];
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB (avatars bucket limit)

        if (fileSize && fileSize > MAX_FILE_SIZE) {
          Alert.alert('Image too large', 'The selected image exceeds the 5MB size limit.');
          return;
        }

        await uploadAvatar(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error selecting avatar:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const uploadAvatar = async (imageUri: string) => {
    if (!user) return;

    try {
      setUploadingAvatar(true);

      const response = await fetch(imageUri);
      const blob = await response.blob();

      const avatarUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = async () => {
          try {
            if (typeof reader.result === 'string') {
              const base64Data = reader.result.split(',')[1];
              const fileName = `avatar_${user.id}_${new Date().getTime()}.jpg`;

              const { error } = await supabase
                .storage
                .from('avatars')
                .upload(fileName, decode(base64Data), {
                  contentType: 'image/jpeg',
                  upsert: true
                });

              if (error) throw error;

              const { data: { publicUrl } } = supabase
                .storage
                .from('avatars')
                .getPublicUrl(fileName);

              resolve(publicUrl);
            } else {
              reject(new Error('Failed to convert image to base64'));
            }
          } catch (uploadError) {
            console.error('Error uploading avatar:', uploadError);
            reject(uploadError);
          }
        };
        reader.onerror = () => reject(new Error('Failed to read image'));
      });

      // Update user profile with new avatar URL
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (updateError) throw updateError;

      // Update local state
      setProfile(prev => prev ? { ...prev, avatar_url: avatarUrl } : null);

      Alert.alert('Success', 'Avatar updated successfully!');

    } catch (error) {
      console.error('Error uploading avatar:', error);
      Alert.alert('Error', 'Failed to upload avatar');
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              // 只调用 AuthContext 的 signOut 方法，它会处理完整的登出流程
              // 包括清理 push tokens 和调用 supabase.auth.signOut()
              await signOut();
            } catch (error: any) {
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const navigateToSignIn = () => {
    navigation.navigate('SignIn', {
      redirectScreen: 'Home',
      redirectParams: { screen: 'Profile' }
    });
  };

  const handleCheckForUpdates = async () => {
    setCheckingUpdate(true);
    try {
      const version = await checkVersion();

      if (version.needsUpdate) {
        toastService.showUpdateAvailable(version.version);

        // 延迟显示确认对话框，让用户先看到通知
        setTimeout(() => {
          Alert.alert(
            'Update Available',
            `A new version (${version.version}) is available. Would you like to update now?`,
            [
              {
                text: 'Later',
                style: 'cancel',
              },
              {
                text: 'Update',
                onPress: () => {
                  if (version.url) {
                    Linking.openURL(version.url);
                  }
                },
              },
            ]
          );
        }, 1000);
      } else {
        toastService.showUpToDate();
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
      toastService.show({
        title: 'Update Check Failed',
        message: 'Unable to check for updates. Please try again later.',
      });
    } finally {
      setCheckingUpdate(false);
    }
  };

  const renderLoginPrompt = () => (
    <LinearGradient
      colors={['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)']}
      style={styles.loginPromptContainer}
    >
      <View style={styles.loginContent}>
        <Ionicons 
          name="person-circle-outline" 
          size={120}
          color={COLORS.PRIMARY_LIGHT}
          style={styles.loginIcon} 
        />
        <Text style={styles.loginTitle}>Access Your Profile</Text>
        <Text style={styles.loginSubtitle}>
          Log in or sign up to manage your events, tickets, and favorites.
        </Text>
        <Button
          title="Login / Register"
          style={styles.loginButton}
          onPress={navigateToSignIn}
        />
      </View>
    </LinearGradient>
  );

  // 如果用户未登录，显示登录提示
  if (!session) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.container}>
          {renderLoginPrompt()}
        </View>
      </View>
    );
  }

  const renderProfileHeader = () => {
    // 计算已支付订单的票数总和
    const paidTicketsCount = tickets
      .filter(ticket => ticket.is_paid)
      .reduce((sum, ticket) => sum + ticket.quantity, 0);
    
    // 计算不同事件类型的数量
    const uniqueEvents = new Set(
      tickets
        .filter(ticket => ticket.is_paid)
        .map(ticket => ticket.event_sessions.event.event_id)
    );
    
    return (
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.avatarContainer}
          onPress={handleAvatarPress}
          disabled={uploadingAvatar}
        >
          {profile?.avatar_url ? (
            <Image
              source={{ uri: profile.avatar_url }}
              style={styles.avatar}
              resizeMode="cover"
            />
          ) : (
            <Image
              source={require('../../assets/default-avatar-1.png')}
              style={styles.avatar}
              resizeMode="cover"
            />
          )}

          {/* Upload indicator */}
          {uploadingAvatar && (
            <View style={styles.uploadingOverlay}>
              <ActivityIndicator size="large" color={COLORS.PRIMARY} />
            </View>
          )}

          {/* Camera icon overlay */}
          <View style={styles.cameraIconOverlay}>
            <Ionicons name="camera" size={20} color={COLORS.TEXT_WHITE} />
          </View>
        </TouchableOpacity>
        <View style={styles.nameContainer}>
          {profile?.display_name ? (
            <Text style={styles.displayName}>{profile.display_name}</Text>
          ) : (
            <Text style={styles.displayName}>User</Text>
          )}
          <TouchableOpacity
            style={styles.editNameIcon}
            onPress={() => navigation.navigate('EditProfile')}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="create-outline" size={16} color={COLORS.TEXT_GRAY} />
          </TouchableOpacity>
        </View>
        <Text style={styles.email}>{profile?.email}</Text>
        
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <View style={styles.statCard}>
              <View style={styles.statIconWrapper}>
                <Ionicons name="ticket-outline" size={20} color={COLORS.PRIMARY} />
              </View>
              <View style={styles.statContent}>
                <Text style={styles.statValue}>{paidTicketsCount}</Text>
                <Text style={styles.statLabel}>Tickets</Text>
              </View>
            </View>
            
            <View style={styles.statDivider} />
            
            <View style={styles.statCard}>
              <View style={styles.statIconWrapper}>
                <Ionicons name="calendar-outline" size={20} color={COLORS.PRIMARY} />
              </View>
              <View style={styles.statContent}>
                <Text style={styles.statValue}>{uniqueEvents.size}</Text>
                <Text style={styles.statLabel}>Events</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderNotificationsSection = () => {
    if (notifications.length === 0) return null;
    
    return (
      <View style={styles.notificationsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Notifications</Text>
        </View>
        <NotificationCarousel notifications={notifications} />
      </View>
    );
  };

  const renderOrganizerPortal = () => {
    if (!profile?.is_organizer) return null;
    
    return (
      <View style={styles.organizerSection}>
        <TouchableOpacity 
          style={styles.organizerButton} 
          onPress={() => navigation.navigate('OrganizerManagement')}
        >
          <LinearGradient
            colors={[COLORS.PRIMARY, 'rgba(231, 76, 38, 0.8)']}
            style={styles.organizerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <View style={styles.organizerContent}>
              <View style={styles.organizerIconContainer}>
                <Ionicons name="briefcase" size={28} color={COLORS.TEXT_WHITE} />
              </View>
              <View style={styles.organizerTextContainer}>
                <Text style={styles.organizerTitle}>Organizer Portal</Text>
                <Text style={styles.organizerSubtitle}>Manage your events and analytics</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_WHITE} />
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };

  const renderActionButtons = () => (
    <View style={styles.actions}>
      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={() => navigation.navigate('FavoriteEvents')}
      >
        <Ionicons name="heart-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>My Favorites</Text>
        <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={() => navigation.navigate('EditProfile')}
      >
        <Ionicons name="person-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>Account Settings</Text>
        <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={() => navigation.navigate('NotificationSettings')}
      >
        <Ionicons name="notifications-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>Notification Settings</Text>
        <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
      </TouchableOpacity>

      {!profile?.is_organizer && (
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={() => navigation.navigate('WebView', { url: 'https://forms.gle/VnVDs9ct1HY9msiM9' })}
        >
          <Ionicons name="business-outline" size={24} color={COLORS.PRIMARY} />
          <Text style={styles.actionText}>Apply to be an Organizer</Text>
          <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
        </TouchableOpacity>
      )}

      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={() => navigation.navigate('WebView', { url: 'https://support.inthepond.com.au/lezigo/terms-of-use' })}
      >
        <Ionicons name="document-text-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>Terms of Use</Text>
        <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('WebView', { url: 'https://support.inthepond.com.au/lezigo/privacy-policy' })}
      >
        <Ionicons name="shield-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>Privacy Policy</Text>
        <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.actionButton}
        onPress={handleCheckForUpdates}
        disabled={checkingUpdate}
      >
        <Ionicons name="refresh-outline" size={24} color={COLORS.PRIMARY} />
        <Text style={styles.actionText}>Check for Updates</Text>
        {checkingUpdate ? (
          <ActivityIndicator size="small" color={COLORS.PRIMARY} />
        ) : (
          <Ionicons name="chevron-forward" size={24} color={COLORS.TEXT_GRAY} />
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.actionButton, styles.logoutButton]}
        onPress={handleLogout}
      >
        <Ionicons name="log-out-outline" size={24} color={COLORS.ERROR} />
        <Text style={[styles.actionText, styles.logoutText]}>Sign Out</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.loadingContainer}>
          <LoadingAnimation text="Loading Profile..." />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <ScrollView
        ref={scrollViewRef}
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.PRIMARY}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {renderProfileHeader()}
        {renderNotificationsSection()}
        {renderOrganizerPortal()}
        {renderActionButtons()}
        
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Version {Constants.expoConfig?.version || '1.0.0'}</Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
  },
  scrollContent: {
    paddingTop: SPACING.MEDIUM,
    paddingBottom: 64,
  },
  header: {
    alignItems: 'center',
    paddingBottom: SPACING.LARGE,
    paddingTop: 60,
    // paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 40,
  },
  avatarContainer: {
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
    borderRadius: 55,
    padding: 3,
    marginBottom: SPACING.MEDIUM,
    position: 'relative',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraIconOverlay: {
    position: 'absolute',
    bottom: 1,
    right: 1,
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.BACKGROUND_DARK,
  },
  nameContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.XSMALL,
    paddingHorizontal: 40, // 为右侧图标留出空间
  },
  displayName: {
    fontSize: FONT_SIZES.XXLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    textAlign: 'center',
  },
  editNameIcon: {
    position: 'absolute',
    right: 0,
    paddingHorizontal: SPACING.SMALL,
    paddingVertical: SPACING.XSMALL,
    borderRadius: RADIUS.SMALL,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    opacity: 0.7,
  },
  email: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.MEDIUM,
  },
  actions: {
    padding: SPACING.LARGE,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_CARD,
    padding: SPACING.MEDIUM,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
  },
  actionText: {
    flex: 1,
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_WHITE,
    marginLeft: SPACING.MEDIUM,
  },
  logoutButton: {
    marginTop: SPACING.LARGE,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.ERROR,
  },
  logoutText: {
    color: COLORS.ERROR,
  },
  versionContainer: {
    paddingBottom: Platform.OS === 'android' ? 20 : 120,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  versionText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    opacity: 0.7,
  },
  loginPromptContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.LARGE,
  },
  loginContent: {
    alignItems: 'center',
    width: '90%',
  },
  loginIcon: {
    marginBottom: SPACING.LARGE,
    opacity: 0.9,
  },
  loginTitle: {
    fontSize: FONT_SIZES.XXLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    textAlign: 'center',
    marginBottom: SPACING.MEDIUM,
    letterSpacing: 0.5,
  },
  loginSubtitle: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
    textAlign: 'center',
    lineHeight: FONT_SIZES.MEDIUM * 1.5,
    marginBottom: SPACING.XLARGE,
  },
  loginButton: {
    width: '100%',
  },
  statsContainer: {
    width: '90%',
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    padding: SPACING.MEDIUM,
    marginTop: SPACING.MEDIUM,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_TRANSPARENT,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: SPACING.SMALL,
  },
  statCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.MEDIUM,
  },
  statIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.SMALL,
  },
  statContent: {
    alignItems: 'flex-start',
  },
  statValue: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
  },
  statDivider: {
    height: 40,
    width: 1,
    backgroundColor: COLORS.PRIMARY_TRANSPARENT,
  },
  notificationsSection: {
    paddingTop: SPACING.LARGE,
    paddingBottom: SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.LARGE,
    marginBottom: SPACING.MEDIUM,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  organizerSection: {
    paddingHorizontal: SPACING.LARGE,
    paddingTop: SPACING.MEDIUM,
    paddingBottom: SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  organizerButton: {
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  organizerGradient: {
    paddingVertical: SPACING.LARGE,
    paddingHorizontal: SPACING.LARGE,
  },
  organizerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  organizerIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.MEDIUM,
  },
  organizerTextContainer: {
    flex: 1,
  },
  organizerTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4,
  },
  organizerSubtitle: {
    fontSize: FONT_SIZES.SMALL,
    color: 'rgba(255, 255, 255, 0.9)',
  },
});