import { StyleSheet, Text, View, TouchableOpacity, Dimensions, Animated } from 'react-native';
import { Event } from '../types/event';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { favoriteService } from '../services/favoriteService';
import { useState, useEffect, useRef } from 'react';
import { COLORS, RADIUS, FONT_SIZES, SPACING } from '../config/theme';
import { useAuth } from '../context/AuthContext';
import { toastService } from '../services/toastService';
import FastImage from 'react-native-fast-image';
import { BlurView } from 'expo-blur';

// 定义卡片可接受的最小事件类型
type EventCardData = {
  event_id: string;
  title: string;
  image: string;
  location: string;
  price: number;
  is_preview?: boolean;
  sessions?: { start_time: string }[];
  organizer?: { display_name?: string | null };
  date?: string;
};

interface EventCardProps {
  event: EventCardData;
  onPress?: () => void;
  isFavorite?: boolean;
  onFavoriteChange?: (status: boolean) => void;
}

export default function EventCard({ 
  event, 
  onPress, 
  isFavorite = false, 
  onFavoriteChange 
}: EventCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { isLoggedIn } = useAuth();
  const [imageLoaded, setImageLoaded] = useState(false);
  const blurAnim = useRef(new Animated.Value(20)).current;

  const handleFavoritePress = async () => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      if (isFavorite) {
        await favoriteService.removeFavorite(event.event_id);
        onFavoriteChange?.(false);
        toastService.showFavoriteRemoved(event.title);
      } else {
        await favoriteService.addFavorite(event.event_id);
        onFavoriteChange?.(true);
        toastService.showFavoriteAdded(event.title);
      }
    } catch (error) {
      console.error('Failed to update favorite status:', error);
      toastService.show({
        title: 'Error',
        message: 'Failed to update favorite status. Please try again.',
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    Animated.timing(blurAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  // 判断场次是否可用
  const isSessionAvailable = (session: { start_time: string, status?: string }) => {
    const now = new Date();
    const sessionStartTime = new Date(session.start_time);
    return session.status !== 'pause' && sessionStartTime > now;
  };

  // 获取最早的可用场次时间
  const getEarliestAvailableSession = (sessions?: { start_time: string, status?: string }[]) => {
    if (!sessions || sessions.length === 0) return null;

    const availableSessions = sessions.filter(isSessionAvailable);
    if (availableSessions.length === 0) return null;

    return availableSessions.reduce((earliest, current) => {
      if (!earliest) return current;
      return new Date(current.start_time) < new Date(earliest.start_time) ? current : earliest;
    }, availableSessions[0]);
  };

  // 获取多个可用场次时间（最多显示3个）
  const getAvailableSessions = (sessions?: { start_time: string, status?: string }[], maxCount: number = 3) => {
    if (!sessions || sessions.length === 0) return [];

    const availableSessions = sessions.filter(isSessionAvailable);
    if (availableSessions.length === 0) return [];

    // 按时间排序，最早的在前
    const sortedSessions = availableSessions.sort((a, b) =>
      new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
    );

    // 返回最多maxCount个session
    return sortedSessions.slice(0, maxCount);
  };

  // 在 EventCard 组件内部
  const earliestSession = getEarliestAvailableSession(event.sessions);
  const availableSessions = getAvailableSessions(event.sessions);
  const hasAvailableSessions = availableSessions.length > 0;

  // 判断是否为已结束的活动（排除Coming Soon活动）
  const isEventEnded = !event.is_preview && !hasAvailableSessions;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.95}>
      <View style={styles.imageContainer}>
        <FastImage
          style={[styles.image, isEventEnded && styles.imageGrayscale]}
          source={{
            uri: event.image,
            priority: FastImage.priority.normal,
            cache: FastImage.cacheControl.immutable,
          }}
          resizeMode={FastImage.resizeMode.cover}
          onLoadEnd={handleImageLoad}
        />
        {!imageLoaded && (
          <View style={[styles.image, styles.blurContainer]}>
            <BlurView intensity={100} style={StyleSheet.absoluteFill} />
          </View>
        )}
        <Animated.View style={[
          StyleSheet.absoluteFill,
          {
            opacity: blurAnim.interpolate({
              inputRange: [0, 20],
              outputRange: [0, 1],
            }),
          }
        ]}>
          <BlurView intensity={100} style={StyleSheet.absoluteFill} />
        </Animated.View>
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageGradient}
        />
        {event.is_preview && (
          <View style={styles.comingSoonBadge}>
            <Text style={styles.comingSoonText}>Coming Soon</Text>
          </View>
        )}
        {isLoggedIn && (
          <TouchableOpacity 
            style={[styles.favoriteButton, isLoading && styles.favoriteButtonDisabled]}
            onPress={handleFavoritePress}
            disabled={isLoading}
          >
            <Ionicons 
              name={isFavorite ? "heart" : "heart-outline"} 
              size={24} 
              color={isFavorite ? COLORS.PRIMARY : COLORS.TEXT_WHITE} 
            />
          </TouchableOpacity>
        )}
        <View style={styles.sessionsContainer}>
          {hasAvailableSessions ? (
            availableSessions.reverse().map((session, index) => (
              <View key={index} style={styles.dateChip}>
                <Text style={styles.dateText}>
                  {formatDate(new Date(session.start_time))}
                </Text>
              </View>
            ))
          ) : isEventEnded ? (
            <View style={[styles.dateChip, styles.endedChip]}>
              <Text style={[styles.dateText, styles.endedText]}>Event Ended</Text>
            </View>
          ) : null}
        </View>
      </View>
      <View style={styles.content}>
        <View style={styles.titleRow}>
          <Text style={[styles.title, isEventEnded && styles.grayedText]} numberOfLines={2}>{event.title}</Text>
        </View>
        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            <Ionicons name="location-outline" size={16} color={isEventEnded ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
            <Text style={[styles.infoText, isEventEnded && styles.grayedText]} numberOfLines={1}>{event.location}</Text>
          </View>
          <View style={styles.infoRow}>
            <Ionicons name="person-outline" size={16} color={isEventEnded ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
            <Text style={[styles.infoText, isEventEnded && styles.grayedText]} numberOfLines={1}>
              {event.organizer?.display_name || 'Unknown Organizer'}
            </Text>
            <View style={[styles.priceContainer, isEventEnded && styles.grayedPriceContainer]}>
              <Text style={styles.price}>
                From A${event.price}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const { width } = Dimensions.get('window');
const cardWidth = width - 40; // 调整左右padding

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    overflow: 'hidden',
    width: cardWidth,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 10,
  },
  imageContainer: {
    position: 'relative',
    height: 130,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '50%',
  },
  content: {
    padding: 16,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  title: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    flex: 1,
    lineHeight: 28,
  },
  infoContainer: {
    gap: 6,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoText: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_LIGHT,
    flex: 1,
  },
  priceContainer: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: RADIUS.SMALL,
    marginLeft: 'auto',
  },
  price: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '700',
    color: COLORS.TEXT_WHITE,
  },
  favoriteButton: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: COLORS.TRANSPARENT_DARK,
    borderRadius: RADIUS.MEDIUM,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoriteButtonDisabled: {
    opacity: 0.5,
  },
  blurContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  sessionsContainer: {
    position: 'absolute',
    left: 12,
    bottom: 12,
    flexDirection: 'column',
    gap: 6,
  },
  dateChip: {
    backgroundColor: 'rgba(0,0,0,0.75)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: RADIUS.SMALL,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
  },
  dateText: {
    fontSize: FONT_SIZES.XSMALL,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 4,
  },
  comingSoonBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: RADIUS.SMALL,
    zIndex: 1,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
  },
  comingSoonText: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.XSMALL,
    fontWeight: 'bold',
  },
  imageGrayscale: {
    opacity: 0.6,
  },
  endedChip: {
    backgroundColor: 'rgba(128,128,128,0.8)',
    borderColor: 'rgba(128,128,128,0.3)',
  },
  endedText: {
    color: 'rgba(255,255,255,0.8)',
  },
  grayedText: {
    color: COLORS.TEXT_GRAY,
    opacity: 0.7,
  },
  grayedPriceContainer: {
    backgroundColor: COLORS.TEXT_GRAY,
  },
});