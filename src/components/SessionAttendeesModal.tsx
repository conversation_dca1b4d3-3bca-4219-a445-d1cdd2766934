import { StyleSheet, Text, View, Modal, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { useEffect, useState, useCallback } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '../config/theme';
import { orderService } from '../services/orderService';
import FastImage from 'react-native-fast-image';

interface SessionAttendeesModalProps {
  isVisible: boolean;
  onClose: () => void;
  sessionId: string;
  sessionTitle: string;
}

interface Attendee {
  user_id: string;
  display_name: string;
  avatar_url: string | null;
}

export default function SessionAttendeesModal({
  isVisible,
  onClose,
  sessionId,
  sessionTitle
}: SessionAttendeesModalProps) {
  // 状态管理
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // 加载参与者数据
  const loadAttendees = useCallback(async (isRefresh = false) => {
    if (!sessionId) return;
    
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // 获取所有参与者数据（不限制数量）
      const data = await orderService.getSessionAttendeesAvatars(sessionId, 100);
      setAttendees(data);
    } catch (err) {
      console.error('Failed to load attendees:', err);
      setError('Failed to load attendees. Please try again.');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  }, [sessionId]);

  // 当modal打开时加载数据
  useEffect(() => {
    if (isVisible) {
      loadAttendees();
    }
  }, [isVisible, loadAttendees]);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    loadAttendees(true);
  }, [loadAttendees]);

  // 渲染单个参与者项目
  const renderAttendeeItem = useCallback(({ item }: { item: Attendee }) => (
    <View style={styles.attendeeItem}>
      <FastImage
        source={
          item.avatar_url
            ? { uri: item.avatar_url, priority: FastImage.priority.normal }
            : require('../../assets/default-avatar-1.png')
        }
        style={styles.attendeeAvatar}
        resizeMode={FastImage.resizeMode.cover}
      />
      <Text style={styles.attendeeName} numberOfLines={1}>
        {item.display_name}
      </Text>
    </View>
  ), []);

  // 渲染空状态
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={48} color={COLORS.TEXT_GRAY} />
      <Text style={styles.emptyStateTitle}>No attendees yet</Text>
      <Text style={styles.emptyStateSubtitle}>Be the first to join this session!</Text>
    </View>
  );

  // 渲染错误状态
  const renderErrorState = () => (
    <View style={styles.errorState}>
      <Ionicons name="alert-circle-outline" size={48} color={COLORS.ERROR} />
      <Text style={styles.errorTitle}>Failed to load attendees</Text>
      <Text style={styles.errorSubtitle}>{error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={() => loadAttendees()}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="formSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
              >
                <Ionicons name="chevron-down" size={24} color="#fff" />
              </TouchableOpacity>
              <View style={styles.titleContainer}>
                <Text style={styles.title}>Who's Joining</Text>
                <Text style={styles.subtitle}>{sessionTitle}</Text>
              </View>
            </View>
            <View style={styles.divider} />
          </View>

          {/* Content */}
          <View style={styles.content}>
            {loading && !refreshing ? (
              <View style={styles.loadingState}>
                <ActivityIndicator size="large" color={COLORS.PRIMARY} />
                <Text style={styles.loadingText}>Loading attendees...</Text>
              </View>
            ) : error ? (
              renderErrorState()
            ) : attendees.length === 0 ? (
              renderEmptyState()
            ) : (
              <View style={styles.listContainer}>
                <FlatList
                  data={attendees}
                  renderItem={renderAttendeeItem}
                  keyExtractor={(item) => item.user_id}
                  numColumns={2}
                  columnWrapperStyle={styles.row}
                  showsVerticalScrollIndicator={false}
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  style={styles.flatList}
                  contentContainerStyle={styles.listContent}
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  initialNumToRender={8}
                  getItemLayout={(data, index) => ({
                    length: 80,
                    offset: 80 * Math.floor(index / 2),
                    index,
                  })}
                />
              </View>
            )}
          </View>
        </View>
      </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  header: {
    paddingTop: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    minHeight: 44,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    marginRight: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginTop: 2,
  },
  divider: {
    height: 1,
    backgroundColor: '#3c3c3e',
    marginHorizontal: 20,
  },
  content: {
    flex: 1,
    padding: 20,
  },

  // Loading state
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.TEXT_GRAY,
    marginTop: 16,
  },

  // Empty state
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
  },

  // Error state
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.ERROR,
    marginTop: 16,
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
  },

  // List container
  listContainer: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
    flexGrow: 1,
  },
  row: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },

  // Attendee item
  attendeeItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#2c2c2e',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 8,
    marginVertical: 6,
    maxWidth: '45%',
  },
  attendeeAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: 8,
    backgroundColor: '#3c3c3e',
  },
  attendeeName: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT_WHITE,
    textAlign: 'center',
  },
});
