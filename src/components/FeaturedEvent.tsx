import { StyleSheet, Text, View, TouchableOpacity, Dimensions, Animated } from 'react-native';
import { Event } from '../types/event';
import { LinearGradient } from 'expo-linear-gradient';
import Button from './Button';
import { COLORS, GRADIENTS, RADIUS, FONT_SIZES } from '../config/theme';
import FastImage from 'react-native-fast-image';
import { BlurView } from 'expo-blur';
import { useState, useRef } from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface FeaturedEventProps {
  event?: Event;
  onTicketPress?: () => void;
}

export default function FeaturedEvent({ event, onTicketPress }: FeaturedEventProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const blurAnim = useRef(new Animated.Value(20)).current;

  const handleImageLoad = () => {
    setImageLoaded(true);
    Animated.timing(blurAnim, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  // 如果没有特色活动，显示占位UI
  if (!event) {
    return (
      <View style={[styles.container]}>
        <View style={styles.background}>
          <View style={[StyleSheet.absoluteFill, styles.placeholderContainer]}>
            <LinearGradient
              colors={[COLORS.BACKGROUND_CARD, COLORS.BACKGROUND_DARK]}
              style={StyleSheet.absoluteFill}
            />
          </View>
          <LinearGradient
            colors={['transparent', 'rgba(0, 0, 0, 0.2)', 'rgba(0, 0, 0, 1)']}
            style={styles.gradient}
          >
            <View style={styles.content}>
              <View style={styles.placeholderIconContainer}>
                <Ionicons name="star-outline" size={50} color={COLORS.PRIMARY} />
              </View>
              <View style={styles.labelContainer}>
                <Text style={styles.label}>Featured Event</Text>
              </View>
              <Text style={styles.placeholderTitle}>More featured events coming soon</Text>
              <View style={styles.placeholderDescription}>
                <Text style={styles.placeholderText}>Stay tuned for exciting featured events</Text>
              </View>
              <Button 
                title="Explore Events"
                style={styles.button}
                variant="primary"
                disabled={true}
              />
            </View>
          </LinearGradient>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container]}>
      <View style={styles.background}>
        <FastImage
          style={StyleSheet.absoluteFill}
          source={{
            uri: event.image,
            priority: FastImage.priority.high,
            cache: FastImage.cacheControl.immutable,
          }}
          resizeMode={FastImage.resizeMode.cover}
          onLoadEnd={handleImageLoad}
        />
        {!imageLoaded && (
          <View style={[StyleSheet.absoluteFill, styles.blurContainer]}>
            <BlurView intensity={100} style={StyleSheet.absoluteFill} />
          </View>
        )}
        <Animated.View style={[
          StyleSheet.absoluteFill,
          {
            opacity: blurAnim.interpolate({
              inputRange: [0, 20],
              outputRange: [0, 1],
            }),
          }
        ]}>
          <BlurView intensity={100} style={StyleSheet.absoluteFill} />
        </Animated.View>
        <LinearGradient
          colors={['transparent', 'rgba(0, 0, 0, 0.2)', 'rgba(0, 0, 0, 1)']}
          style={styles.gradient}
        >
          <View style={styles.content}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>Featured Event</Text>
            </View>
            <Text style={styles.title}>{event.title}</Text>
            <Button 
              title="Get Tickets"
              style={styles.button}
              variant="primary"
              onPress={onTicketPress}
            />
          </View>
        </LinearGradient>
      </View>
    </View>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    width: width,
    height: width + 40,
  },
  background: {
    flex: 1,
    position: 'relative',
  },
  gradient: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  content: {
    padding: 24,
  },
  labelContainer: {
    backgroundColor: COLORS.PRIMARY,
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: RADIUS.ROUND,
    marginBottom: 12,
  },
  label: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.SMALL,
    fontWeight: '600',
  },
  title: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.TITLE,
    fontWeight: 'bold',
    marginBottom: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 5,
  },
  button: {
    width: '100%',
    backgroundColor: COLORS.PRIMARY,
  },
  blurContainer: {
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  shimmerEffect: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  // 占位样式
  placeholderContainer: {
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  placeholderIconContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  placeholderTitle: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.TITLE,
    fontWeight: 'bold',
    marginBottom: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 5,
  },
  placeholderDescription: {
    marginBottom: 24,
  },
  placeholderText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.MEDIUM,
    textAlign: 'center',
  },
}); 