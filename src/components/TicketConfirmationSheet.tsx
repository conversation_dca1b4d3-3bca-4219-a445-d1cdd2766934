import { StyleSheet, Text, View, Modal, TouchableOpacity, ScrollView, ActivityIndicator, Image } from 'react-native';
import { useEffect, useState } from 'react';
import { Event, ExtendedEventSession } from '../types/event';
import { Ionicons } from '@expo/vector-icons';
import Button from './Button';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '../config/theme';
import { feeService, FeeConfig } from '../services/feeService';
import { eventService } from '../services/eventService';
import { orderService } from '../services/orderService';
import FastImage from 'react-native-fast-image';
import SessionAttendeesModal from './SessionAttendeesModal';

interface TicketConfirmationSheetProps {
  isVisible: boolean;
  onClose: () => void;
  event: Event;
  session: ExtendedEventSession;
  onPurchase: (
    totalPrice: number,
    subtotal: number,
    serviceFee: number,
    appliedFeeConfig: FeeConfig | null
  ) => void;
  quantity: number;
  setQuantity: (quantity: number) => void;
  isProcessing: boolean;
}

export default function TicketConfirmationSheet({
  isVisible,
  onClose,
  event,
  session,
  onPurchase,
  quantity,
  setQuantity,
  isProcessing
}: TicketConfirmationSheetProps) {
  // 添加状态来存储最新的session信息和加载状态
  const [latestSession, setLatestSession] = useState<ExtendedEventSession>(session);
  const [loadingSession, setLoadingSession] = useState(false);
  const [feeConfig, setFeeConfig] = useState<FeeConfig | null>(null);
  const [loadingFeeConfig, setLoadingFeeConfig] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 添加参与者头像相关状态
  const [attendeeAvatars, setAttendeeAvatars] = useState<Array<{
    user_id: string;
    display_name: string;
    avatar_url: string | null;
  }>>([]);
  const [loadingAvatars, setLoadingAvatars] = useState(false);

  // 添加参与者modal状态
  const [showAttendeesModal, setShowAttendeesModal] = useState(false);

  // 加载最新的session信息和组织者费用配置
  useEffect(() => {
    if (isVisible) {
      const loadLatestData = async () => {
        setError(null);
        setLoadingSession(true);
        setLoadingFeeConfig(true);
        setLoadingAvatars(true);

        try {
          // 并行加载session信息、费用配置和参与者头像
          const [latestSessionData, feeConfigData, avatarsData] = await Promise.all([
            eventService.getSessionLatestInfo(session.id),
            event.organizer ? feeService.getOrganizerFeeConfig(event.organizer.id) : null,
            orderService.getSessionAttendeesAvatars(session.id, 15)
          ]);

          setLatestSession(latestSessionData);
          setFeeConfig(feeConfigData);
          setAttendeeAvatars(avatarsData);
        } catch (err) {
          console.error('Failed to load latest data:', err);
          setError('Failed to load latest ticket information. Please try again.');
        } finally {
          setLoadingSession(false);
          setLoadingFeeConfig(false);
          setLoadingAvatars(false);
        }
      };

      loadLatestData();
    }
  }, [isVisible, session.id, event.organizer]);

  // 使用最新的session价格
  const price = latestSession.price || 0;
  const subtotal = price * quantity;
  const serviceFee = feeConfig 
    ? feeService.calculateServiceFee(subtotal, feeConfig) 
    : feeService.calculateServiceFee(subtotal);
  const totalPrice = feeConfig 
    ? feeService.calculateTotalAmount(subtotal, feeConfig) 
    : feeService.calculateTotalAmount(subtotal);

  const formatPrice = (amount: number) => {
    return feeService.formatAmount(amount, feeConfig?.currency);
  };

  // 参与者头像组件
  const renderAttendeeAvatars = () => {
    if (loadingAvatars) {
      return (
        <View style={styles.attendeeAvatarsContainer}>
          <View style={styles.attendeeAvatarsRow}>
            <Text style={styles.attendeeAvatarsTitle}>Who's Joining</Text>
            <View style={styles.avatarsContainer}>
              <ActivityIndicator size="small" color={COLORS.TEXT_GRAY} />
              <Text style={styles.loadingText}>Loading...</Text>
            </View>
          </View>
        </View>
      );
    }

    if (attendeeAvatars.length === 0) {
      return (
        <View style={styles.attendeeAvatarsContainer}>
          <View style={styles.attendeeAvatarsRow}>
            <Text style={styles.attendeeAvatarsTitle}>Who's Joining</Text>
            <Text style={styles.noAttendeesText}>Be the first to join!</Text>
          </View>
        </View>
      );
    }

    const maxDisplayCount = 5;
    const displayAttendees = attendeeAvatars.slice(0, maxDisplayCount);
    const remainingCount = Math.max(0, attendeeAvatars.length - maxDisplayCount);

    return (
      <View style={styles.attendeeAvatarsContainer}>
        <View style={styles.attendeeAvatarsRow}>
          <Text style={styles.attendeeAvatarsTitle}>Who's Joining</Text>
          <TouchableOpacity
            style={styles.avatarsContainer}
            onPress={() => setShowAttendeesModal(true)}
            activeOpacity={0.7}
          >
            <View style={styles.avatarsRow}>
              {displayAttendees.map((attendee, index) => (
                <View
                  key={attendee.user_id}
                  style={[
                    styles.attendeeAvatar,
                    { marginLeft: index > 0 ? -8 : 0, zIndex: displayAttendees.length - index }
                  ]}
                >
                  <Image
                    source={
                      attendee.avatar_url
                        ? { uri: attendee.avatar_url }
                        : require('../../assets/default-avatar-1.png')
                    }
                    style={styles.avatarImage}
                    resizeMode="cover"
                  />
                </View>
              ))}
              {remainingCount > 0 && (
                <View style={[
                  styles.attendeeAvatar,
                  styles.remainingCountAvatar,
                  { marginLeft: -8, zIndex: 0 }
                ]}>
                  <Text style={styles.remainingCountText}>+{remainingCount}</Text>
                </View>
              )}
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const handleDecrease = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleIncrease = () => {
    if (quantity < maxAvailableTickets) {
      setQuantity(quantity + 1);
    }
  };

  // 设置默认的最大购票数量，使用最新session数据
  const maxTicketsPerUser = latestSession.max_tickets_per_user && latestSession.max_tickets_per_user > 0 
    ? latestSession.max_tickets_per_user 
    : 10;
  // 计算实际可购买的最大数量（用户限制和剩余票数中的较小值）
  const maxAvailableTickets = Math.min(maxTicketsPerUser, latestSession.available);

  // 按钮应该在以下情况下禁用：
  // 1. 正在处理付款
  // 2. 正在加载session信息或费用配置
  // 3. 没有可用票数
  // 4. 加载出错
  const isButtonDisabled = isProcessing || loadingSession || loadingFeeConfig || latestSession.available <= 0 || error !== null;

  // 按钮文本根据状态变化
  const getButtonText = () => {
    if (isProcessing) return "Processing...";
    if (loadingSession || loadingFeeConfig) return "Loading latest ticket information...";
    if (error) return "Error loading ticket information";
    if (latestSession.available <= 0) return "Sold Out";
    if (totalPrice === 0) return "Confirm Free Reservation";
    return "Proceed to Payment";
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="formSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
                disabled={isProcessing}
              >
                <Ionicons name="chevron-down" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.title}>Confirm Ticket</Text>
            </View>
            <View style={styles.divider} />
          </View>

          <ScrollView style={styles.scrollView}>
            <View style={styles.content}>
              {error ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                  <Button
                    title="Try Again"
                    onPress={() => {
                      if (isVisible) {
                        setError(null);
                        setLoadingSession(true);
                        setLoadingFeeConfig(true);
                        setLoadingAvatars(true);
                        // 重新加载数据
                        Promise.all([
                          eventService.getSessionLatestInfo(session.id),
                          event.organizer ? feeService.getOrganizerFeeConfig(event.organizer.id) : null,
                          orderService.getSessionAttendeesAvatars(session.id, 15)
                        ]).then(([latestSessionData, feeConfigData, avatarsData]) => {
                          setLatestSession(latestSessionData);
                          setFeeConfig(feeConfigData);
                          setAttendeeAvatars(avatarsData);
                        }).catch(err => {
                          console.error('Failed to reload data:', err);
                          setError('Failed to load latest ticket information. Please try again.');
                        }).finally(() => {
                          setLoadingSession(false);
                          setLoadingFeeConfig(false);
                          setLoadingAvatars(false);
                        });
                      }
                    }}
                    style={styles.tryAgainButton}
                  />
                </View>
              ) : (
                <>
                  <View style={styles.eventCard}>
                    <FastImage 
                      source={{ 
                        uri: event.image,
                        priority: FastImage.priority.high,
                        cache: FastImage.cacheControl.immutable
                      }} 
                      style={styles.eventImage}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                    <LinearGradient
                      colors={['transparent', 'rgba(0,0,0,0.8)']}
                      style={StyleSheet.absoluteFill}
                    />
                    <View style={styles.eventInfo}>
                      <Text style={styles.eventTitle}>{event.title}</Text>
                      <Text style={styles.sessionTitle}>{latestSession.title}</Text>
                      <View style={styles.dateTimeContainer}>
                        <View style={styles.infoRow}>
                          <Ionicons name="calendar-outline" size={16} color="#999" />
                          <Text style={styles.infoText}>
                            {new Date(latestSession.start_time).toLocaleDateString()}
                          </Text>
                        </View>
                        <View style={styles.infoRow}>
                          <Ionicons name="time-outline" size={16} color="#999" />
                          <Text style={styles.infoText}>
                            {new Date(latestSession.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* 参与者头像显示区域 */}
                  {renderAttendeeAvatars()}

                  <View style={styles.quantitySelector}>
                    <View style={styles.sectionHeader}>
                      <Text style={styles.sectionTitle}>Select Quantity</Text>
                      {latestSession.available > 0 ? (
                        <Text style={styles.limitText}>Max {maxAvailableTickets}</Text>
                      ) : (
                        <Text style={styles.soldOutText}>Sold Out</Text>
                      )}
                    </View>
                    {latestSession.available > 0 && latestSession.available < maxTicketsPerUser && (
                      <Text style={styles.availableText}>{latestSession.available} tickets available</Text>
                    )}
                    {latestSession.available <= 0 && (
                      <Text style={styles.soldOutMessage}>Sorry, this session is sold out. Please check other available sessions.</Text>
                    )}

                    {latestSession.available > 0 ? (
                      <View style={styles.quantityControls}>
                        <TouchableOpacity 
                          style={[styles.quantityButton, (quantity <= 1 || isProcessing) && styles.quantityButtonDisabled]} 
                          onPress={handleDecrease}
                          disabled={quantity <= 1 || isProcessing}
                        >
                          <Text style={[styles.quantityButtonText, (quantity <= 1 || isProcessing) && styles.quantityButtonTextDisabled]}>-</Text>
                        </TouchableOpacity>
                        <Text style={styles.quantityText}>{quantity}</Text>
                        <TouchableOpacity 
                          style={[styles.quantityButton, (quantity >= maxAvailableTickets || isProcessing) && styles.quantityButtonDisabled]} 
                          onPress={handleIncrease}
                          disabled={quantity >= maxAvailableTickets || isProcessing}
                        >
                          <Text style={[styles.quantityButtonText, (quantity >= maxAvailableTickets || isProcessing) && styles.quantityButtonTextDisabled]}>+</Text>
                        </TouchableOpacity>
                      </View>
                    ) : null}
                  </View>

                  <View style={styles.priceBreakdown}>
                    <Text style={styles.sectionTitle}>Price Details</Text>
                    <View style={styles.priceRow}>
                      <Text style={styles.priceLabel}>Ticket Price (x{quantity})</Text>
                      <Text style={styles.priceValue}>
                        {loadingSession ? (
                          <ActivityIndicator size="small" color={COLORS.TEXT_GRAY} />
                        ) : (
                          formatPrice(subtotal)
                        )}
                      </Text>
                    </View>
                    <View style={styles.priceRow}>
                      <Text style={styles.priceLabel}>Service & Payment Fee</Text>
                      <Text style={styles.priceValue}>
                        {(loadingSession || loadingFeeConfig) ? (
                          <ActivityIndicator size="small" color={COLORS.TEXT_GRAY} />
                        ) : (
                          formatPrice(serviceFee)
                        )}
                      </Text>
                    </View>
                    <View style={styles.priceRow}>
                      <Text style={styles.priceLabel}>GST</Text>
                      <Text style={styles.priceValue}>Included</Text>
                    </View>
                    <View style={styles.divider} />
                    <View style={styles.totalRow}>
                      <Text style={styles.totalLabel}>Total</Text>
                      <Text style={styles.totalValue}>
                        {(loadingSession || loadingFeeConfig) ? (
                          <ActivityIndicator size="small" color={COLORS.TEXT_WHITE} />
                        ) : (
                          formatPrice(totalPrice)
                        )}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.refundPolicy}>
                    <View style={styles.refundPolicyContent}>
                      <Ionicons name="information-circle-outline" size={20} color={COLORS.TEXT_GRAY} />
                      <Text style={styles.refundPolicyText}>
                        Tickets are non-refundable.
                      </Text>
                    </View>
                  </View>
                </>
              )}
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <Button
              title={getButtonText()}
              onPress={() => {
                if (!isButtonDisabled) {
                  onPurchase(totalPrice, subtotal, serviceFee, feeConfig);
                }
              }}
              style={styles.purchaseButton}
              disabled={isButtonDisabled}
              icon={
                (isProcessing || loadingSession || loadingFeeConfig)
                  ? () => <ActivityIndicator size="small" color="#fff" style={{marginRight: 8}} />
                  : undefined
              }
            />
          </View>
        </View>

        {/* 参与者详情Modal */}
        <SessionAttendeesModal
          isVisible={showAttendeesModal}
          onClose={() => setShowAttendeesModal(false)}
          sessionId={session.id}
          sessionTitle={latestSession.title}
        />
      </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  scrollView: {
    flexGrow: 1,
  },
  header: {
    paddingTop: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 12,
    height: 44,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    padding: 20,
    paddingBottom: 20,
  },
  eventCard: {
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 24,
    backgroundColor: '#000',
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  eventInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: 4,
  },
  sessionTitle: {
    fontSize: 16,
    color: COLORS.TEXT_WHITE,
    marginBottom: 12,
    opacity: 0.9,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  infoText: {
    fontSize: 14,
    color: '#999',
  },
  priceBreakdown: {
    backgroundColor: '#2c2c2e',
    borderRadius: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 16,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  priceLabel: {
    fontSize: 16,
    color: '#999',
  },
  priceValue: {
    fontSize: 16,
    color: COLORS.TEXT_WHITE,
  },
  divider: {
    height: 1,
    backgroundColor: '#3c3c3e',
    marginVertical: 12,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  footer: {
    padding: 20,
    backgroundColor: '#2c2c2e',
  },
  purchaseButton: {
    width: '100%',
    height: 36,
    marginBottom: 20,
  },
  quantitySelector: {
    backgroundColor: '#2c2c2e',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3c3c3e',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonDisabled: {
    backgroundColor: '#2c2c2e',
    opacity: 0.5,
  },
  quantityButtonText: {
    fontSize: 24,
    color: '#fff',
    fontWeight: '600',
  },
  quantityButtonTextDisabled: {
    color: '#666',
  },
  quantityText: {
    fontSize: 20,
    color: COLORS.TEXT_WHITE,
    fontWeight: '600',
    marginHorizontal: 24,
    minWidth: 30,
    textAlign: 'center',
  },
  limitText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availableText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
  soldOutText: {
    fontSize: 14,
    color: '#FF3B30',
    fontWeight: 'bold',
  },
  soldOutMessage: {
    fontSize: 14,
    color: '#FF3B30',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    color: COLORS.ERROR,
    textAlign: 'center',
    marginBottom: 16,
  },
  tryAgainButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  refundPolicy: {
    backgroundColor: '#2c2c2e',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
  },
  refundPolicyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  refundPolicyText: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    flex: 1,
    lineHeight: 20,
  },

  // 参与者头像样式
  attendeeAvatarsContainer: {
    backgroundColor: '#2c2c2e',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  attendeeAvatarsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  attendeeAvatarsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    flex: 0,
  },
  avatarsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 32,
    flex: 1,
    justifyContent: 'flex-end',
  },
  avatarsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 32,
  },
  attendeeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#2c2c2e',
    overflow: 'hidden',
    backgroundColor: '#2c2c2e',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  remainingCountAvatar: {
    backgroundColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  remainingCountText: {
    fontSize: 12,
    fontWeight: '700',
    color: COLORS.TEXT_WHITE,
  },
  noAttendeesText: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    fontStyle: 'italic',
    textAlign: 'right',
    flex: 1,
  },
  loadingText: {
    fontSize: 14,
    color: COLORS.TEXT_GRAY,
    marginLeft: 8,
  },
});