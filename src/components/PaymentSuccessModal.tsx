import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
  Clipboard,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING, RADIUS } from '../config/theme';
import LoadingAnimation from './LoadingAnimation';
import Button from './Button';



interface PaymentSuccessModalProps {
  isVisible: boolean;
  onClose: () => void;
  onViewTickets: () => void;
  eventTitle: string;
  ticketQuantity: number;
}

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({
  isVisible,
  onClose,
  onViewTickets,
  eventTitle,
  ticketQuantity
}) => {
  const [status, setStatus] = useState<'verifying' | 'success'>('verifying');
  const wechatId = 'LEZIGOAU';
  const screenHeight = Dimensions.get('window').height;
  const modalYPosition = useRef(new Animated.Value(screenHeight)).current;
  const checkmarkScale = useRef(new Animated.Value(0)).current;
  const checkmarkOpacity = useRef(new Animated.Value(0)).current;

  const handleWeChatPress = () => {
    // 复制微信号到剪贴板
    Clipboard.setString(wechatId);

    // 提示用户手动打开微信添加好友
    Alert.alert(
      'WeChat ID Copied',
      `WeChat ID: ${wechatId}\n\nThe WeChat ID has been copied to your clipboard.\n\nPlease open WeChat manually and search for this ID to add as friend.`,
      [{ text: 'OK' }]
    );
  };


  
  useEffect(() => {
    if (isVisible) {
      setStatus('verifying');
      modalYPosition.setValue(screenHeight); // Reset position before animating
      checkmarkScale.setValue(0);
      checkmarkOpacity.setValue(0);

      Animated.spring(modalYPosition, {
        toValue: 0,
        useNativeDriver: true,
        tension: 50,
        friction: 9,
      }).start();

      const timer = setTimeout(() => {
        setStatus('success');
      }, 3000);

      return () => clearTimeout(timer);
    } else {
      Animated.timing(modalYPosition, {
        toValue: screenHeight,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible, screenHeight, modalYPosition, checkmarkScale, checkmarkOpacity]);

  useEffect(() => {
    if (status === 'success' && isVisible) {
      Animated.parallel([
        Animated.spring(checkmarkScale, {
          toValue: 1,
          useNativeDriver: true,
          tension: 70,
          friction: 7,
        }),
        Animated.timing(checkmarkOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [status, isVisible, checkmarkScale, checkmarkOpacity]);
  
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none" // Use "none" as we control animation with Animated.View
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              transform: [{ translateY: modalYPosition }],
            },
          ]}
        >
          <View style={styles.contentContainer}>
            {status === 'verifying' ? (
              <>
                <LoadingAnimation text="Confirming your payment..." size="large" />
                <Text style={styles.verifyingText}>
                  Please wait while we confirm your payment and generate your tickets...
                </Text>
              </>
            ) : (
              <View style={styles.successContainer}>
                <View style={styles.checkmarkCircle}>
                  <Animated.View style={[
                    styles.checkmarkContainer,
                    {
                      transform: [{ scale: checkmarkScale }],
                      opacity: checkmarkOpacity
                    }
                  ]}>
                    <Ionicons name="checkmark" size={60} color={COLORS.PRIMARY} />
                  </Animated.View>
                </View>
                <Text style={styles.successTitle}>Payment Successful!</Text>
                <Text style={styles.successDescription}>
                  You have successfully purchased {ticketQuantity} ticket{ticketQuantity > 1 ? 's' : ''} for {eventTitle}.
                </Text>

                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>Community Support</Text>

                  <TouchableOpacity
                    style={styles.wechatButton}
                    onPress={handleWeChatPress}
                  >
                    <Ionicons name="logo-wechat" size={20} color="#fff" />
                    <Text style={styles.wechatButtonText}>Add WeChat Friend: LEZIGOAU</Text>
                  </TouchableOpacity>
                </View>
                
                <View style={styles.buttonsContainer}>
                  <Button 
                    title="View My Tickets"
                    onPress={onViewTickets}
                    style={styles.viewTicketsButton}
                  />
                  <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                    <Text style={styles.closeButtonText}>Return to Event</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end', // Align modal to the bottom
    alignItems: 'center',
  },
  container: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    width: '100%', // Full width
    borderTopLeftRadius: 20, // Card-like top radius
    borderTopRightRadius: 20, // Card-like top radius
    overflow: 'hidden',
    elevation: 8, // Enhanced shadow for "质感"
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 }, // Shadow for bottom sheet
    shadowOpacity: 0.15,
    shadowRadius: 5,
  },
  contentContainer: {
    padding: SPACING.LARGE,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300,
  },
  verifyingText: {
    textAlign: 'center',
    marginTop: SPACING.LARGE,
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.SMALL,
    lineHeight: 22,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  checkmarkCircle: {
    width: 100,
    height: 100,
    borderRadius: RADIUS.ROUND,
    backgroundColor: 'rgba(231, 76, 38, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.LARGE,
  },
  checkmarkContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  successTitle: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  successDescription: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
    textAlign: 'center',
    marginBottom: SPACING.LARGE,
    lineHeight: 24,
  },
  section: {
    width: '100%',
    paddingHorizontal: 10,
    marginBottom: SPACING.LARGE,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
    textAlign: 'center',
  },


  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  viewTicketsButton: {
    width: '100%',
    marginBottom: SPACING.MEDIUM,
  },
  closeButton: {
    padding: SPACING.MEDIUM,
  },
  closeButtonText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '500',
  },
  wechatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#07C160',
    paddingVertical: SPACING.MEDIUM,
    paddingHorizontal: SPACING.LARGE,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
  },
  wechatButtonText: {
    color: '#fff',
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    marginLeft: SPACING.SMALL,
  },
});

export default PaymentSuccessModal;