import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Clipboard,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SPACING, RADIUS, FONT_SIZES } from '../config/theme';

const { width } = Dimensions.get('window');
const MODAL_WIDTH = width * 0.85;

interface SupportModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function SupportModal({ isVisible, onClose }: SupportModalProps) {
  const supportEmail = '<EMAIL>';
  const wechatId = 'LEZIGOAU';

  const handleCopyEmail = () => {
    Clipboard.setString(supportEmail);
    Alert.alert('Success', 'Email address copied to clipboard');
  };

  const handleEmailPress = () => {
    Linking.openURL(`mailto:${supportEmail}`);
  };

  const handleWeChatPress = () => {
    // 复制微信号到剪贴板
    Clipboard.setString(wechatId);

    // 提示用户手动打开微信添加好友
    Alert.alert(
      'WeChat ID Copied',
      `WeChat ID: ${wechatId}\n\nThe WeChat ID has been copied to your clipboard.\n\nPlease open WeChat manually and search for this ID to add as friend.`,
      [{ text: 'OK' }]
    );
  };



  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.closeArea}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Contact Support</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <Text style={styles.description}>
            Have questions about your order or want to join our community? Contact us through:
          </Text>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Email Support</Text>
            <View style={styles.emailWrapper}>
              <TouchableOpacity 
                style={styles.emailContainer}
                onPress={handleEmailPress}
              >
                <Text style={styles.email} numberOfLines={1} adjustsFontSizeToFit>{supportEmail}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.copyButton}
                onPress={handleCopyEmail}
              >
                <Ionicons name="copy-outline" size={24} color={COLORS.PRIMARY} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Community Support</Text>

            <TouchableOpacity
              style={styles.wechatButton}
              onPress={handleWeChatPress}
            >
              <Ionicons name="logo-wechat" size={20} color="#fff" />
              <Text style={styles.wechatButtonText}>Add WeChat Friend: LEZIGOAU</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    width: MODAL_WIDTH,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.LARGE,
    padding: SPACING.LARGE,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.LARGE,
  },
  title: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  closeButton: {
    padding: SPACING.SMALL,
  },
  description: {
    fontSize: FONT_SIZES.MEDIUM,
    color: COLORS.TEXT_LIGHT,
    marginBottom: SPACING.LARGE,
    lineHeight: 22,
  },
  section: {
    marginBottom: SPACING.LARGE,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.MEDIUM,
  },
  emailWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(231, 76, 38, 0.1)',
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 1,
    borderColor: 'rgba(231, 76, 38, 0.2)',
  },
  emailContainer: {
    flex: 1,
    paddingVertical: SPACING.MEDIUM,
    paddingLeft: SPACING.MEDIUM,
  },
  email: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.MEDIUM,
  },
  copyButton: {
    padding: SPACING.MEDIUM,
  },


  wechatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#07C160',
    paddingVertical: SPACING.MEDIUM,
    paddingHorizontal: SPACING.LARGE,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.MEDIUM,
  },
  wechatButtonText: {
    color: '#fff',
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    marginLeft: SPACING.SMALL,
  },
});