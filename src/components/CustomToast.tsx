import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, RADIUS, SPACING } from '../config/theme';
import { BaseToast } from 'react-native-toast-message';

// 简化的自定义通知
export const CustomToast = (props: any) => (
  <BaseToast
    {...props}
    style={styles.toast}
    contentContainerStyle={styles.contentContainer}
    text1Style={styles.text1}
    text2Style={styles.text2}
    renderLeadingIcon={() => null}
    renderTrailingIcon={() => null}
  />
);

const styles = StyleSheet.create({
  toast: {
    borderLeftWidth: 0,
    borderRadius: RADIUS.MEDIUM,
    height: 60,
    marginHorizontal: SPACING.MEDIUM,
    marginBottom: 60, // 向上移动，避免与底部导航栏重叠
    paddingHorizontal: SPACING.MEDIUM,
    backgroundColor: COLORS.TEXT_WHITE,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_LIGHT,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contentContainer: {
    paddingHorizontal: 0,
    flex: 1,
    justifyContent: 'center',
  },
  text1: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 2,
  },
  text2: {
    fontSize: FONT_SIZES.SMALL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
});

// Toast 配置 - 所有类型都使用同一个组件
export const toastConfig = {
  success: CustomToast,
  error: CustomToast,
  warning: CustomToast,
  info: CustomToast,
};
