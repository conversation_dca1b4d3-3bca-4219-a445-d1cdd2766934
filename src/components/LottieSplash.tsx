import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import LottieView from 'lottie-react-native';

interface LottieSplashProps {
  onAnimationFinish: () => void;
}

const { width, height } = Dimensions.get('window');

const LottieSplash: React.FC<LottieSplashProps> = ({ onAnimationFinish }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onAnimationFinish();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      <LottieView
        source={require('../../assets/splash-screen/Lezigo-splash.json')}
        autoPlay
        loop={false}
        onAnimationFinish={onAnimationFinish}
        style={styles.animation}
        renderMode="HARDWARE"
        resizeMode="cover"
        speed={1.0}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  animation: {
    width: width,
    height: height,
  },
});

export default LottieSplash; 