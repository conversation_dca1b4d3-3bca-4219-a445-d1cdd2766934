import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { COLORS, RADIUS } from '../config/theme';

interface BackButtonProps {
  onPress?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'floating' | 'floatingOpaque';
}

const BackButton: React.FC<BackButtonProps> = ({ onPress, style, variant = 'default' }) => {
  const navigation = useNavigation();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <TouchableOpacity
      style={[styles.base, styles[variant], style]}
      onPress={handlePress}
    >
      <Ionicons name="chevron-back" size={24} color={COLORS.TEXT_WHITE} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    width: 64,
    height: 48,
    borderRadius: RADIUS.LARGE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  default: {
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
  floating: {
    backgroundColor: COLORS.TRANSPARENT_DARK,
  },
  floatingOpaque: {
    backgroundColor: COLORS.BACKGROUND_CARD,
  },
});

export default BackButton;
