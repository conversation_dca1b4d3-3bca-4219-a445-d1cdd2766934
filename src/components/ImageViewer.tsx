import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Dimensions,
} from 'react-native';
import ImageViewing from 'react-native-image-viewing';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../config/theme';

const { height: screenHeight } = Dimensions.get('window');

interface ImageViewerProps {
  visible: boolean;
  images: string[];
  initialIndex?: number;
  onClose: () => void;
}

export default function ImageViewer({ visible, images, initialIndex = 0, onClose }: ImageViewerProps) {
  // 转换图片数组格式以适配react-native-image-viewing
  const imageViewingImages = images.map(uri => ({ uri }));

  // 自定义头部组件 - 只保留关闭按钮
  const HeaderComponent = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Ionicons name="close" size={24} color={COLORS.TEXT_WHITE} />
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.5)" />
      <ImageViewing
        images={imageViewingImages}
        imageIndex={initialIndex}
        visible={visible}
        onRequestClose={onClose}
        HeaderComponent={HeaderComponent}
        backgroundColor={COLORS.BACKGROUND_CARD}
        swipeToCloseEnabled={true}
        doubleTapToZoomEnabled={true}
        presentationStyle="formSheet"
        animationType="slide"
        FooterComponent={() => <View style={styles.footer} />}
      />
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    position: 'absolute', // 绝对定位确保始终在最上层
    top: 24,
    left: 20,
    right: 0,
    paddingBottom: 12,
    backgroundColor: 'transparent', // 透明背景
    zIndex: 9999, // 极高的z-index确保始终在最上层
    elevation: 9999, // Android确保在最上层
    pointerEvents: 'box-none', // 允许点击穿透到图片，但按钮仍可点击
  },
  closeButton: {
    width: 64,
    height: 48,
    borderRadius: 24, // RADIUS.LARGE 的值
    backgroundColor: COLORS.TRANSPARENT_DARK, // 与BackButton floating样式一致
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'auto', // 确保按钮可以接收点击事件
    shadowColor: '#000', // 添加阴影增强可见性
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5, // Android阴影
  },
  footer: {
    height: screenHeight * 0.1, // 10%的底部空间，使内容占90%
    backgroundColor: 'transparent',
  },
});
