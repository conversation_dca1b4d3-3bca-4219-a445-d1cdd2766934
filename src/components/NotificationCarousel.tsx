import { StyleSheet, Text, View, Animated, Dimensions, TouchableOpacity } from 'react-native';
import { useEffect, useRef, useState } from 'react';
import { COLORS, FONT_SIZES, RADIUS } from '../config/theme';
import { Ionicons } from '@expo/vector-icons';
import { Notification, NotificationType } from '../types/notification';

interface NotificationCarouselProps {
  notifications: Notification[];
}

export default function NotificationCarousel({ notifications }: NotificationCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (notifications.length <= 1) return;

    const interval = setInterval(() => {
      // 淡出动画
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }).start(() => {
        // 更新索引
        setCurrentIndex((prev) => (prev + 1) % notifications.length);
        // 淡入动画
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }).start();
      });
    }, 5000); // 每5秒切换一次

    return () => clearInterval(interval);
  }, [notifications.length]);

  if (notifications.length === 0) {
    return (
      <View style={styles.container}>
        <View style={[styles.notificationCard, styles.placeholderCard]}>
          <View style={styles.placeholderContent}>
            <Ionicons name="notifications-outline" size={24} color={COLORS.TEXT_GRAY} />
            <Text style={styles.placeholderText}>No Notifications Yet</Text>
          </View>
        </View>
      </View>
    );
  }

  const currentNotification = notifications[currentIndex];

  const handleNextNotification = () => {
    setCurrentIndex((prev) => (prev + 1) % notifications.length);
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.notificationCard,
          {
            backgroundColor: COLORS.BACKGROUND_CARD,
            borderLeftColor: COLORS.PRIMARY,
            borderLeftWidth: 4,
            opacity: fadeAnim,
          },
        ]}
      >
        <View style={styles.notificationTop}>
          <View style={styles.leftSection}>
            <Text style={styles.emoji}>{currentNotification.emoji}</Text>
          </View>

          {notifications.length > 1 && (
            <TouchableOpacity
              onPress={handleNextNotification}
              style={[styles.nextButton, { backgroundColor: COLORS.PRIMARY_TRANSPARENT }]}
            >
              <Ionicons name="chevron-forward" size={18} color={COLORS.PRIMARY} />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.notificationBottom}>
          <Text style={styles.message} numberOfLines={3}>
            {currentNotification.message}
          </Text>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  notificationCard: {
    flexDirection: 'column',
    padding: 16,
    borderRadius: RADIUS.MEDIUM,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    height: 160,
  },
  notificationTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationBottom: {
    flex: 1,
    justifyContent: 'center',
  },
  emoji: {
    color: COLORS.PRIMARY,
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    paddingTop: 8,
  },
  message: {
    color: COLORS.TEXT_WHITE,
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '500',
    lineHeight: 22,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 64,
    height: 32,
    borderRadius: 16,
    borderWidth: 0,
  },
  placeholderCard: {
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderLeftColor: COLORS.TEXT_GRAY,
    borderLeftWidth: 4,
    opacity: 0.7,
  },
  placeholderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    height: 160,
  },
  placeholderText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.MEDIUM,
    marginLeft: 8,
  },
}); 