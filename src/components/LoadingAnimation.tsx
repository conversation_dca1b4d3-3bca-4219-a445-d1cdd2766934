import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withDelay,
  Easing,
  FadeIn,
  FadeOut,
} from 'react-native-reanimated';
import { COLORS, FONT_SIZES, SPACING } from '../config/theme';

interface LoadingAnimationProps {
  text?: string;
  size?: 'small' | 'large';
  variant?: 'primary' | 'white';
}

interface BarProps {
  color: string;
  minHeight: number;
  maxHeight: number;
  barWidth: number;
  delay: number;
}

const Bar: React.FC<BarProps> = ({ color, minHeight, maxHeight, barWidth, delay }) => {
  const height = useSharedValue(minHeight);

  useEffect(() => {
    // A nice, smooth "material" ease
    const easing = Easing.bezier(0.4, 0.0, 0.2, 1.0); 
    const duration = 400;

    height.value = withDelay(
      delay,
      withRepeat(
        withSequence(
          withTiming(maxHeight, { duration, easing }),
          withTiming(minHeight, { duration, easing })
        ),
        -1, // infinite loop
        true // reverse direction on every other repetition
      )
    );
  }, [height, minHeight, maxHeight, delay]);

  const animatedStyle = useAnimatedStyle(() => ({
    height: height.value,
  }));

  return (
    <Animated.View 
      style={[
        styles.bar, 
        { 
          backgroundColor: color,
          width: barWidth,
        }, 
        animatedStyle
      ]} 
    />
  );
};

const LoadingAnimation: React.FC<LoadingAnimationProps> = ({ 
  text = 'Loading...', 
  size = 'large',
  variant = 'primary'
}) => {
  const config = {
    barWidth: size === 'large' ? 5 : 3,
    minHeight: size === 'large' ? 5 : 3,
    maxHeight: size === 'large' ? 25 : 15,
    fontSize: size === 'large' ? FONT_SIZES.MEDIUM : FONT_SIZES.SMALL,
  };

  const colors = variant === 'primary' ? {
    bar: COLORS.PRIMARY,
    text: COLORS.TEXT_DARK,
  } : {
    bar: '#FFFFFF',
    text: COLORS.TEXT_WHITE,
  };

  const barProps = {
    color: colors.bar,
    minHeight: config.minHeight,
    maxHeight: config.maxHeight,
    barWidth: config.barWidth,
  }

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(300).easing(Easing.out(Easing.ease))}
      exiting={FadeOut.duration(300).easing(Easing.in(Easing.ease))}
    >
      <View style={styles.barsContainer}>
        <Bar {...barProps} delay={0} />
        <Bar {...barProps} delay={100} />
        <Bar {...barProps} delay={200} />
      </View>
      
      {text && (
        <Text 
          style={[
            styles.loadingText, 
            { 
              fontSize: config.fontSize,
              color: colors.text,
            }
          ]}
        >
          {text}
        </Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.MEDIUM,
  },
  barsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    height: 40, // A fixed height container to allow bars to grow
    marginBottom: SPACING.MEDIUM,
  },
  bar: {
    borderRadius: 2,
    marginHorizontal: 3,
  },
  loadingText: {
    fontWeight: '600',
    letterSpacing: 0.5,
    textAlign: 'center',
  },
});

export default LoadingAnimation; 