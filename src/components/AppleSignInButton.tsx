import React from 'react';
import { Platform, Alert } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import { supabase } from '../lib/supabase';
import { RADIUS } from '../config/theme';

interface AppleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  style?: any;
  loading?: boolean;
}

export default function AppleSignInButton({ 
  onSuccess, 
  onError, 
  style,
  loading = false
}: AppleSignInButtonProps) {
  const handleAppleSignIn = async () => {
    try {
      // 检查设备是否支持Apple登录
      const isAvailable = await AppleAuthentication.isAvailableAsync();
      if (!isAvailable) {
        throw new Error('Apple Sign In is not available on this device');
      }

      // 获取Apple凭证
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      console.log('Apple credential received:', {
        user: credential.user,
        email: credential.email,
        fullName: credential.fullName,
        hasIdentityToken: !!credential.identityToken,
      });

      if (!credential.identityToken) {
        throw new Error('No identity token received from Apple');
      }

      // 使用修正的方法进行Supabase认证
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: credential.identityToken,
        nonce: credential.nonce,
        options: {
          // 指定正确的audience
          skipNonceCheck: false,
        },
      });

      if (error) {
        console.error('Supabase Apple sign in error:', error);
        throw error;
      }

      console.log('Supabase sign in successful:', {
        user: data.user?.id,
        email: data.user?.email,
        isNewUser: !data.user?.email_confirmed_at,
      });

      // 如果是首次登录且有用户信息，可以更新用户资料
      if (credential.fullName && data.user) {
        try {
          // 手动构建显示名称，避免使用可能未定义的格式化方法
          const { givenName, familyName } = credential.fullName;
          let displayName = '';

          if (givenName && familyName) {
            displayName = `${givenName} ${familyName}`;
          } else if (givenName) {
            displayName = givenName;
          } else if (familyName) {
            displayName = familyName;
          }

          if (displayName.trim()) {
            console.log('Updating user profile with display name:', displayName);
            await supabase.auth.updateUser({
              data: { display_name: displayName.trim() }
            });
          }
        } catch (nameError) {
          console.log('Could not update display name:', nameError);
          // 继续执行，不阻止登录流程
        }
      }

      console.log('Apple Sign In completed successfully');
      onSuccess?.();
    } catch (error: any) {
      console.error('Apple Sign In Error:', error);

      if (error.code === 'ERR_REQUEST_CANCELED') {
        // 用户取消登录，不显示错误
        console.log('User canceled Apple Sign In');
        return;
      }

      // 检查是否是姓名处理错误，如果是则不阻止登录
      if (error.message && error.message.includes('Cannot read property')) {
        console.log('Name formatting error, but login was successful');
        onSuccess?.();
        return;
      }

      const errorMessage = error.message || 'Apple Sign In failed';
      onError?.(errorMessage);
      Alert.alert('Sign In Failed', errorMessage);
    }
  };

  // 只在iOS上显示
  if (Platform.OS !== 'ios') {
    return null;
  }

  return (
    <AppleAuthentication.AppleAuthenticationButton
      buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
      buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
      cornerRadius={RADIUS.MEDIUM}
      style={[{ width: '100%', height: 44 }, style]}
      onPress={handleAppleSignIn}
    />
  );
}
