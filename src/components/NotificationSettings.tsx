import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING, RADIUS } from '../config/theme';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/AuthContext';

interface NotificationPreferences {
  event_reminders: boolean;
  event_updates: boolean;
  promotional_messages: boolean;
  push_enabled: boolean;
  email_enabled: boolean;
}

interface NotificationSettingsProps {
  onClose?: () => void;
}

export default function NotificationSettings({ onClose }: NotificationSettingsProps) {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    event_reminders: true,
    event_updates: true,
    promotional_messages: false,
    push_enabled: true,
    email_enabled: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setPreferences({
          event_reminders: data.event_reminders,
          event_updates: data.event_updates,
          promotional_messages: data.promotional_messages,
          push_enabled: data.push_enabled,
          email_enabled: data.email_enabled,
        });
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      Alert.alert('Error', 'Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async (newPreferences: NotificationPreferences) => {
    if (!user?.id) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: user.id,
          ...newPreferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      setPreferences(newPreferences);
      console.log('Notification preferences saved successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert('Error', 'Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (key: keyof NotificationPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value };
    
    // 如果禁用推送通知，也禁用所有依赖于推送的设置
    if (key === 'push_enabled' && !value) {
      newPreferences.event_reminders = false;
      newPreferences.event_updates = false;
      newPreferences.promotional_messages = false;
    }
    
    savePreferences(newPreferences);
  };

  const renderSettingItem = (
    key: keyof NotificationPreferences,
    title: string,
    description: string,
    icon: string,
    disabled?: boolean
  ) => (
    <View style={[styles.settingItem, disabled && styles.disabledItem]}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={disabled ? COLORS.TEXT_GRAY : COLORS.PRIMARY} />
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, disabled && styles.disabledText]}>{title}</Text>
          <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={preferences[key]}
        onValueChange={(value) => handleToggle(key, value)}
        trackColor={{ false: COLORS.BACKGROUND_CARD, true: COLORS.PRIMARY_TRANSPARENT }}
        thumbColor={preferences[key] ? COLORS.PRIMARY : COLORS.TEXT_GRAY}
        disabled={disabled || saving}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Notification Settings</Text>
          {onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={COLORS.TEXT_WHITE} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading preferences...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Notification Settings</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.TEXT_WHITE} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Push Notifications</Text>
          <Text style={styles.sectionDescription}>
            Control how you receive notifications on your device
          </Text>

          {renderSettingItem(
            'push_enabled',
            'Enable Push Notifications',
            'Receive notifications on your device',
            'notifications'
          )}

          {renderSettingItem(
            'event_reminders',
            'Event Reminders',
            'Get notified 1 hour before your events start',
            'time',
            !preferences.push_enabled
          )}

          {renderSettingItem(
            'event_updates',
            'Event Updates',
            'Notifications about changes to your events',
            'calendar',
            !preferences.push_enabled
          )}


        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Marketing</Text>
          <Text style={styles.sectionDescription}>
            Promotional content and recommendations
          </Text>

          {renderSettingItem(
            'promotional_messages',
            'Promotional Messages',
            'Special offers and event recommendations',
            'gift',
            !preferences.push_enabled
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Email</Text>
          <Text style={styles.sectionDescription}>
            Email notifications and updates
          </Text>

          {renderSettingItem(
            'email_enabled',
            'Email Notifications',
            'Receive important updates via email',
            'mail'
          )}
        </View>

        <View style={styles.infoContainer}>
          <Ionicons name="information-circle" size={20} color={COLORS.TEXT_GRAY} />
          <Text style={styles.infoText}>
            You can always change these settings later. Some notifications are required for account security and cannot be disabled.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND_DARK,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.LARGE,
    paddingVertical: SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  title: {
    fontSize: FONT_SIZES.XLARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
  },
  closeButton: {
    padding: SPACING.SMALL,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.LARGE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.TEXT_GRAY,
    fontSize: FONT_SIZES.MEDIUM,
  },
  section: {
    marginTop: SPACING.LARGE,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: 'bold',
    color: COLORS.TEXT_WHITE,
    marginBottom: SPACING.SMALL,
  },
  sectionDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginBottom: SPACING.MEDIUM,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.MEDIUM,
    paddingHorizontal: SPACING.MEDIUM,
    backgroundColor: COLORS.BACKGROUND_CARD,
    borderRadius: RADIUS.MEDIUM,
    marginBottom: SPACING.SMALL,
  },
  disabledItem: {
    opacity: 0.5,
  },
  settingLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    flex: 1,
    marginLeft: SPACING.MEDIUM,
  },
  settingTitle: {
    fontSize: FONT_SIZES.MEDIUM,
    fontWeight: '600',
    color: COLORS.TEXT_WHITE,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
  },
  disabledText: {
    color: COLORS.TEXT_GRAY,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: SPACING.LARGE,
    marginBottom: SPACING.XLARGE,
    padding: SPACING.MEDIUM,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: RADIUS.MEDIUM,
  },
  infoText: {
    flex: 1,
    fontSize: FONT_SIZES.SMALL,
    color: COLORS.TEXT_GRAY,
    marginLeft: SPACING.SMALL,
    lineHeight: 18,
  },
}); 