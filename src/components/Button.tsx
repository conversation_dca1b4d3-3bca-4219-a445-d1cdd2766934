import { StyleSheet, Text, TouchableOpacity, TouchableOpacityProps, ActivityIndicator, View } from 'react-native';
import { ReactNode } from 'react';
import { COLORS, RADIUS, FONT_SIZES } from '../config/theme';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary';
  loading?: boolean;
  icon?: () => ReactNode;
}

export default function Button({ 
  title, 
  variant = 'primary', 
  loading = false,
  style, 
  disabled,
  icon,
  ...props 
}: ButtonProps) {
  return (
    <TouchableOpacity 
      style={[
        styles.button, 
        variant === 'primary' ? styles.primaryButton : styles.secondaryButton,
        (disabled || loading) && styles.disabledButton,
        style
      ]} 
      activeOpacity={0.8}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <ActivityIndicator color="#fff" />
      ) : (
        <View style={styles.contentContainer}>
          {icon && icon()}
          <Text 
            style={[
              styles.text,
              variant === 'primary' ? styles.primaryText : styles.secondaryText,
              (disabled || loading) && styles.disabledText
            ]}
          >
            {title}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    padding: 16,
    borderRadius: RADIUS.XLARGE,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondaryButton: {
    backgroundColor: COLORS.TRANSPARENT_LIGHT,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY_BORDER,
  },
  disabledButton: {
    opacity: 0.5,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: FONT_SIZES.LARGE,
    fontWeight: '600',
  },
  primaryText: {
    color: COLORS.TEXT_WHITE,
  },
  secondaryText: {
    color: COLORS.TEXT_WHITE,
  },
  disabledText: {
    opacity: 0.7,
  },
}); 