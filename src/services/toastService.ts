import Toast from 'react-native-toast-message';

export interface ToastOptions {
  title: string;
  message?: string;
  duration?: number;
  position?: 'top' | 'bottom';
  onPress?: () => void;
  onHide?: () => void;
}

class ToastService {
  /**
   * 显示通知 - 统一的通知方法
   */
  show(options: ToastOptions) {
    Toast.show({
      type: 'info', // 统一使用 info 类型
      text1: options.title,
      text2: options.message,
      visibilityTime: options.duration || 3000,
      position: options.position || 'bottom',
      onPress: options.onPress,
      onHide: options.onHide,
    });
  }

  /**
   * 隐藏当前通知
   */
  hide() {
    Toast.hide();
  }

  /**
   * 快捷方法：显示简单消息
   */
  showMessage(message: string, duration?: number) {
    this.show({
      title: message,
      duration,
    });
  }

  /**
   * 显示收藏成功通知
   */
  showFavoriteAdded(eventTitle?: string) {
    this.show({
      title: 'Added to Favorites',
      message: eventTitle ? `"${eventTitle}" has been added to your favorites` : 'Event added to favorites',
      duration: 2500,
    });
  }

  /**
   * 显示取消收藏通知
   */
  showFavoriteRemoved(eventTitle?: string) {
    this.show({
      title: 'Removed from Favorites',
      message: eventTitle ? `"${eventTitle}" has been removed from your favorites` : 'Event removed from favorites',
      duration: 2500,
    });
  }

  /**
   * 显示网络错误通知
   */
  showNetworkError() {
    this.show({
      title: 'Network Error',
      message: 'Please check your internet connection and try again',
      duration: 4000,
    });
  }

  /**
   * 显示登录成功通知
   */
  showLoginSuccess(userName?: string) {
    this.show({
      title: 'Welcome Back!',
      message: userName ? `Hello ${userName}` : 'You have successfully signed in',
      duration: 2500,
    });
  }

  /**
   * 显示登出通知
   */
  showLogoutSuccess() {
    this.show({
      title: 'Signed Out',
      message: 'You have been successfully signed out',
      duration: 2000,
    });
  }

  /**
   * 显示更新检查结果
   */
  showUpdateAvailable(version: string) {
    this.show({
      title: 'Update Available',
      message: `Version ${version} is now available`,
      duration: 4000,
    });
  }

  /**
   * 显示已是最新版本
   */
  showUpToDate() {
    this.show({
      title: 'Up to Date',
      message: 'You are using the latest version',
      duration: 2500,
    });
  }
}

export const toastService = new ToastService();
