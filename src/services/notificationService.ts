import { supabase } from '../lib/supabase';
import { Notification } from '../types/notification';

export const notificationService = {
  /**
   * 获取所有活跃的通知
   * @returns 通知列表
   */
  async getActiveNotifications(): Promise<Notification[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('id, content, emoji, type, created_at')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to fetch notifications:', error);
        return [];
      }

      return data?.map(item => ({
        id: item.id,
        message: item.content,
        emoji: item.emoji,
        type: item.type as 'success' | 'info' | 'warning',
        fullMessage: item.content // 使用相同内容作为完整消息
      })) || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }
}; 