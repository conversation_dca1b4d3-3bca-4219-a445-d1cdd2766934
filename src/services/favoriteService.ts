import { supabase } from '../lib/supabase';
import { Event } from '../types/event';

/**
 * 创建一个favoriteService实例，该实例不需要重复获取用户ID
 * @param userId 用户ID
 * @returns favoriteService实例
 */
export const createFavoriteService = (userId: string | null) => {
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  return {
    /**
     * 添加收藏
     * @param eventId 事件ID
     */
    async addFavorite(eventId: string) {
      const { data, error } = await supabase
        .from('favorite_events')
        .insert([
          { user_id: userId, event_id: eventId }
        ])
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    /**
     * 移除收藏
     * @param eventId 事件ID
     */
    async removeFavorite(eventId: string) {
      const { data, error } = await supabase
        .from('favorite_events')
        .delete()
        .match({ user_id: userId, event_id: eventId })
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    /**
     * 检查事件是否已收藏
     * @param eventId 事件ID
     */
    async isFavorite(eventId: string) {
      const { data, error } = await supabase
        .from('favorite_events')
        .select('*')
        .match({ user_id: userId, event_id: eventId })
        .maybeSingle();

      if (error) throw error;
      return !!data;
    },

    /**
     * 批量获取多个事件的收藏状态
     * @param eventIds 事件ID数组
     */
    async getBatchFavoriteStatus(eventIds: string[]): Promise<Record<string, boolean>> {
      // 如果没有事件ID或事件ID数组为空，则返回空对象
      if (!eventIds || eventIds.length === 0) {
        return {};
      }

      // 批量查询所有指定事件的收藏状态
      const { data, error } = await supabase
        .from('favorite_events')
        .select('event_id')
        .eq('user_id', userId)
        .in('event_id', eventIds);

      if (error) throw error;

      // 转换为 {eventId: boolean} 格式的结果
      const favoriteMap: Record<string, boolean> = {};
      
      // 初始化所有事件ID为未收藏
      eventIds.forEach(id => {
        favoriteMap[id] = false;
      });
      
      // 设置已收藏的事件
      if (data) {
        data.forEach(item => {
          favoriteMap[item.event_id] = true;
        });
      }
      
      return favoriteMap;
    },

    /**
     * 获取用户收藏的事件列表 - 优化版
     */
    async getUserFavoriteEvents() {
      // 查询用户收藏的所有事件，仅获取必要字段
      const { data, error } = await supabase
        .from('favorite_events')
        .select(`
          events (
            event_id,
            title,
            image,
            location,
            is_archived
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;

      // 提取events数据并确保每个项目是单个对象而不是数组
      const events = data?.map(item => {
        const event = item.events as any;
        if (!event) return null;
        return {
          event_id: event.event_id,
          title: event.title,
          image: event.image,
          location: event.location,
          is_archived: event.is_archived
        };
      }).filter(Boolean) || [];
      
      return { data: events, error: null };
    }
  };
};

/**
 * 传统的favoriteService，在每个方法中获取用户ID
 * 为保持向后兼容而保留
 * 建议在新代码中使用createFavoriteService和useAuth
 */
export const favoriteService = {
  async addFavorite(eventId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return createFavoriteService(user.id).addFavorite(eventId);
  },

  async removeFavorite(eventId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return createFavoriteService(user.id).removeFavorite(eventId);
  },

  async isFavorite(eventId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return false;

    return createFavoriteService(user.id).isFavorite(eventId);
  },

  async getBatchFavoriteStatus(eventIds: string[]): Promise<Record<string, boolean>> {
    // 如果没有事件ID或事件ID数组为空，则返回空对象
    if (!eventIds || eventIds.length === 0) {
      return {};
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      // 用户未登录，返回所有事件都未收藏的结果
      return eventIds.reduce((result, id) => {
        result[id] = false;
        return result;
      }, {} as Record<string, boolean>);
    }

    return createFavoriteService(user.id).getBatchFavoriteStatus(eventIds);
  },

  async getUserFavoriteEvents() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    return createFavoriteService(user.id).getUserFavoriteEvents();
  }
}; 