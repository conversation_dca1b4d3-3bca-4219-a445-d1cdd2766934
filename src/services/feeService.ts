import { Database } from '../types/database';
import { supabase } from '../lib/supabase';

export interface FeeConfig {
  percentage: number;  // 百分比费率
  fixedAmount: number; // 固定金额
  currency: string;    // 货币单位
  minFee: number;      // 最小服务费金额
}

export const defaultFeeConfig: FeeConfig = {
  percentage: 0.05,  // 5%
  fixedAmount: 0.3,   // $0.3
  currency: 'A',
  minFee: 0.5         // 最小服务费金额为$0.5
};

export const feeService = {
  /**
   * 从数据库中获取组织者的费用配置
   * @param organizerId 组织者ID
   * @returns 费用配置
   */
  async getOrganizerFeeConfig(organizerId: string): Promise<FeeConfig | null> {
    try {
      const { data, error } = await supabase
        .from('organizer_profiles')
        .select('service_fee_percentage, service_fee_fixed, service_fee_min')
        .eq('organizer_id', organizerId)
        .single();
      
      if (error || !data) {
        console.error('Failed to fetch organizer fee config:', error);
        return null;
      }
      
      return {
        percentage: data.service_fee_percentage / 100, // 从百分比转换为小数
        fixedAmount: data.service_fee_fixed,
        currency: defaultFeeConfig.currency, // 使用默认货币
        minFee: data.service_fee_min
      };
    } catch (error) {
      console.error('Failed to fetch organizer fee config:', error);
      return null;
    }
  },

  /**
   * 计算服务费
   * @param subtotal 小计金额
   * @param config 可选的费用配置，如果不提供则使用默认配置
   * @returns 服务费金额
   */
  calculateServiceFee(subtotal: number, config: FeeConfig = defaultFeeConfig): number {
    // 免费活动不收取服务费
    if (subtotal <= 0) {
      return 0;
    }
    
    // 计算基础服务费
    const percentageFee = subtotal * config.percentage;
    const baseFee = percentageFee + config.fixedAmount;
    
    // 确保服务费不低于最小金额
    return Math.max(baseFee, config.minFee);
  },

  /**
   * 计算总金额（包含服务费）
   * @param subtotal 小计金额
   * @param config 可选的费用配置，如果不提供则使用默认配置
   * @returns 总金额
   */
  calculateTotalAmount(subtotal: number, config: FeeConfig = defaultFeeConfig): number {
    const serviceFee = this.calculateServiceFee(subtotal, config);
    return subtotal + serviceFee;
  },

  /**
   * 格式化金额显示
   * @param amount 金额
   * @param currency 货币符号
   * @returns 格式化后的金额字符串
   */
  formatAmount(amount: number, currency: string = defaultFeeConfig.currency): string {
    return `${currency === 'AUD' ? 'A' : currency}$${amount.toFixed(2)}`;
  },

  /**
   * 获取默认费用配置
   * @returns 默认费用配置
   */
  getDefaultConfig(): FeeConfig {
    return { ...defaultFeeConfig };
  }
}; 