import AsyncStorage from '@react-native-async-storage/async-storage';

// 缓存键
const CACHE_KEYS = {
  TAGS: 'cached_tags',
  TAGS_TIMESTAMP: 'cached_tags_timestamp',
  TAG_EVENTS_PREFIX: 'cached_tag_events_',
  TAG_EVENTS_TIMESTAMP_PREFIX: 'cached_tag_events_timestamp_',
  EVENT_DETAILS_PREFIX: 'cached_event_details_',
  EVENT_DETAILS_TIMESTAMP_PREFIX: 'cached_event_details_timestamp_',
  TRENDING_EVENTS: 'cached_trending_events',
  TRENDING_EVENTS_TIMESTAMP: 'cached_trending_events_timestamp',
};

// 缓存过期时间（毫秒）
const CACHE_EXPIRY = {
  TAGS: 24 * 60 * 60 * 1000, // 24小时
  TAG_EVENTS: 30 * 60 * 1000, // 30分钟
  EVENT_DETAILS: 15 * 60 * 1000, // 15分钟
  TRENDING_EVENTS: 15 * 60 * 1000, // 15分钟
};

export const cacheService = {
  /**
   * 保存标签数据到缓存
   * @param tags 标签数组
   */
  async cacheTags(tags: string[]): Promise<void> {
    try {
      const timestamp = Date.now();
      await AsyncStorage.multiSet([
        [CACHE_KEYS.TAGS, JSON.stringify(tags)],
        [CACHE_KEYS.TAGS_TIMESTAMP, timestamp.toString()],
      ]);
      console.log('Tags cached successfully');
    } catch (error) {
      console.error('Error caching tags:', error);
    }
  },

  /**
   * 从缓存获取标签数据
   * @returns 标签数组或null（如果缓存不存在或已过期）
   */
  async getCachedTags(): Promise<string[] | null> {
    try {
      const [tagsJson, timestampStr] = await AsyncStorage.multiGet([
        CACHE_KEYS.TAGS,
        CACHE_KEYS.TAGS_TIMESTAMP,
      ]);
      
      // 检查是否有缓存数据
      if (!tagsJson[1] || !timestampStr[1]) {
        console.log('No cached tags found');
        return null;
      }
      
      // 检查缓存是否过期
      const timestamp = parseInt(timestampStr[1], 10);
      const now = Date.now();
      if (now - timestamp > CACHE_EXPIRY.TAGS) {
        console.log('Cached tags expired');
        return null;
      }
      
      // 返回缓存的标签数据
      const tags = JSON.parse(tagsJson[1]);
      console.log('Retrieved cached tags successfully');
      return tags;
    } catch (error) {
      console.error('Error retrieving cached tags:', error);
      return null;
    }
  },

  /**
   * 清除标签缓存
   */
  async clearTagsCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        CACHE_KEYS.TAGS,
        CACHE_KEYS.TAGS_TIMESTAMP,
      ]);
      console.log('Tags cache cleared successfully');
    } catch (error) {
      console.error('Error clearing tags cache:', error);
    }
  },

  /**
   * 缓存特定标签的事件数据
   * @param tagId 标签ID
   * @param events 事件数据
   * @param page 页码
   * @param hasMore 是否有更多数据
   */
  async cacheTagEvents(tagId: string, events: any[], page: number, hasMore: boolean): Promise<void> {
    try {
      const timestamp = Date.now();
      const cacheData = {
        events,
        page,
        hasMore,
      };
      
      await AsyncStorage.multiSet([
        [`${CACHE_KEYS.TAG_EVENTS_PREFIX}${tagId}_${page}`, JSON.stringify(cacheData)],
        [`${CACHE_KEYS.TAG_EVENTS_TIMESTAMP_PREFIX}${tagId}_${page}`, timestamp.toString()],
      ]);
      console.log(`Events for tag ${tagId} page ${page} cached successfully`);
    } catch (error) {
      console.error(`Error caching events for tag ${tagId}:`, error);
    }
  },

  /**
   * 获取缓存的特定标签的事件数据
   * @param tagId 标签ID
   * @param page 页码
   * @returns 事件数据或null（如果缓存不存在或已过期）
   */
  async getCachedTagEvents(tagId: string, page: number): Promise<{ events: any[], page: number, hasMore: boolean } | null> {
    try {
      const [eventsJson, timestampStr] = await AsyncStorage.multiGet([
        `${CACHE_KEYS.TAG_EVENTS_PREFIX}${tagId}_${page}`,
        `${CACHE_KEYS.TAG_EVENTS_TIMESTAMP_PREFIX}${tagId}_${page}`,
      ]);
      
      // 检查是否有缓存数据
      if (!eventsJson[1] || !timestampStr[1]) {
        console.log(`No cached events found for tag ${tagId} page ${page}`);
        return null;
      }
      
      // 检查缓存是否过期
      const timestamp = parseInt(timestampStr[1], 10);
      const now = Date.now();
      if (now - timestamp > CACHE_EXPIRY.TAG_EVENTS) {
        console.log(`Cached events for tag ${tagId} page ${page} expired`);
        return null;
      }
      
      // 返回缓存的事件数据
      const cacheData = JSON.parse(eventsJson[1]);
      console.log(`Retrieved cached events for tag ${tagId} page ${page} successfully`);
      return cacheData;
    } catch (error) {
      console.error(`Error retrieving cached events for tag ${tagId}:`, error);
      return null;
    }
  },

  /**
   * 清除特定标签的事件缓存
   * @param tagId 标签ID
   */
  async clearTagEventsCache(tagId: string): Promise<void> {
    try {
      // 获取所有缓存键
      const allKeys = await AsyncStorage.getAllKeys();
      
      // 过滤出与该标签相关的缓存键
      const keysToRemove = allKeys.filter(key => 
        key.startsWith(`${CACHE_KEYS.TAG_EVENTS_PREFIX}${tagId}`) || 
        key.startsWith(`${CACHE_KEYS.TAG_EVENTS_TIMESTAMP_PREFIX}${tagId}`)
      );
      
      if (keysToRemove.length > 0) {
        await AsyncStorage.multiRemove(keysToRemove);
        console.log(`Cache for tag ${tagId} cleared successfully`);
      }
    } catch (error) {
      console.error(`Error clearing cache for tag ${tagId}:`, error);
    }
  },

  /**
   * 缓存事件详情数据
   * @param eventId 事件ID
   * @param eventData 事件数据
   */
  async cacheEventDetails(eventId: string, eventData: any): Promise<void> {
    try {
      const timestamp = Date.now();
      await AsyncStorage.multiSet([
        [`${CACHE_KEYS.EVENT_DETAILS_PREFIX}${eventId}`, JSON.stringify(eventData)],
        [`${CACHE_KEYS.EVENT_DETAILS_TIMESTAMP_PREFIX}${eventId}`, timestamp.toString()],
      ]);
      console.log(`Event details for ${eventId} cached successfully`);
    } catch (error) {
      console.error(`Error caching event details for ${eventId}:`, error);
    }
  },

  /**
   * 获取缓存的事件详情数据
   * @param eventId 事件ID
   * @returns 事件详情数据或null（如果缓存不存在或已过期）
   */
  async getCachedEventDetails(eventId: string): Promise<any | null> {
    try {
      const [eventJson, timestampStr] = await AsyncStorage.multiGet([
        `${CACHE_KEYS.EVENT_DETAILS_PREFIX}${eventId}`,
        `${CACHE_KEYS.EVENT_DETAILS_TIMESTAMP_PREFIX}${eventId}`,
      ]);
      
      // 检查是否有缓存数据
      if (!eventJson[1] || !timestampStr[1]) {
        console.log(`No cached event details found for ${eventId}`);
        return null;
      }
      
      // 检查缓存是否过期
      const timestamp = parseInt(timestampStr[1], 10);
      const now = Date.now();
      if (now - timestamp > CACHE_EXPIRY.EVENT_DETAILS) {
        console.log(`Cached event details for ${eventId} expired`);
        return null;
      }
      
      // 返回缓存的事件详情数据
      const eventData = JSON.parse(eventJson[1]);
      console.log(`Retrieved cached event details for ${eventId} successfully`);
      return eventData;
    } catch (error) {
      console.error(`Error retrieving cached event details for ${eventId}:`, error);
      return null;
    }
  },

  /**
   * 清除事件详情缓存
   * @param eventId 事件ID
   */
  async clearEventDetailsCache(eventId: string): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        `${CACHE_KEYS.EVENT_DETAILS_PREFIX}${eventId}`,
        `${CACHE_KEYS.EVENT_DETAILS_TIMESTAMP_PREFIX}${eventId}`,
      ]);
      console.log(`Event details cache for ${eventId} cleared successfully`);
    } catch (error) {
      console.error(`Error clearing event details cache for ${eventId}:`, error);
    }
  },

  /**
   * 缓存趋势活动数据
   * @param events 趋势活动数据
   * @param page 页码
   * @param hasMore 是否有更多数据
   */
  async cacheTrendingEvents(events: any[], page: number, hasMore: boolean): Promise<void> {
    try {
      const timestamp = Date.now();
      const cacheData = {
        events,
        page,
        hasMore,
      };
      
      await AsyncStorage.multiSet([
        [CACHE_KEYS.TRENDING_EVENTS, JSON.stringify(cacheData)],
        [CACHE_KEYS.TRENDING_EVENTS_TIMESTAMP, timestamp.toString()],
      ]);
      console.log('Trending events cached successfully');
    } catch (error) {
      console.error('Error caching trending events:', error);
    }
  },

  /**
   * 获取缓存的趋势活动数据
   * @returns 趋势活动数据或null（如果缓存不存在或已过期）
   */
  async getCachedTrendingEvents(): Promise<{ events: any[], page: number, hasMore: boolean } | null> {
    try {
      const [eventsJson, timestampStr] = await AsyncStorage.multiGet([
        CACHE_KEYS.TRENDING_EVENTS,
        CACHE_KEYS.TRENDING_EVENTS_TIMESTAMP,
      ]);

      if (!eventsJson[1] || !timestampStr[1]) {
        console.log('No cached trending events found');
        return null;
      }

      const timestamp = parseInt(timestampStr[1], 10);
      const now = Date.now();
      if (now - timestamp > CACHE_EXPIRY.TRENDING_EVENTS) {
        console.log('Cached trending events expired');
        return null;
      }

      const cacheData = JSON.parse(eventsJson[1]);
      console.log('Retrieved cached trending events successfully');
      return cacheData;
    } catch (error) {
      console.error('Error retrieving cached trending events:', error);
      return null;
    }
  },

  /**
   * 清除趋势活动缓存
   */
  async clearTrendingEventsCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        CACHE_KEYS.TRENDING_EVENTS,
        CACHE_KEYS.TRENDING_EVENTS_TIMESTAMP,
      ]);
      console.log('Trending events cache cleared successfully');
    } catch (error) {
      console.error('Error clearing trending events cache:', error);
    }
  },

  /**
   * 清除所有应用缓存数据
   * 用于用户登出时清理所有本地缓存
   */
  async clearAllCache(): Promise<void> {
    try {
      console.log('Starting to clear all cache data...');

      // 获取所有AsyncStorage键
      const allKeys = await AsyncStorage.getAllKeys();

      // 过滤出所有应用相关的缓存键
      const cacheKeysToRemove = allKeys.filter(key =>
        key.startsWith('cached_') || // 所有以 cached_ 开头的键
        Object.values(CACHE_KEYS).some(cacheKey =>
          key === cacheKey ||
          key.startsWith(cacheKey)
        )
      );

      if (cacheKeysToRemove.length > 0) {
        await AsyncStorage.multiRemove(cacheKeysToRemove);
        console.log(`Successfully cleared ${cacheKeysToRemove.length} cache entries:`, cacheKeysToRemove);
      } else {
        console.log('No cache entries found to clear');
      }
    } catch (error) {
      console.error('Error clearing all cache:', error);
      // 即使清理缓存失败，也不应该阻止登出流程
      // 只记录错误，不抛出异常
    }
  },
};