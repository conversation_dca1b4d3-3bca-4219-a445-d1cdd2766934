import { supabase } from '../lib/supabase';
import { Database } from '../types/database';

// 自定义错误类型
export class InsufficientInventoryError extends Error {
  constructor(public availableTickets: number, public requestedTickets: number) {
    super(`Only ${availableTickets} tickets available, but ${requestedTickets} requested`);
    this.name = 'InsufficientInventoryError';
  }
}

export class UserLimitExceededError extends Error {
  constructor(public currentCount: number, public maxAllowed: number, public requested: number) {
    super(`User limit exceeded: current ${currentCount}, max ${maxAllowed}, requested ${requested}`);
    this.name = 'UserLimitExceededError';
  }
}

export class OrderExpiredError extends Error {
  constructor() {
    super('Order has expired');
    this.name = 'OrderExpiredError';
  }
}

export class SessionNotFoundError extends Error {
  constructor() {
    super('Event session not found');
    this.name = 'SessionNotFoundError';
  }
}

export type Order = Database['public']['Tables']['orders']['Row'];

export const orderService = {
  /**
   * 获取用户订单的分页数据
   * 实现真正的服务器端分页
   * @param userId 用户ID
   * @param page 页码，从0开始
   * @param pageSize 每页数量
   * @returns 分页后的订单数据
   */
  async getUserOrdersPaginated(userId: string, page: number = 0, pageSize: number = 10) {
    // 计算起始位置
    const from = page * pageSize;
    const to = from + pageSize - 1;
    
    const { data, error, count } = await supabase
      .from('orders')
      .select(`
        order_id,
        quantity,
        total_amount,
        is_paid,
        created_at,
        event_sessions (
          id,
          title,
          start_time,
          event:events (
            event_id,
            title,
            image
          )
        )
      `, { count: 'exact' })
      .eq('user_id', userId)
      .eq('is_paid', true) // 直接只获取已支付的订单
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) throw error;
    
    return {
      data: data || [],
      totalCount: count || 0,
      hasMore: (count || 0) > from + pageSize
    };
  },

  /**
   * 获取用户订单的完整详情
   * 包含所有关联数据，用于需要完整数据的场景
   */
  async getUserOrdersComplete(userId: string) {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        event_sessions (
          *,
          event:events (
            *,
            sessions:event_sessions(*),
            organizer:organizer_profiles!organizer_id(*)
          )
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  // 创建新订单（保留原有函数用于向后兼容）
  async createOrder(orderData: {
    user_id: string;
    session_id: string;
    quantity: number;
    total_amount: number;
    is_paid?: boolean;
  }) {
    const { data, error } = await supabase
      .from('orders')
      .insert([
        {
          ...orderData,
          is_paid: orderData.is_paid || false,
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // ✅ 新增：原子性订单创建函数
  async createOrderWithInventoryCheck(orderData: {
    user_id: string;
    session_id: string;
    quantity: number;
    total_amount: number;
  }) {
    const { data, error } = await supabase.rpc('create_order_with_inventory_check', {
      p_user_id: orderData.user_id,
      p_session_id: orderData.session_id,
      p_quantity: orderData.quantity,
      p_total_amount: orderData.total_amount
    });

    if (error) {
      console.error('Order creation error:', error);

      if (error.message.includes('INSUFFICIENT_INVENTORY')) {
        const match = error.message.match(/Only (\d+) tickets available/);
        const available = match ? parseInt(match[1]) : 0;
        throw new InsufficientInventoryError(available, orderData.quantity);
      }

      if (error.message.includes('USER_LIMIT_EXCEEDED')) {
        const match = error.message.match(/User limit is (\d+), current count is (\d+)/);
        const maxAllowed = match ? parseInt(match[1]) : 0;
        const currentCount = match ? parseInt(match[2]) : 0;
        throw new UserLimitExceededError(currentCount, maxAllowed, orderData.quantity);
      }

      if (error.message.includes('SESSION_NOT_FOUND')) {
        throw new SessionNotFoundError();
      }

      throw error;
    }

    return {
      order_id: data[0].order_id,
      expires_at: data[0].expires_at,
      available_tickets: data[0].available_tickets
    };
  },

  // ✅ 新增：检查库存可用性
  async checkInventoryAvailability(sessionId: string, quantity: number) {
    const { data, error } = await supabase.rpc('check_inventory_availability', {
      p_session_id: sessionId,
      p_quantity: quantity
    });

    if (error) throw error;

    return {
      available: data[0].available,
      canPurchase: data[0].can_purchase,
      capacity: data[0].capacity,
      soldTickets: data[0].sold_tickets,
      reservedTickets: data[0].reserved_tickets
    };
  },

  // ✅ 新增：获取订单状态
  async getOrderStatus(orderId: string) {
    const { data, error } = await supabase
      .from('orders')
      .select('status, expires_at, quantity, total_amount, created_at')
      .eq('order_id', orderId)
      .single();

    if (error) throw error;
    return data;
  },

  // ✅ 新增：更新订单支付意向ID
  async updateOrderPaymentIntentId(orderId: string, paymentIntentId: string) {
    const { error } = await supabase
      .from('orders')
      .update({
        payment_intent_id: paymentIntentId,
        updated_at: new Date().toISOString()
      })
      .eq('order_id', orderId);

    if (error) throw error;
  },

  // 更新订单支付状态
  async updateOrderPaymentStatus(orderId: string, isPaid: boolean) {
    // 1. 更新订单本身的支付状态
    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update({ is_paid: isPaid })
      .eq('order_id', orderId)
      .select(`
        *,
        event_sessions (
          price,
          event_id,
          event:events (
            organizer_id
          )
        )
      `) // 在select中直接获取后续需要的信息
      .single();

    if (updateError) throw updateError;
    if (!updatedOrder) throw new Error('Order not found after update.');

    // 2. 如果支付成功 (isPaid is true) 并且订单有关联的 session 和 price，则更新收入
    // 确保 updatedOrder.event_sessions 和 updatedOrder.event_sessions.event 都存在
    if (isPaid && 
        updatedOrder.event_sessions && 
        updatedOrder.event_sessions.event && // 增加对 event 对象的检查
        typeof updatedOrder.event_sessions.price === 'number' && 
        updatedOrder.quantity
    ) {
      const sessionPrice = updatedOrder.event_sessions.price;
      const quantity = updatedOrder.quantity;
      const sessionId = updatedOrder.session_id;
      const organizerId = updatedOrder.event_sessions.event.organizer_id; // 现在可以安全访问

      if (sessionPrice > 0 && quantity > 0) { // 只为付费票计算收入
        const incomeForThisSale = quantity * sessionPrice;

        // 2a. 更新 event_sessions 表的 revenue
        const { error: sessionRevenueError } = await supabase.rpc('increment_session_revenue', {
          session_id_param: sessionId,
          increment_amount: incomeForThisSale
        });

        if (sessionRevenueError) {
          console.error('Error updating session revenue:', sessionRevenueError);
        }

        // 2b. 更新 organizer_profiles 表的 total_revenue
        // organizerId 此时必然存在，因为前面已经检查过 updatedOrder.event_sessions.event
        const { error: organizerRevenueError } = await supabase.rpc('increment_organizer_revenue', {
          organizer_id_param: organizerId,
          increment_amount: incomeForThisSale
        });

        if (organizerRevenueError) {
          console.error('Error updating organizer revenue:', organizerRevenueError);
        }
      }
    } else if (isPaid && updatedOrder.event_sessions && !updatedOrder.event_sessions.event) {
        // 如果 event_sessions 存在但 event 对象不存在，记录一个警告
        console.warn(`Organizer ID could not be determined for session ${updatedOrder.session_id} because the joined event data is missing. Revenue for organizer not updated.`);
    }

    return updatedOrder;
  },

  // 获取特定订单详情
  async getOrderById(orderId: string) {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        event_sessions (
          *,
          event:events (
            *,
            sessions:event_sessions(*),
            organizer:organizer_profiles!organizer_id(*)
          )
        )
      `)
      .eq('order_id', orderId)
      .single();

    if (error) throw error;
    return data;
  },

  // ✅ 已移除：calculateSoldTickets - 现在使用数据库字段 sold_tickets

  // ✅ 修改：获取用户已购买的特定场次的票数（使用新状态字段）
  async getUserTicketsCountForSession(userId: string, sessionId: string): Promise<number> {
    const { data, error } = await supabase
      .from('orders')
      .select('quantity')
      .eq('user_id', userId)
      .eq('session_id', sessionId)
      .in('status', ['reserved', 'paid', 'fulfilled']); // 使用新的状态字段

    if (error) throw error;

    // 计算总票数
    return (data || []).reduce((sum, order) => sum + (order.quantity || 0), 0);
  },

  /**
   * 获取特定Session的所有报名用户信息
   * 返回用户名和邮箱信息
   */
  async getSessionAttendees(sessionId: string) {
    // 首先获取所有订单
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select(`
        order_id,
        quantity,
        is_paid,
        user_id
      `)
      .eq('session_id', sessionId)
      .eq('is_paid', true);

    if (ordersError) {
      console.error('Failed to fetch session orders:', ordersError);
      throw ordersError;
    }

    if (!orders || orders.length === 0) {
      return [];
    }

    // 获取所有用户的ID
    const userIds = orders.map(order => order.user_id);

    // 获取这些用户的信息
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select(`
        user_id,
        display_name,
        email
      `)
      .in('user_id', userIds);

    if (profilesError) {
      console.error('Failed to fetch user profiles:', profilesError);
      throw profilesError;
    }

    // 将用户信息与订单合并
    const attendees = orders.map(order => {
      const userProfile = userProfiles?.find(profile => profile.user_id === order.user_id) || {
        display_name: 'Unknown User',
        email: 'Unknown Email'
      };

      return {
        order_id: order.order_id,
        quantity: order.quantity,
        is_paid: order.is_paid,
        user_profiles: userProfile
      };
    });

    return attendees;
  },

  /**
   * 获取特定Session的所有参与者信息，包含签到状态
   * 返回包含票据状态和签到信息的完整数据
   */
  async getSessionAttendeesWithCheckIn(sessionId: string) {
    // 获取所有订单及其关联的票据信息
    const { data: ordersWithTickets, error: ordersError } = await supabase
      .from('orders')
      .select(`
        order_id,
        quantity,
        is_paid,
        user_id,
        tickets (
          ticket_id,
          status,
          check_in_time
        )
      `)
      .eq('session_id', sessionId)
      .eq('is_paid', true);

    if (ordersError) {
      console.error('Failed to fetch session orders with tickets:', ordersError);
      throw ordersError;
    }

    if (!ordersWithTickets || ordersWithTickets.length === 0) {
      return [];
    }

    // 获取所有用户的ID
    const userIds = ordersWithTickets.map(order => order.user_id);

    // 获取这些用户的信息
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select(`
        user_id,
        display_name,
        email
      `)
      .in('user_id', userIds);

    if (profilesError) {
      console.error('Failed to fetch user profiles:', profilesError);
      throw profilesError;
    }

    // 处理数据并计算签到状态
    const attendeesWithCheckIn = ordersWithTickets.map(order => {
      const userProfile = userProfiles?.find(profile => profile.user_id === order.user_id) || {
        display_name: '未知用户',
        email: '无邮箱信息'
      };

      // 处理票据信息
      const tickets = order.tickets || [];
      const totalTickets = order.quantity;
      const checkedInTickets = tickets.filter(ticket => ticket.status === 'used');
      const checkedInCount = checkedInTickets.length;

      // 确定签到状态
      let checkInStatus: 'checked_in' | 'not_checked_in' | 'partial';
      if (checkedInCount === 0) {
        checkInStatus = 'not_checked_in';
      } else if (checkedInCount === totalTickets) {
        checkInStatus = 'checked_in';
      } else {
        checkInStatus = 'partial';
      }

      // 获取最新的签到时间
      const latestCheckInTime = checkedInTickets
        .map(ticket => ticket.check_in_time)
        .filter(time => time !== null)
        .sort((a, b) => new Date(b!).getTime() - new Date(a!).getTime())[0];

      return {
        order_id: order.order_id,
        quantity: order.quantity,
        is_paid: order.is_paid,
        user_profiles: userProfile,
        tickets: tickets.map(ticket => ({
          ticket_id: ticket.ticket_id,
          status: ticket.status as 'valid' | 'used' | 'cancelled',
          check_in_time: ticket.check_in_time
        })),
        check_in_status: checkInStatus,
        checked_in_count: checkedInCount,
        total_tickets: totalTickets,
        latest_check_in_time: latestCheckInTime || undefined
      };
    });

    return attendeesWithCheckIn;
  },

  /**
   * 获取特定Session的参与者头像信息
   * 返回参与者的头像URL和基本信息，用于UI显示
   */
  async getSessionAttendeesAvatars(sessionId: string, limit: number = 5) {
    // 第一步：获取已付费订单的用户ID
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('user_id')
      .eq('session_id', sessionId)
      .eq('is_paid', true);

    if (ordersError) {
      console.error('Failed to fetch session orders:', ordersError);
      throw ordersError;
    }

    if (!orders || orders.length === 0) {
      return [];
    }

    // 去重用户ID（同一用户可能有多个订单）
    const uniqueUserIds = [...new Set(orders.map(order => order.user_id))];

    if (uniqueUserIds.length === 0) {
      return [];
    }

    // 第二步：获取用户头像信息，限制数量
    const { data: userProfiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select(`
        user_id,
        display_name,
        avatar_url
      `)
      .in('user_id', uniqueUserIds)
      .limit(limit);

    if (profilesError) {
      console.error('Failed to fetch user profiles:', profilesError);
      throw profilesError;
    }

    return (userProfiles || []).map(profile => ({
      user_id: profile.user_id,
      display_name: profile.display_name,
      avatar_url: profile.avatar_url
    }));
  }
};