import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { supabase } from '../lib/supabase';
import { 
  NotificationTypeEnum, 
  NotificationData,
  PushNotificationPayload 
} from '../types/pushNotification';



export class PushNotificationService {
  private static isInitialized = false;

  /**
   * Initialize push notification system
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Configure notification handler
      Notifications.setNotificationHandler({
        handleNotification: async (notification) => {
          const notificationType = notification.request.content.data?.type as NotificationTypeEnum;
          
          return {
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: true,
            shouldShowBanner: true,
            shouldShowList: true,
            // High priority notifications show in foreground
            _displayInForeground: notificationType === NotificationTypeEnum.EVENT_REMINDER || 
                                notificationType === NotificationTypeEnum.EVENT_CANCELLED
          };
        },
      });

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupNotificationChannels();
      }

      this.isInitialized = true;
      console.log('Push notification service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize push notification service:', error);
      throw error;
    }
  }

  /**
   * Set up notification channels for Android
   */
  private static async setupNotificationChannels(): Promise<void> {
    const channels = [
      {
        id: 'event_reminder',
        name: 'Event Reminders',
        importance: Notifications.AndroidImportance.HIGH,
        sound: 'default',
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B35'
      },
      {
        id: 'event_update',
        name: 'Event Updates',
        importance: Notifications.AndroidImportance.HIGH,
        sound: 'default',
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B35'
      },

      {
        id: 'ticket',
        name: 'Ticket Notifications',
        importance: Notifications.AndroidImportance.DEFAULT,
        sound: 'default',
        vibrationPattern: [0, 250],
        lightColor: '#2196F3'
      },
      {
        id: 'organizer',
        name: 'Organizer Messages',
        importance: Notifications.AndroidImportance.DEFAULT,
        sound: 'default',
        vibrationPattern: [0, 250],
        lightColor: '#9C27B0'
      },
      {
        id: 'system',
        name: 'System Announcements',
        importance: Notifications.AndroidImportance.DEFAULT,
        sound: 'default',
        vibrationPattern: [0, 250],
        lightColor: '#FF9800'
      },
      {
        id: 'promotional',
        name: 'Promotional Messages',
        importance: Notifications.AndroidImportance.LOW,
        sound: 'default',
        vibrationPattern: [0, 250],
        lightColor: '#795548'
      }
    ];

    for (const channel of channels) {
      await Notifications.setNotificationChannelAsync(channel.id, channel);
    }

    console.log('Android notification channels configured successfully');
  }

  /**
   * Register device for push notifications and save token
   */
  static async registerDevice(userId: string): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications require a physical device');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowDisplayInCarPlay: true,
            allowCriticalAlerts: false,
            provideAppNotificationSettings: true,
            allowProvisional: false,
          },
        });
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Push notification permission not granted');
        return null;
      }

      // Get Expo push token
      const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;
      if (!projectId) {
        throw new Error('Project ID not found in app configuration');
      }

      const pushToken = await Notifications.getExpoPushTokenAsync({ projectId });
      const tokenString = pushToken.data;

      // Save or update device token in database
      await this.saveDeviceToken(userId, tokenString);

      console.log('Device registered for push notifications successfully');
      return tokenString;
    } catch (error) {
      console.error('Failed to register device for push notifications:', error);
      return null;
    }
  }

  /**
   * Save device token to database
   */
  private static async saveDeviceToken(userId: string, token: string): Promise<void> {
    try {
      const deviceModel = await Device.getDeviceTypeAsync();
      const platform = Platform.OS as 'ios' | 'android';

      const { error } = await supabase
        .from('user_device_tokens')
        .upsert({
          user_id: userId,
          expo_push_token: token,
          device_platform: platform,
          device_model: Device.modelName || deviceModel.toString(),
          app_version: Constants?.expoConfig?.version || '1.0.0',
          is_active: true,
          last_used_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,expo_push_token',
          ignoreDuplicates: false
        });

      if (error) {
        throw error;
      }

      console.log('Device token saved to database successfully');
    } catch (error) {
      console.error('Failed to save device token:', error);
      throw error;
    }
  }

  /**
   * Setup notification listeners for deep linking and response handling
   */
  static setupNotificationListeners(navigation: any): () => void {
    // Handle notification received while app is running
    const notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      const data = notification.request.content.data as NotificationData;
      
      // Update badge count or handle in-app notifications
      this.handleNotificationReceived(data);
    });

    // Handle notification response (user tapped notification)
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', response);
      const data = response.notification.request.content.data as NotificationData;
      
      this.handleNotificationResponse(data, navigation);
    });

    // Cleanup function
    return () => {
      Notifications.removeNotificationSubscription(notificationListener);
      Notifications.removeNotificationSubscription(responseListener);
    };
  }

  /**
   * Handle notification received while app is active
   */
  private static handleNotificationReceived(data: NotificationData): void {
    // Log for analytics
    console.log(`Notification received: ${data.type}`, data);
    
    // Update app badge or trigger in-app updates
    if (data.type === NotificationTypeEnum.EVENT_REMINDER) {
      // Could trigger a refresh of event data
    }
  }

  /**
   * Handle notification response (navigation)
   */
  private static handleNotificationResponse(data: NotificationData, navigation: any): void {
    console.log(`Handling notification response: ${data.type}`, data);

    try {
      switch (data.type) {
        case NotificationTypeEnum.EVENT_REMINDER:
        case NotificationTypeEnum.EVENT_UPDATED:
        case NotificationTypeEnum.EVENT_CANCELLED:
          if (data.sessionId && data.eventId) {
            navigation.navigate('EventDetails', { 
              eventId: data.eventId,
              sessionId: data.sessionId 
            });
          }
          break;



        case NotificationTypeEnum.TICKET_VALIDATED:
          if (data.sessionId) {
            navigation.navigate('Ticket', { sessionId: data.sessionId });
          }
          break;

        case NotificationTypeEnum.ORGANIZER_MESSAGE:
          if (data.eventId) {
            navigation.navigate('EventDetails', { eventId: data.eventId });
          }
          break;

        case NotificationTypeEnum.SYSTEM_ANNOUNCEMENT:
          navigation.navigate('Profile');
          break;

        case NotificationTypeEnum.PROMOTIONAL:
          navigation.navigate('Find');
          break;

        default:
          console.warn(`Unhandled notification type: ${data.type}`);
      }
    } catch (error) {
      console.error('Error handling notification response:', error);
    }
  }



  /**
   * Clear all notifications
   */
  static async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync();
      console.log('All notifications cleared');
    } catch (error) {
      console.error('Failed to clear notifications:', error);
    }
  }

  /**
   * Set badge count
   */
  static async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Failed to set badge count:', error);
    }
  }

  /**
   * Get last notification response for deep linking on app launch
   */
  static async getLastNotificationResponse(): Promise<NotificationData | null> {
    try {
      const response = await Notifications.getLastNotificationResponseAsync();
      return response?.notification.request.content.data as NotificationData || null;
    } catch (error) {
      console.error('Failed to get last notification response:', error);
      return null;
    }
  }

  /**
   * Unregister device for push notifications (mark tokens as inactive)
   */
  static async unregisterDevice(userId: string): Promise<void> {
    try {
      console.log('Unregistering device for user:', userId);
      
      // Mark all tokens for this user as inactive
      const { data, error } = await supabase
        .from('user_device_tokens')
        .update({ 
          is_active: false,
          last_used_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select();

      if (error) {
        console.error('Supabase error during unregister:', error);
        throw error;
      }

      console.log('Device unregistered successfully, updated rows:', data?.length || 0);
      console.log('Updated tokens:', data);
    } catch (error) {
      console.error('Failed to unregister device for push notifications:', error);
      throw error;
    }
  }
} 