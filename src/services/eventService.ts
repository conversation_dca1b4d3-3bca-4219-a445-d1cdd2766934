import { supabase } from '../lib/supabase';
import { Database } from '../types/database';
import { cacheService } from './cacheService';

type Event = Database['public']['Tables']['events']['Row'];
type EventSession = Database['public']['Tables']['event_sessions']['Row'];
type OrganizerProfile = Database['public']['Tables']['organizer_profiles']['Row'];

// 分页结果接口
export interface PaginatedResult<T> {
  data: T[];
  totalCount: number;
  hasMore: boolean;
  page: number;
  pageSize: number;
}

// 添加价格计算工具函数
const calculateEventPrice = (sessions: { price: number | null }[]): number => {
  // 检查是否有价格为0或null的场次
  const hasZeroPrice = sessions.some(session => session.price === 0 || session.price === null);
  if (hasZeroPrice) {
    return 0;
  }
  
  // 找出所有有效价格中的最低价
  const validPrices = sessions.filter(session => session.price !== null && session.price > 0)
    .map(session => session.price as number);
  
  return validPrices.length > 0 ? Math.min(...validPrices) : 0;
};

export const eventService = {
  // 获取趋势活动列表（带分页，只获取卡片必要字段）
  async getTrendingEvents(page = 0, pageSize = 10, useCache = true): Promise<PaginatedResult<Event>> {
    // 如果允许使用缓存且是第一页，尝试从缓存获取数据
    if (useCache && page === 0) {
      const cachedData = await cacheService.getCachedTrendingEvents();
      if (cachedData) {
        console.log('Using cached trending events data');
        return {
          data: cachedData.events,
          totalCount: cachedData.events.length,
          hasMore: cachedData.hasMore,
          page: cachedData.page,
          pageSize
        };
      }
    }

    console.log(`Fetching trending events, page: ${page}, page size: ${pageSize}...`);
    
    try {
      // 首先获取总数
      const { count } = await supabase
        .from('events')
        .select('*', { count: 'exact', head: true })
        .eq('is_verified', true);

      const totalCount = count || 0;
      
      // 如果请求的页面超出范围，返回空数据
      const start = page * pageSize;
      if (start >= totalCount) {
        return {
          data: [],
          totalCount,
          hasMore: false,
          page,
          pageSize
        };
      }

      // 获取实际数据
      const { data, error } = await supabase
        .from('events')
        .select(`
          event_id,
          title,
          location,
          image,
          is_featured,
          is_preview,
          tags,
          sessions:event_sessions(
            id,
            start_time,
            price,
            status
          ),
          organizer:organizer_profiles!organizer_id(
            organizer_id,
            display_name
          )
        `)
        .eq('is_verified', true);

      if (error) {
        console.error('Failed to fetch trending events:', error);
        throw error;
      }

      // 修改数据处理逻辑
      const processedData = data?.map(event => {
        return {
          ...event,
          price: calculateEventPrice(event.sessions)
        };
      }) || [];

      // 判断活动是否已结束
      const isEventEnded = (event: any) => {
        if (event.is_preview) return false; // Coming Soon活动永远不算已结束
        if (!event.sessions || event.sessions.length === 0) return false;
        const now = new Date();
        return event.sessions.every((session: any) => {
          const sessionStartTime = new Date(session.start_time);
          return sessionStartTime <= now || session.status === 'pause';
        });
      };

      // 分离已结束和未结束的活动
      const activeEvents = processedData.filter(event => !isEventEnded(event));
      const endedEvents = processedData.filter(event => isEventEnded(event));

      // 随机排序
      const shuffleArray = (array: any[]) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
      };

      // 合并：随机排序的活跃活动 + 随机排序的已结束活动
      const sortedData = [
        ...shuffleArray(activeEvents),
        ...shuffleArray(endedEvents)
      ];

      // 应用分页
      const paginatedData = sortedData.slice(start, start + pageSize);

      // 如果是第一页，缓存数据
      if (page === 0) {
        await cacheService.cacheTrendingEvents(
          paginatedData,
          page,
          totalCount > (page + 1) * pageSize
        );
      }

      console.log('Successfully fetched trending events data:', paginatedData.length, 'records, total:', totalCount);
      return {
        data: paginatedData,
        totalCount,
        hasMore: totalCount > (page + 1) * pageSize,
        page,
        pageSize
      };
    } catch (error) {
      console.error('Failed to fetch trending events:', error);
      throw error;
    }
  },

  // ✅ 重构：获取特定事件（使用数据库库存字段）
  async getEventById(id: string, useCache = true) {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        sessions:event_sessions(
          *
        ),
        organizer:organizer_profiles!organizer_id(
          *,
          user_profiles!user_id(
            avatar_url
          )
        )
      `)
      .eq('event_id', id)
      .single();

    if (error) throw error;

    // ✅ 使用数据库中的实时库存字段
    if (data && data.sessions) {
      data.sessions = data.sessions.map((session: Database['public']['Tables']['event_sessions']['Row']) => ({
        ...session,
        available: session.capacity - (session.sold_tickets || 0) - (session.reserved_tickets || 0)
      }));
    }

    return data;
  },

  // ✅ 新增：获取场次最新库存信息
  async getSessionLatestInfo(sessionId: string) {
    const { data, error } = await supabase
      .from('event_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error) throw error;

    return {
      ...data,
      available: data.capacity - (data.sold_tickets || 0) - (data.reserved_tickets || 0)
    };
  },

  // ✅ 新增：批量获取多个场次的库存信息
  async getSessionsLatestInfo(sessionIds: string[]) {
    const { data, error } = await supabase
      .from('event_sessions')
      .select('*')
      .in('id', sessionIds);

    if (error) throw error;

    return (data || []).map(session => ({
      ...session,
      available: session.capacity - (session.sold_tickets || 0) - (session.reserved_tickets || 0)
    }));
  },

  // 获取特定组织者
  async getOrganizerById(id: string) {
    const { data, error } = await supabase
      .from('organizer_profiles')
      .select(`
        *,
        user_profiles!user_id(
          avatar_url
        ),
        events!organizer_id (
          *,
          sessions:event_sessions(*)
        )
      `)
      .eq('organizer_id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // 根据标签获取事件（带分页）
  async getEventsByTag(tag: string, page = 0, pageSize = 10): Promise<PaginatedResult<Event>> {
    const start = page * pageSize;
    const end = start + pageSize - 1;
    
    const { data, error, count } = await supabase
      .from('events')
      .select(`
        event_id,
        title,
        location,
        image,
        is_featured,
        tags,
        sessions:event_sessions(
          id,
          start_time,
          price
        ),
        organizer:organizer_profiles!organizer_id(
          organizer_id,
          display_name
        )
      `, { count: 'exact' })
      .eq('is_verified', true)
      .contains('tags', [tag])
      .order('created_at', { ascending: false })
      .range(start, end);

    if (error) throw error;
    
    // 修改数据处理逻辑
    const processedData = data?.map(event => {
      return {
        ...event,
        price: calculateEventPrice(event.sessions)
      };
    }) || [];
    
    return {
      data: processedData,
      totalCount: count || 0,
      hasMore: (count || 0) > end + 1,
      page,
      pageSize
    };
  },

  // 创建新事件
  async createEvent(
    event: Omit<Event, 'event_id' | 'created_at' | 'updated_at'>,
    sessions: Omit<EventSession, 'id' | 'event_id' | 'created_at' | 'updated_at'>[],
    organizer: Omit<OrganizerProfile, 'user_id' | 'created_at' | 'updated_at'>
  ) {
    // 开始事务
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .insert(event)
      .select()
      .single();

    if (eventError) throw eventError;

    // 创建会话
    if (sessions.length > 0) {
      const sessionsWithEventId = sessions.map(session => ({
        ...session,
        event_id: eventData.event_id
      }));

      const { error: sessionsError } = await supabase
        .from('event_sessions')
        .insert(sessionsWithEventId);

      if (sessionsError) throw sessionsError;
    }

    return eventData;
  },

  // 更新事件
  async updateEvent(
    eventId: string,
    eventData: {
      title: string;
      location: string;
      full_address?: string;
      tags: string[];
      description: string;
      image: string;
      description_images?: string[];
      is_preview?: boolean;
    }
  ) {
    try {
      const { error } = await supabase
        .from('events')
        .update({
          title: eventData.title,
          location: eventData.location,
          full_address: eventData.full_address,
          tags: eventData.tags,
          description: eventData.description,
          image: eventData.image,
          description_images: eventData.description_images,
          is_preview: eventData.is_preview,
          updated_at: new Date().toISOString()
        })
        .eq('event_id', eventId);

      return { error };
    } catch (error) {
      console.error('Error updating event:', error);
      throw error;
    }
  },

  // 删除事件
  async deleteEvent(id: string) {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('event_id', id);

    if (error) throw error;
  },

  // 更新会话
  async updateSession(
    id: string,
    session: Partial<Omit<EventSession, 'id' | 'event_id' | 'created_at' | 'updated_at'>>
  ) {
    const { data, error } = await supabase
      .from('event_sessions')
      .update(session)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 搜索活动（带分页）
  async searchEvents(query: string, page = 0, pageSize = 10): Promise<PaginatedResult<Event>> {
    const start = page * pageSize;
    const end = start + pageSize - 1;
    
    const { data, error, count } = await supabase
      .from('events')
      .select(`
        event_id,
        title,
        description,
        location,
        image,
        is_featured,
        tags,
        sessions:event_sessions(
          id,
          start_time,
          price
        ),
        organizer:organizer_profiles!organizer_id(
          organizer_id,
          display_name
        )
      `, { count: 'exact' })
      .eq('is_verified', true)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .range(start, end);

    if (error) throw error;
    
    // 修改数据处理逻辑
    const processedData = data?.map(event => {
      return {
        ...event,
        price: calculateEventPrice(event.sessions)
      };
    }) || [];
    
    return {
      data: processedData,
      totalCount: count || 0,
      hasMore: (count || 0) > end + 1,
      page,
      pageSize
    };
  },

  // 获取组织者的所有活动
  async getOrganizerEvents(userId: string) {
    // 首先获取组织者信息
    const { data: organizerData, error: organizerError } = await supabase
      .from('organizer_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (organizerError) {
      console.error('Failed to get organizer profile:', organizerError);
      return [];
    }
    
    if (!organizerData) return [];
    
    // 获取组织者的所有活动
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        sessions:event_sessions(*)
      `)
      .eq('organizer_id', organizerData.organizer_id)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Failed to get organizer events:', error);
      return [];
    }
    
    return data || [];
  },

  // ✅ 重构：获取活动已售票数（使用数据库字段）
  async getEventSoldTickets(eventId: string) {
    const { data: sessions, error } = await supabase
      .from('event_sessions')
      .select('sold_tickets')
      .eq('event_id', eventId);

    if (error) {
      console.error('Failed to get event sessions:', error);
      return 0;
    }

    // 计算所有场次的已售票数总和
    return (sessions || []).reduce((sum, session) => sum + (session.sold_tickets || 0), 0);
  },
  
  // ✅ 重构：获取场次已售票数（使用数据库字段）
  async getSessionSoldTickets(sessionId: string) {
    const { data: session, error } = await supabase
      .from('event_sessions')
      .select('sold_tickets')
      .eq('id', sessionId)
      .single();

    if (error) {
      console.error('Failed to get session:', error);
      return 0;
    }

    return session?.sold_tickets || 0;
  },




}; 