import { Event } from '../types/event';
import { supabase } from '../lib/supabase';
import { Database } from '../types/database';

export type TicketStatus = {
  status: 'valid' | 'used' | 'expired' | 'cancelled';
  label: string;
  color: string;
};

export type Ticket = Database['public']['Tables']['tickets']['Row'];

export const ticketService = {
  // 获取票据状态
  getTicketStatus(ticket: Ticket, event: Event): TicketStatus {
    const now = new Date();
    const eventDate = event.sessions[0]?.start_time 
      ? new Date(event.sessions[0].start_time) 
      : null;
    
    // 根据票证状态返回对应的显示信息
    switch (ticket.status) {
      case 'used':
        return {
          status: 'used',
          label: 'Used',
          color: '#FF9500'  // 橙色
        };
      case 'cancelled':
        return {
          status: 'cancelled',
          label: 'Cancelled',
          color: '#FF3B30'  // 红色
        };
      default:
        // 如果活动已结束（24小时后）
        if (eventDate && now.getTime() > eventDate.getTime() + 24 * 60 * 60 * 1000) {
          return {
            status: 'expired',
            label: 'Expired',
            color: '#FF3B30'  // 红色
          };
        }
        
        // 默认为有效状态
        return {
          status: 'valid',
          label: 'Valid',
          color: '#4CAF50'  // 绿色
        };
    }
  },

  // 格式化票据ID为可读性更好的票号
  formatTicketId(ticketId: string): string {
    if (!ticketId) return '';
    // 删除破折号，截取前12位并转为大写
    return `T${ticketId.replace(/-/g, '').substring(0, 12).toUpperCase()}`;
  },

  // 尝试将短格式票据ID解析为完整UUID
  async parseTicketId(shortId: string): Promise<string | null> {
    // 如果已经是UUID格式，直接返回
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(shortId)) {
      return shortId.toLowerCase();
    }
    
    // 移除T前缀和所有非字母数字字符
    let cleaned = shortId.replace(/^T/, '').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    
    // 如果是短代码形式
    if (cleaned.length > 0) {
      try {
        // 查询数据库中所有票据
        const { data, error } = await supabase
          .from('tickets')
          .select('ticket_id');
        
        if (error) throw error;
        
        if (data && data.length > 0) {
          // 遍历所有票据，查找匹配的票据ID
          for (const ticket of data) {
            const ticketIdWithoutHyphens = ticket.ticket_id.replace(/-/g, '').toLowerCase();
            if (ticketIdWithoutHyphens.startsWith(cleaned)) {
              return ticket.ticket_id;
            }
          }
        }
      } catch (error) {
        console.error('Error parsing ticket ID:', error);
      }
    }
    
    return null;
  },

  // 验证票据
  validateTicket(ticketId: string): boolean {
    // 检查票据ID是否符合UUID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(ticketId);
  },

  // 格式化票据日期时间
  formatTicketDateTime(dateTimeStr: string | null): string {
    if (!dateTimeStr) return 'TBD';
    
    const date = new Date(dateTimeStr);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) return 'Invalid Date';
    
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    
    return date.toLocaleString('en-US', options);
  },

  // 获取订单下的所有票据
  async getTicketsByOrderId(orderId: string): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // 创建票据
  async createTicket(ticketData: {
    order_id: string;
    status?: string;
  }): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .insert([
        {
          order_id: ticketData.order_id,
          status: ticketData.status || 'valid',
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 批量创建票据
  async createTickets(orderId: string, quantity: number): Promise<Ticket[]> {
    // 创建多个票据基础记录
    const ticketsToCreate = Array(quantity)
      .fill(0)
      .map(() => ({
        order_id: orderId,
        status: 'valid',
      }));

    const { data, error } = await supabase
      .from('tickets')
      .insert(ticketsToCreate)
      .select();

    if (error) throw error;
    return data || [];
  },

  // 更新票据状态
  async updateTicketStatus(ticketId: string, status: string): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .update({ status })
      .eq('ticket_id', ticketId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 获取特定票据
  async getTicketById(ticketId: string): Promise<Ticket | null> {
    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .eq('ticket_id', ticketId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // PGRST116表示没有找到记录
        return null;
      }
      throw error;
    }
    
    return data;
  },
  
  // 检查票据是否存在
  async checkTicketsExistForOrder(orderId: string): Promise<boolean> {
    const { count, error } = await supabase
      .from('tickets')
      .select('*', { count: 'exact', head: true })
      .eq('order_id', orderId);
      
    if (error) throw error;
    return (count || 0) > 0;
  },
  
  // 获取用户的所有票据
  async getUserTickets(userId: string): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select(`
        *,
        order:orders!inner(
          *,
          user_id,
          event_sessions(*)
        )
      `)
      .eq('order.user_id', userId)
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    return data || [];
  },

  // 获取票据验证所需的最小信息集
  async getTicketValidationInfo(ticketId: string): Promise<{
    ticket: Ticket;
    eventTitle: string;
    organizerUserId: string | null;
  } | null> {
    const { data, error } = await supabase
      .from('tickets')
      .select(`
        *,
        order:orders!inner(
          session:event_sessions!inner(
            event:events(
              title,
              organizer:organizer_profiles(
                user_id
              )
            )
          )
        )
      `)
      .eq('ticket_id', ticketId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw error;
    }

    return {
      ticket: data,
      eventTitle: data.order.session.event.title,
      organizerUserId: data.order.session.event.organizer?.user_id || null
    };
  }
}; 