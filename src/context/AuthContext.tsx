import React, { createContext, useState, useEffect, useContext, useRef } from 'react';
import { supabase } from '../lib/supabase';
import { User, Session, AuthChangeEvent } from '@supabase/supabase-js';
import { Alert } from 'react-native';
import { PushNotificationService } from '../services/pushNotificationService';
import { cacheService } from '../services/cacheService';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isLoggedIn: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithApple: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 检查 session 状态
const checkSessionStatus = (session: Session | null): {
  isValid: boolean;
  timeRemaining: number;
} => {
  if (!session?.expires_at || !session?.access_token) {
    return { isValid: false, timeRemaining: 0 };
  }
  
  const expiresAtMs = session.expires_at * 1000;
  const now = Date.now();
  const timeRemaining = expiresAtMs - now;
  
  return {
    isValid: timeRemaining > 0,
    timeRemaining
  };
};

interface AuthProviderProps {
  children: React.ReactNode;
  session: Session | null;
  authIsReady: boolean;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ 
  children, 
  session: initialSession, 
  authIsReady 
}) => {
  const [user, setUser] = useState<User | null>(initialSession?.user || null);
  const [session, setSession] = useState<Session | null>(initialSession);
  const [loading, setLoading] = useState(!authIsReady);
  const [lastPushRegisterUserId, setLastPushRegisterUserId] = useState<string | null>(null);

  // 统一的错误处理函数
  const handleAuthError = (error: any, message: string) => {
    console.error(message, error);
    // 在生产环境中可能不想显示所有错误给用户
    if (__DEV__) {
      Alert.alert('Authentication Error', error.message || message);
    }
  };

  // 注册推送通知设备（避免重复注册）
  const registerPushNotifications = async (userId: string) => {
    if (lastPushRegisterUserId === userId) {
      return; // 避免重复注册
    }
    
    try {
      const token = await PushNotificationService.registerDevice(userId);
      if (token) {
        setLastPushRegisterUserId(userId);
        console.log('Push notification device registered successfully');
      }
    } catch (error) {
      console.error('Failed to register push notifications:', error);
      // 不显示错误给用户，因为推送通知不是核心功能
    }
  };

  // 响应从App.tsx传来的session变化
  useEffect(() => {
    console.log('AuthContext - Session prop changed:', !!initialSession);
    setSession(initialSession);
    setUser(initialSession?.user || null);
    
    // 如果有新的session且有效，注册推送通知
    const { isValid } = checkSessionStatus(initialSession);
    if (initialSession && isValid && initialSession.user?.id) {
      registerPushNotifications(initialSession.user.id);
    }
  }, [initialSession]);

  // 用户信息刷新 - Supabase内置的自动刷新
  const refreshUser = async () => {
    try {
      // 使用getSession()获取当前会话，Supabase会自动处理过期token的刷新
      const { data, error } = await supabase.auth.getSession();
      if (error) throw error;
      
      setSession(data.session);
      setUser(data.session?.user || null);
      
      // 如果会话有效且用户存在，注册推送通知
      if (data.session?.user?.id) {
        const { isValid } = checkSessionStatus(data.session);
        if (isValid) {
          await registerPushNotifications(data.session.user.id);
        }
      } else {
        // 清理状态
        setLastPushRegisterUserId(null);
      }
    } catch (error) {
      handleAuthError(error, 'Unable to refresh your session');
      // 会话无效时清理状态
      setSession(null);
      setUser(null);
      setLastPushRegisterUserId(null);
    }
  };

  // 处理认证状态变化
  useEffect(() => {
    if (!authIsReady) return;
    setLoading(false);
  }, [authIsReady]);

  const value = {
    user,
    session,
    loading,
    isLoggedIn: !!session?.user && checkSessionStatus(session).isValid,
    signIn: async (email: string, password: string) => {
      try {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        if (error) throw error;
      } catch (error: any) {
        handleAuthError(error, 'Sign in failed');
        throw error;
      }
    },
    signInWithApple: async () => {
      try {
        const { error } = await supabase.auth.signInWithOAuth({
          provider: 'apple',
          options: {
            redirectTo: 'lezigo://auth/callback',
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            },
          },
        });
        if (error) throw error;
      } catch (error: any) {
        handleAuthError(error, 'Apple sign in failed');
        throw error;
      }
    },
    signOut: async () => {
      try {
        console.log('Starting sign out process...');

        // 1. Clean up push notification tokens before signing out
        if (user?.id) {
          console.log('Cleaning up push tokens for user:', user.id);
          await PushNotificationService.unregisterDevice(user.id);
        } else {
          console.log('No user ID available for push token cleanup');
        }

        // 2. Clear all local notifications and reset badge count
        console.log('Clearing all local notifications...');
        await PushNotificationService.clearAllNotifications();
        await PushNotificationService.setBadgeCount(0);

        // 3. Clear all local cache data
        console.log('Clearing all local cache data...');
        await cacheService.clearAllCache();

        // 4. Sign out from Supabase (this will clear auth storage)
        console.log('Signing out from Supabase...');
        const { error } = await supabase.auth.signOut();
        if (error) throw error;

        // 5. Clear application state
        setSession(null);
        setUser(null);
        setLastPushRegisterUserId(null);

        console.log('Sign out completed successfully');
      } catch (error: any) {
        console.error('Error during sign out:', error);
        handleAuthError(error, 'Sign out failed');
        throw error;
      }
    },
    refreshUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 