import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import * as SecureStore from 'expo-secure-store';

// 替换为您的 Supabase URL 和 anon key
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL as string;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY as string;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase URL or anon key. Please check your environment variables.');
}

// 实现安全的存储机制
const ExpoSecureStoreAdapter = {
  getItem: async (key: string) => {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      console.error('Error getting auth storage:', error);

      // 处理iOS系统限制错误
      if (error instanceof Error) {
        if (error.message.includes('User interaction is not allowed')) {
          console.warn('iOS system restriction: Keychain access temporarily unavailable');
          return null; // 静默处理，不抛出异常
        }
        if (error.message.includes('keychain')) {
          throw new Error('Keychain access failed. Please check device security settings.');
        }
      }
      return null;
    }
  },
  setItem: async (key: string, value: string) => {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      console.error('Error setting auth storage:', error);
      // 对于关键存储错误，抛出异常以便上层处理
      if (error instanceof Error && error.message.includes('keychain')) {
        throw new Error('Failed to save authentication data. Please check device security settings.');
      }
      throw error;
    }
  },
  removeItem: async (key: string) => {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error('Error removing auth storage:', error);
      // 删除失败通常不是致命错误，只记录日志
      if (error instanceof Error && error.message.includes('keychain')) {
        console.warn('Failed to remove authentication data from keychain');
      }
    }
  }
};

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    // ✅ 自动刷新token - Supabase会在token过期前90秒自动刷新
    autoRefreshToken: true,
    // ✅ 持久化会话 - 在设备重启后保持登录状态
    persistSession: true,
    // ✅ 禁用URL检测 - React Native中不需要，避免不必要的检查
    detectSessionInUrl: false,
    // ✅ 安全存储 - 使用设备加密存储保护认证信息
    storage: ExpoSecureStoreAdapter,
    // ✅ PKCE流程 - 更安全的OAuth流程，推荐用于移动应用
    flowType: 'pkce',
    // ✅ 调试模式 - 仅在开发环境启用，便于追踪认证问题
    debug: false,
  },
}); 