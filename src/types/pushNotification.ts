export enum NotificationTypeEnum {
  EVENT_REMINDER = 'event_reminder',
  EVENT_CANCELLED = 'event_cancelled',
  EVENT_UPDATED = 'event_updated',
  TICKET_VALIDATED = 'ticket_validated',
  ORGANIZER_MESSAGE = 'organizer_message',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  PROMOTIONAL = 'promotional'
}

export interface NotificationData {
  type: NotificationTypeEnum;
  sessionId?: string;
  eventId?: string;
  orderId?: string;
  userId?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

export interface ScheduledNotificationRecord {
  id: string;
  user_id: string;
  notification_type: NotificationTypeEnum;
  scheduled_for: string;
  reference_id: string; // session_id, event_id, order_id, etc.
  sent_at?: string;
  failed_at?: string;
  error_message?: string;
  retry_count: number;
  created_at: string;
  updated_at: string;
}

export interface PushNotificationPayload {
  to: string;
  title: string;
  body: string;
  data: NotificationData;
  sound?: string;
  badge?: number;
  priority?: 'default' | 'high' | 'max';
  channelId?: string;
  _displayInForeground?: boolean;
}

export interface NotificationPreferences {
  user_id: string;
  event_reminders: boolean;
  event_updates: boolean;
  promotional_messages: boolean;
  push_enabled: boolean;
  email_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface DeviceToken {
  id: string;
  user_id: string;
  expo_push_token: string;
  device_platform: 'ios' | 'android';
  device_model?: string;
  app_version?: string;
  is_active: boolean;
  last_used_at: string;
  created_at: string;
  updated_at: string;
} 