import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Event } from './event';
import { NavigatorScreenParams, CompositeScreenProps, RouteProp } from '@react-navigation/native';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Session 类型定义
export type Session = {
  id?: string;
  title: string;
  start_time: string;
  price: number | null;
  capacity: number;
  max_tickets_per_user?: number;
  status: 'available' | 'pause';
  sale_start_time?: string;
  sale_end_time?: string;
}

export type TabParamList = {
  HomeTab: undefined;
  Find: undefined;
  Orders: undefined;
  Profile: undefined;
};

export type RootStackParamList = {
  SignIn: {
    redirectScreen?: keyof RootStackParamList;
    redirectParams?: any;
  };
  SignUp: undefined;
  OTPVerification: { email: string; alreadySent?: boolean };

  Home: NavigatorScreenParams<TabParamList>;
  EventDetails: { eventId: string };
  TagEvents: { tagId: string };
  ProfileScreen: undefined;
  EditProfile: undefined;
  ChangePassword: undefined;
  Ticket: { eventId: string; orderId: string };
  OrganizerScreen: { organizerId: string };
  FavoriteEvents: undefined;
  OrganizerManagement: undefined;
  TicketValidation: undefined;
  ForgotPassword: undefined;
  ManageEvents: undefined;
  EventForm: { 
    event?: any;
    session?: Session;
    isEditing?: boolean;
  };
  SessionAttendees: { sessionId: string; sessionTitle: string };
  SessionForm: {
    session?: Session;
    isEditing?: boolean;
    onSave?: (session: Session) => void;
  };
  WebView: { url: string; title?: string };
  RevenueReport: undefined;
  NotificationSettings: undefined;
};

export type HomeScreenProps = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, 'HomeTab'>,
  NativeStackScreenProps<RootStackParamList>
>;

export type FindScreenProps = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, 'Find'>,
  NativeStackScreenProps<RootStackParamList>
>;

export type ProfileScreenProps = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, 'Profile'>,
  NativeStackScreenProps<RootStackParamList>
>;

export type OrdersScreenProps = CompositeScreenProps<
  BottomTabScreenProps<TabParamList, 'Orders'>,
  NativeStackScreenProps<RootStackParamList>
>;

export type EventDetailsScreenProps = NativeStackScreenProps<RootStackParamList, 'EventDetails'>;
export type TagEventsScreenProps = NativeStackScreenProps<RootStackParamList, 'TagEvents'>;
export type OrganizerScreenProps = NativeStackScreenProps<RootStackParamList, 'OrganizerScreen'>;
export type EditProfileScreenProps = NativeStackScreenProps<RootStackParamList, 'EditProfile'>;
export type ChangePasswordScreenProps = NativeStackScreenProps<RootStackParamList, 'ChangePassword'>;
export type TicketScreenProps = NativeStackScreenProps<RootStackParamList, 'Ticket'>;
export type SignUpScreenProps = NativeStackScreenProps<RootStackParamList, 'SignUp'>;
export type SignInScreenProps = NativeStackScreenProps<RootStackParamList, 'SignIn'>;
export type OrganizerManagementScreenProps = NativeStackScreenProps<RootStackParamList, 'OrganizerManagement'>;
export type TicketValidationScreenProps = NativeStackScreenProps<RootStackParamList, 'TicketValidation'>;
export type WebViewScreenProps = NativeStackScreenProps<RootStackParamList, 'WebView'>;
export type RevenueReportScreenProps = NativeStackScreenProps<RootStackParamList, 'RevenueReport'>;
export type NotificationSettingsScreenProps = NativeStackScreenProps<RootStackParamList, 'NotificationSettings'>;

export type OTPVerificationScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'OTPVerification'>;
  route: RouteProp<RootStackParamList, 'OTPVerification'>;
}; 
