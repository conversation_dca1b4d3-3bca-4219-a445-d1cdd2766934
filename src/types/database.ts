export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      events: {
        Row: {
          event_id: string
          title: string
          location: string
          full_address: string | null
          image: string
          is_featured: boolean
          is_verified: boolean
          tags: string[]
          description: string | null
          description_images: string[] | null
          is_preview: boolean
          created_at: string
          updated_at: string
          organizer_id: string
        }
        Insert: {
          event_id?: string
          title: string
          location: string
          full_address?: string | null
          image: string
          is_featured?: boolean
          is_verified?: boolean
          tags?: string[]
          description?: string | null
          description_images?: string[] | null
          is_preview?: boolean
          created_at?: string
          updated_at?: string
          organizer_id: string
        }
        Update: {
          event_id?: string
          title?: string
          location?: string
          full_address?: string | null
          image?: string
          is_featured?: boolean
          is_verified?: boolean
          tags?: string[]
          description?: string | null
          description_images?: string[] | null
          is_preview?: boolean
          created_at?: string
          updated_at?: string
          organizer_id?: string
        }
      }
      event_sessions: {
        Row: {
          id: string
          event_id: string
          title: string
          start_time: string
          price: number | null
          capacity: number
          status: 'available' | 'pause'
          created_at: string
          updated_at: string
          max_tickets_per_user?: number
          sale_start_time?: string
          sale_end_time?: string
        }
        Insert: {
          id?: string
          event_id: string
          title: string
          start_time: string
          price?: number | null
          capacity: number
          status?: 'available' | 'pause'
          created_at?: string
          updated_at?: string
          max_tickets_per_user?: number
          sale_start_time?: string
          sale_end_time?: string
        }
        Update: {
          id?: string
          event_id?: string
          title?: string
          start_time?: string
          price?: number | null
          capacity?: number
          status?: 'available' | 'pause'
          created_at?: string
          updated_at?: string
          max_tickets_per_user?: number
          sale_start_time?: string
          sale_end_time?: string
        }
      }
      event_organizers: {
        Row: {
          id: string
          name: string
          avatar: string | null
          role: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          avatar?: string | null
          role?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          avatar?: string | null
          role?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      events_organizers: {
        Row: {
          event_id: string
          organizer_id: string
          created_at: string
        }
        Insert: {
          event_id: string
          organizer_id: string
          created_at?: string
        }
        Update: {
          event_id?: string
          organizer_id?: string
          created_at?: string
        }
      }
      organizer_profiles: {
        Row: {
          user_id: string
          organizer_id: string
          display_name: string | null
          avatar_url: string | null
          verification_status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          organizer_id?: string
          display_name?: string | null
          avatar_url?: string | null
          verification_status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          organizer_id?: string
          display_name?: string | null
          avatar_url?: string | null
          verification_status?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          user_id: string
          display_name: string | null
          avatar_url: string | null
          email: string
          is_organizer: boolean
          updated_at: string
        }
        Insert: {
          user_id: string
          display_name?: string | null
          avatar_url?: string | null
          email: string
          is_organizer?: boolean
          updated_at?: string
        }
        Update: {
          user_id?: string
          display_name?: string | null
          avatar_url?: string | null
          email?: string
          is_organizer?: boolean
          updated_at?: string
        }
      }
      orders: {
        Row: {
          order_id: string;
          user_id: string;
          session_id: string;
          quantity: number;
          total_amount: number;
          is_paid: boolean;
          payment_method: string | null;
          payment_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          order_id?: string;
          user_id: string;
          session_id: string;
          quantity: number;
          total_amount: number;
          is_paid?: boolean;
          payment_method?: string | null;
          payment_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          order_id?: string;
          user_id?: string;
          session_id?: string;
          quantity?: number;
          total_amount?: number;
          is_paid?: boolean;
          payment_method?: string | null;
          payment_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      }
      tickets: {
        Row: {
          ticket_id: string;
          order_id: string;
          status: string;
          check_in_time: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          ticket_id?: string;
          order_id: string;
          status: string;
          check_in_time?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          ticket_id?: string;
          order_id?: string;
          status?: string;
          check_in_time?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      }
      favorite_events: {
        Row: {
          id: string;
          user_id: string;
          event_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          event_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          event_id?: string;
          created_at?: string;
        };
      }
    }
  }
} 