export interface User {
  id: string;
  email: string;
  avatar_url?: string;
  updated_at?: string;
  is_organizer: boolean;
}

export interface UserProfile {
  user_id: string;
  avatar_url?: string;
  updated_at: string;
  is_organizer: boolean;
}

export interface OrganizerProfile {
  user_id: string;
  organizer_id: string;
  verification_status: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfileFormData {
  avatar_url?: string;
}

export interface PasswordChangeFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
} 