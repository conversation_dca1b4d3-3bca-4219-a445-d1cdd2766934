import { Database } from './database';

// 扩展event_sessions类型，添加available字段
export type ExtendedEventSession = Database['public']['Tables']['event_sessions']['Row'] & {
  id: string;
  available: number;
  orders?: {
    quantity: number;
    is_paid: boolean;
  }[];
};

export type Event = Database['public']['Tables']['events']['Row'] & {
  sessions: ExtendedEventSession[];
  organizer: Database['public']['Tables']['organizer_profiles']['Row'] & {
    id: string;
  };
  date?: string;
  fullAddress: string | null;
  isFeatured: boolean;
  participants: {
    current: number;
    total: number;
  };
  price: number;
}; 