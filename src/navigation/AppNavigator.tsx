import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import HomeScreen from '../screens/HomeScreen';
import EventDetailsScreen from '../screens/EventDetailsScreen';
import FindScreen from '../screens/FindScreen';
import TagEventsScreen from '../screens/TagEventsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import OrdersScreen from '../screens/OrdersScreen';
import TicketScreen from '../screens/TicketScreen';
import ChangePasswordScreen from '../screens/ChangePasswordScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import { View, Text, Platform } from 'react-native';
import { RootStackParamList, TabParamList } from '../types/navigation';
import OrganizerScreen from '../screens/OrganizerScreen';
import SignInScreen from '../screens/SignInScreen';
import SignUpScreen from '../screens/SignUpScreen';

import { Session } from '@supabase/supabase-js';
import FavoriteEventsScreen from '../screens/FavoriteEventsScreen';
import OrganizerManagementScreen from '../screens/organizer/OrganizerManagementScreen';
import TicketValidationScreen from '../screens/organizer/TicketValidationScreen';
import ManageEventsScreen from '../screens/organizer/ManageEventsScreen';
import EventFormScreen from '../screens/organizer/EventFormScreen';
import SessionAttendeesScreen from '../screens/organizer/SessionAttendeesScreen';
import { BlurView } from 'expo-blur';
import { COLORS } from '../config/theme';
import { useAuth } from '../context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SessionFormScreen from '../screens/organizer/SessionFormScreen';
import WebViewScreen from '../screens/WebViewScreen';
import RevenueReportScreen from '../screens/organizer/RevenueReportScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import { PushNotificationService } from '../services/pushNotificationService';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

function TabNavigator() {
  const { session } = useAuth();
  const isLoggedIn = !!session;

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: Platform.select({
          ios: {
            backgroundColor: 'transparent',
            borderTopColor: 'rgba(34, 34, 34, 0.3)',
            position: 'absolute',
            elevation: 0,
            height: 85,
            paddingBottom: 20,
          },
          android: {
            backgroundColor: '#000',
            borderTopColor: '#222',
          },
        }),
        tabBarBackground: Platform.select({
          ios: () => (
            <BlurView
              tint="dark"
              intensity={100}
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: 85,
              }}
            />
          ),
          android: undefined,
        }),
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: '#666',
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused ? "home" : "home-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Find"
        component={FindScreen}
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused ? "search" : "search-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Orders"
        component={OrdersScreen}
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused ? "receipt" : "receipt-outline"} size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons name={focused ? "person" : "person-outline"} size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

export default function AppNavigator() {
  const { session, loading } = useAuth();
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  // 设置推送通知监听器
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    // 只有在用户登录后才设置监听器
    if (session?.user) {
      // 创建一个导航对象用于推送通知处理
      const navigationRef = {
        navigate: (screen: string, params?: any) => {
          // 这里会在实际的导航对象准备好后被替换
          console.log(`Navigate to ${screen} with params:`, params);
        }
      };

      unsubscribe = PushNotificationService.setupNotificationListeners(navigationRef);

      // 检查应用启动时是否有待处理的通知
      PushNotificationService.getLastNotificationResponse()
        .then(data => {
          if (data) {
            console.log('Handling launch notification:', data);
            // 可以在这里处理应用启动时的通知
          }
        })
        .catch(error => {
          console.error('Error getting last notification response:', error);
        });
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [session?.user]);

  // 设置loading超时机制，防止无限loading
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (loading) {
      console.log('AppNavigator - Loading started');
      timeout = setTimeout(() => {
        console.warn('AppNavigator - Loading timeout, forcing continue');
        setLoadingTimeout(true);
      }, 10000); // 10秒超时
    } else {
      console.log('AppNavigator - Loading finished');
      setLoadingTimeout(false);
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [loading]);
  
  // 显示加载状态（除非超时）
  if (loading && !loadingTimeout) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000' }}>
        <Text style={{ color: '#fff', fontSize: 16, marginBottom: 8 }}>Loading...</Text>
        <Text style={{ color: '#666', fontSize: 14, textAlign: 'center', paddingHorizontal: 20 }}>
          Please check your network settings if this takes too long
        </Text>
      </View>
    );
  }

  console.log('AppNavigator - Rendering main navigation, session:', !!session, 'loading:', loading);

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#000',
        },
        headerTintColor: '#fff',
        headerShown: false,
        contentStyle: { backgroundColor: '#000' },
        animation: 'slide_from_right',
        animationDuration: 125,
      }}
      initialRouteName="Home"
    >
      <Stack.Screen name="Home" component={TabNavigator} />
      <Stack.Screen name="EventDetails" component={EventDetailsScreen} />
      <Stack.Screen name="TagEvents" component={TagEventsScreen} />
      <Stack.Screen name="SignIn" component={SignInScreen} />
      <Stack.Screen name="SignUp" component={SignUpScreen} />

      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="ChangePassword" component={ChangePasswordScreen} />
      <Stack.Screen name="Ticket" component={TicketScreen} />
      <Stack.Screen 
        name="OrganizerScreen" 
        component={OrganizerScreen}
        options={{
          headerShown: false
        }}
      />
      <Stack.Screen name="FavoriteEvents" component={FavoriteEventsScreen} />
      <Stack.Screen name="OrganizerManagement" component={OrganizerManagementScreen} />
      <Stack.Screen name="TicketValidation" component={TicketValidationScreen} />
      <Stack.Screen name="ManageEvents" component={ManageEventsScreen} />
      <Stack.Screen name="EventForm" component={EventFormScreen} />
      <Stack.Screen name="SessionAttendees" component={SessionAttendeesScreen} />
      <Stack.Screen name="SessionForm" component={SessionFormScreen} />
      <Stack.Screen name="WebView" component={WebViewScreen} />
      <Stack.Screen name="RevenueReport" component={RevenueReportScreen} />
      <Stack.Screen name="NotificationSettings" component={NotificationSettingsScreen} />
    </Stack.Navigator>
  );
} 