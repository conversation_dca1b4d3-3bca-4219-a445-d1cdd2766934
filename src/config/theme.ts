/**
 * 应用主题配置
 * 集中管理应用中使用的颜色、尺寸和样式常量
 */
import { Platform } from 'react-native';

// 基础颜色
export const COLORS = {
  // 主题色
  PRIMARY: '#E74C26',
  PRIMARY_LIGHT: '#E74C26CC',
  PRIMARY_DARK: '#CD3F1A',
  PRIMARY_TRANSPARENT: '#E74C2633',
  PRIMARY_BORDER: '#E74C2680',
  BORDER_COLOR: '#2c2c2e',
  
  // 背景色
  BACKGROUND_DARK: '#000000',
  BACKGROUND_CARD: '#1A1A1A',
  
  // 文本颜色
  TEXT_WHITE: '#FFFFFF',
  TEXT_LIGHT: '#CCCCCC',
  TEXT_GRAY: '#999999',
  TEXT_DARK: '#333333',
  
  // 功能色
  SUCCESS: '#4CD964',
  WARNING: '#FF9500',
  ERROR: '#FF3B30',
  
  // 常用透明色
  TRANSPARENT_LIGHT: 'rgba(255, 255, 255, 0.1)',
  TRANSPARENT_DARK: 'rgba(0, 0, 0, 0.5)',
};

// 用于渐变的颜色组合
export const GRADIENTS = {
  // 顶部渐变背景 (橙色到黑色)
  PRIMARY_TO_BLACK: ['rgba(231, 76, 38, 0.7)', 'rgba(0, 0, 0, 0.95)'],
  // 图片渐变遮罩 (透明到黑色)
  TRANSPARENT_TO_BLACK: ['transparent', 'rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 0.95)'],
};

// 针对不同平台的字体大小定义
const FONT_SIZES_ANDROID = {
  XSMALL: 10,
  SMALL: 14,
  MEDIUM: 16,
  LARGE: 18,
  XLARGE: 20,
  XXLARGE: 24,
  XXXLARGE: 28,
  TITLE: 28,
};

const FONT_SIZES_IOS = {
  XSMALL: 10,
  SMALL: 14,
  MEDIUM: 16,
  LARGE: 18,
  XLARGE: 20,
  XXLARGE: 24,
  XXXLARGE: 28,
  TITLE: 28,
};

// 应用的字体大小
export const FONT_SIZES = Platform.select({
  ios: FONT_SIZES_IOS,
  android: FONT_SIZES_ANDROID,
  default: FONT_SIZES_IOS,
});

// 针对不同平台的圆角定义
const RADIUS_ANDROID = {
  SMALL: 8,
  MEDIUM: 16,
  LARGE: 20,
  XLARGE: 28,
  ROUND: 50, // 完全圆形
};

const RADIUS_IOS = {
  SMALL: 8,
  MEDIUM: 16,
  LARGE: 20,
  XLARGE: 28,
  ROUND: 50, // 完全圆形
};

// 圆角半径
export const RADIUS = Platform.select({
  ios: RADIUS_IOS,
  android: RADIUS_ANDROID,
  default: RADIUS_IOS,
});

// 针对不同平台的间距定义
const SPACING_ANDROID = {
  XSMALL: 4,
  SMALL: 8,
  MEDIUM: 16,
  LARGE: 24,
  XLARGE: 32,
};

const SPACING_IOS = {
  XSMALL: 4,
  SMALL: 8,
  MEDIUM: 16,
  LARGE: 24,
  XLARGE: 32,
};

// 间距
export const SPACING = Platform.select({
  ios: SPACING_IOS,
  android: SPACING_ANDROID,
  default: SPACING_IOS,
}); 