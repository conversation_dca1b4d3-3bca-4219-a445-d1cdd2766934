import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useCallback, useEffect, useState, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Linking from 'expo-linking';

import { NavigationContainer, DefaultTheme } from '@react-navigation/native';
import AppNavigator from './src/navigation/AppNavigator';
import { supabase } from './src/lib/supabase';
import { Session } from '@supabase/supabase-js';
import { AuthProvider } from './src/context/AuthContext';
import { StripeProvider } from '@stripe/stripe-react-native';
import LottieSplash from './src/components/LottieSplash';
import { PushNotificationService } from './src/services/pushNotificationService';
import Toast from 'react-native-toast-message';
import { toastConfig } from './src/components/CustomToast';

// 1. 防止原生 Splash Screen 自动隐藏
SplashScreen.preventAutoHideAsync();

const navigationTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: '#000',
    card: '#000',
    text: '#fff',
    border: 'transparent',
  },
};

export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const [session, setSession] = useState<Session | null>(null);
  const [showLottieSplash, setShowLottieSplash] = useState(false);
  const splashFadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    let subscription: any;
    
    async function prepare() {
      try {
        // 2. 并行执行所有初始化任务
        await PushNotificationService.initialize();
        console.log('Push notification service initialized');

        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        
        setSession(session);
        
        // 启用认证状态监听器，确保session状态实时更新
        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange((_event, newSession) => {
          console.log('App.tsx - Auth state changed:', _event, !!newSession);

          // 只在非TOKEN_REFRESHED事件或session实际变化时更新状态
          if (_event !== 'TOKEN_REFRESHED') {
            setSession(newSession);
          } else {
            // TOKEN_REFRESHED时静默更新，避免不必要的重新渲染
            setSession(prevSession => {
              // 只有当session实际发生变化时才更新
              if (!prevSession && !newSession) return prevSession;
              if (!prevSession || !newSession) return newSession;
              if (prevSession.access_token !== newSession.access_token) {
                return newSession;
              }
              return prevSession;
            });
          }
        });
        
        subscription = authSubscription;
        
        console.log('Auth initialization completed');
        // lottie animation is a plus, not a must.
        setShowLottieSplash(true); 
      } catch (e) {
        console.warn('Initialization error:', e);
      } finally {
        // 3. 标记初始化完成，准备渲染主应用
        setAppIsReady(true);
      }
    }
    
    prepare();

    // 正确的清理函数位置
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  // 处理深度链接
  useEffect(() => {
    const handleDeepLink = (url: string) => {
      console.log('Deep link received:', url);

      // 处理Supabase认证回调
      if (url.includes('auth/callback')) {
        console.log('Auth callback processed - Supabase will handle session update');
        // Supabase会自动处理认证状态更新
        // 认证成功后，AuthContext会自动更新用户状态
      }
    };

    // 处理应用启动时的深度链接
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url);
      }
    });

    // 监听运行时的深度链接
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => subscription?.remove();
  }, []);

  const onLayoutRootView = useCallback(async () => {
    // 4. 当主应用视图布局完成后，隐藏原生 Splash Screen
    // 这确保了从原生到JS的无缝过渡
    if (appIsReady) {
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  const handleLottieAnimationComplete = useCallback(() => {
    Animated.timing(splashFadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowLottieSplash(false);
    });
  }, [splashFadeAnim]);

  if (!appIsReady) {
    return null;
  }

  return (
    <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
      <SafeAreaProvider>
        <AuthProvider session={session} authIsReady={appIsReady}>
          <StripeProvider
            publishableKey="pk_live_51NcImnFjjy03tBuz7YMN42NvGbj8zEGXu39wf8xklv98PIDlPyzZoo2JV5Uj6JZiGBROv4Cr5d1MVeueFUugqGxR00D0zQNape"
            merchantIdentifier="merchant.com.inthepond.lezigo"
            urlScheme="lezigo"
          >
            <NavigationContainer theme={navigationTheme}>
              <AppNavigator />
            </NavigationContainer>
            <StatusBar style="light" />
          </StripeProvider>
        </AuthProvider>

        {/* Toast 组件 - 必须放在最外层 */}
        <Toast config={toastConfig} />
      </SafeAreaProvider>

      {/* 7. Lottie Splash 覆盖在最上层，动画结束后淡出 */}
      {showLottieSplash && (
        <Animated.View style={[StyleSheet.absoluteFill, { 
          opacity: splashFadeAnim, 
          backgroundColor: '#1C1C1C' // 与 app.json 中颜色保持一致
        }]}>
          <LottieSplash onAnimationFinish={handleLottieAnimationComplete} />
        </Animated.View>
      )}
    </View>
  );
}
