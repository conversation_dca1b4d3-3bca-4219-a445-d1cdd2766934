# 🗺️ 功能映射表

本文档提供了每个功能模块对应的文件列表，帮助开发者快速定位相关代码。

## 📋 目录

- [用户认证系统](#用户认证系统)
- [活动浏览与搜索](#活动浏览与搜索)
- [活动详情与票务购买](#活动详情与票务购买)
- [订单管理](#订单管理)
- [个人中心](#个人中心)
- [主办方功能](#主办方功能)
- [推送通知系统](#推送通知系统)
- [支付系统](#支付系统)

---

## 🔐 用户认证系统

### 功能描述
完整的用户注册、登录、会话管理系统，支持安全存储和自动会话刷新。

### 相关文件

#### 前端组件
- `src/screens/SignInScreen.tsx` - 登录页面
- `src/screens/SignUpScreen.tsx` - 注册页面  
- `src/screens/SetupProfileScreen.tsx` - 个人资料设置页面

#### 状态管理
- `src/context/AuthContext.tsx` - 全局认证状态管理
- `src/hooks/useAppState.ts` - 应用状态监听Hook

#### 类型定义
- `src/types/user.ts` - 用户相关类型定义
- `src/types/session.ts` - 会话类型定义

#### 配置文件
- `src/lib/supabase.ts` - Supabase客户端配置（包含安全存储适配器）

#### 导航配置
- `src/navigation/AppNavigator.tsx` - 认证相关路由配置

### 关键功能点
- ✅ 邮箱密码登录
- ✅ 用户注册
- ✅ 自动会话刷新
- ✅ 安全存储（Expo SecureStore）
- ✅ 推送通知注册
- ✅ 错误处理和重试机制

---

## 🔍 活动浏览与搜索

### 功能描述
用户可以浏览精选活动、热门活动，并通过搜索和标签筛选活动。

### 相关文件

#### 前端组件
- `src/screens/HomeScreen.tsx` - 首页（精选活动 + 热门活动列表）
- `src/screens/FindScreen.tsx` - 搜索页面（活动搜索 + 标签分类）
- `src/screens/TagEventsScreen.tsx` - 标签活动列表页

#### 子组件
- `src/components/FeaturedEvent.tsx` - 精选活动展示组件
- `src/components/EventCard.tsx` - 活动卡片组件
- `src/components/LoadingAnimation.tsx` - 加载动画组件

#### 业务逻辑
- `src/services/eventService.ts` - 活动相关API服务
- `src/services/cacheService.ts` - 数据缓存服务
- `src/services/favoriteService.ts` - 收藏功能服务

#### 类型定义
- `src/types/event.ts` - 活动相关类型扩展
- `src/types/database.ts` - 数据库类型定义

### 关键功能点
- ✅ 精选活动轮播
- ✅ 热门活动无限滚动列表
- ✅ 实时搜索（防抖处理）
- ✅ 标签分类浏览
- ✅ 活动收藏功能
- ✅ 数据缓存优化
- ✅ 分页加载

---

## 🎫 活动详情与票务购买

### 功能描述
展示活动详细信息，支持多场次选择和票务购买流程。

### 相关文件

#### 前端组件
- `src/screens/EventDetailsScreen.tsx` - 活动详情页面
- `src/screens/TicketScreen.tsx` - 电子票详情页面

#### 子组件
- `src/components/TicketConfirmationSheet.tsx` - 票务确认底部弹窗
- `src/components/PaymentSuccessModal.tsx` - 支付成功弹窗

#### 业务逻辑
- `src/services/eventService.ts` - 活动详情获取
- `src/services/orderService.ts` - 订单创建和管理
- `src/services/ticketService.ts` - 票务管理
- `src/services/feeService.ts` - 费用计算服务

#### 后端服务
- `supabase/functions/create-payment-intent/` - 创建支付意向
- `supabase/functions/stripe-webhook/` - 支付回调处理
- `supabase/functions/process-free-tickets/` - 免费票务处理

### 关键功能点
- ✅ 活动详情展示
- ✅ 多场次选择
- ✅ 实时库存查询
- ✅ 票数选择
- ✅ Stripe支付集成
- ✅ 免费票务处理
- ✅ 电子票生成
- ✅ 二维码显示

---

## 📦 订单管理

### 功能描述
用户查看购买历史、管理电子票、跟踪订单状态。

### 相关文件

#### 前端组件
- `src/screens/OrdersScreen.tsx` - 订单列表页面
- `src/screens/TicketScreen.tsx` - 电子票详情页面

#### 业务逻辑
- `src/services/orderService.ts` - 订单查询和管理
- `src/services/ticketService.ts` - 票务状态管理

#### 类型定义
- `src/types/database.ts` - 订单和票务相关类型

### 关键功能点
- ✅ 订单历史查看
- ✅ 分页加载订单
- ✅ 电子票二维码显示
- ✅ 订单状态跟踪
- ✅ 票务详情查看

---

## 👤 个人中心

### 功能描述
用户个人信息管理、设置、收藏管理等功能。

### 相关文件

#### 前端组件
- `src/screens/ProfileScreen.tsx` - 个人中心主页
- `src/screens/EditProfileScreen.tsx` - 编辑个人资料
- `src/screens/ChangePasswordScreen.tsx` - 修改密码
- `src/screens/FavoriteEventsScreen.tsx` - 收藏的活动
- `src/screens/NotificationSettingsScreen.tsx` - 通知设置

#### 子组件
- `src/components/SupportModal.tsx` - 客服支持弹窗
- `src/components/NotificationSettings.tsx` - 通知设置组件

#### 业务逻辑
- `src/services/favoriteService.ts` - 收藏功能服务
- `src/services/notificationService.ts` - 通知管理服务

### 关键功能点
- ✅ 个人资料展示和编辑
- ✅ 密码修改
- ✅ 收藏活动管理
- ✅ 通知偏好设置
- ✅ 客服支持
- ✅ 版本信息显示

---

## 🏢 主办方功能

### 功能描述
主办方可以创建活动、管理票务、查看数据统计等完整的活动管理功能。

### 相关文件

#### 前端组件
- `src/screens/OrganizerScreen.tsx` - 主办方主页
- `src/screens/organizer/OrganizerManagementScreen.tsx` - 主办方管理中心
- `src/screens/organizer/ManageEventsScreen.tsx` - 活动管理列表
- `src/screens/organizer/EventFormScreen.tsx` - 活动创建/编辑表单
- `src/screens/organizer/SessionFormScreen.tsx` - 场次创建/编辑表单
- `src/screens/organizer/SessionAttendeesScreen.tsx` - 场次参与者管理
- `src/screens/organizer/TicketValidationScreen.tsx` - 票务验证（扫码验票）
- `src/screens/organizer/RevenueReportScreen.tsx` - 收入报告

#### 业务逻辑
- `src/services/eventService.ts` - 活动创建和管理
- `src/services/orderService.ts` - 订单和参与者数据
- `src/services/ticketService.ts` - 票务验证

### 关键功能点
- ✅ 活动创建和编辑
- ✅ 场次管理
- ✅ 参与者列表查看
- ✅ 扫码验票功能
- ✅ 收入数据统计
- ✅ 数据导出功能

---

## 🔔 推送通知系统

### 功能描述
自动化推送通知系统，包括活动提醒、支付确认等多种通知类型。

### 相关文件

#### 前端服务
- `src/services/pushNotificationService.ts` - 推送通知客户端服务
- `src/services/notificationService.ts` - 通知管理服务

#### 后端服务
- `supabase/functions/send-push-notifications/` - 推送通知发送服务
- `supabase/functions/cron-event-reminders/` - 定时活动提醒任务

#### 类型定义
- `src/types/notification.ts` - 通知相关类型
- `src/types/pushNotification.ts` - 推送通知类型

### 关键功能点
- ✅ 设备注册和token管理
- ✅ 活动提醒（定时触发）
- ✅ 支付确认通知
- ✅ 票务验证通知
- ✅ 系统公告通知
- ✅ 用户偏好管理
- ✅ 通知历史记录

---

## 💳 支付系统

### 功能描述
集成Stripe支付系统，支持付费和免费票务的完整支付流程。

### 相关文件

#### 前端集成
- `App.tsx` - StripeProvider配置
- `src/screens/EventDetailsScreen.tsx` - 支付流程处理

#### 后端服务
- `supabase/functions/create-payment-intent/` - 创建Stripe支付意向
- `supabase/functions/stripe-webhook/` - 处理Stripe支付回调
- `supabase/functions/process-free-tickets/` - 处理免费票务

#### 业务逻辑
- `src/services/orderService.ts` - 订单支付状态管理
- `src/services/feeService.ts` - 费用计算（包含Stripe手续费）

### 关键功能点
- ✅ Stripe支付集成
- ✅ 多种支付方式支持
- ✅ 支付意向创建
- ✅ Webhook回调处理
- ✅ 免费票务处理
- ✅ 支付状态同步
- ✅ 手续费计算
