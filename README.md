# 🎫 Lezigo - 现代化活动售票平台

<div align="center">

![Lezigo Logo](./assets/lezigo-logo.jpg)

**一个基于 React Native + Supabase 构建的现代化活动售票平台**

[![React Native](https://img.shields.io/badge/React%20Native-0.76.9-blue.svg)](https://reactnative.dev/)
[![Expo](https://img.shields.io/badge/Expo-~52.0.47-black.svg)](https://expo.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-blue.svg)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-green.svg)](https://supabase.com/)
[![Stripe](https://img.shields.io/badge/Stripe-Payment-purple.svg)](https://stripe.com/)

</div>

## 📖 项目概述

Lezigo 是一个功能完整的移动端活动售票平台，为用户提供从活动发现到票务购买的完整体验。应用支持活动浏览、在线支付、电子票务、推送通知等核心功能，同时为活动主办方提供完整的活动管理和数据分析工具。

### ✨ 核心特性

- 🎯 **智能活动发现** - 精选推荐 + 个性化搜索
- 💳 **安全支付系统** - Stripe 集成，支持多种支付方式
- 🎫 **电子票务管理** - 二维码票务，支持扫码验票
- 📱 **实时推送通知** - 活动提醒、支付确认等智能通知
- 👥 **主办方工具** - 活动创建、参与者管理、收入统计
- 🔒 **企业级安全** - 端到端加密，安全存储用户数据

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm
- Expo CLI
- iOS Simulator / Android Emulator 或真机

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd LezigoRN
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 配置必要的环境变量
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **启动开发服务器**
```bash
npm start
# 或
yarn start
```

5. **运行应用**
```bash
# iOS
npm run ios

# Android
npm run android

# Web (开发调试)
npm run web
```

## 🎯 主要功能

### 👤 用户功能

#### 🏠 活动发现
- **精选活动展示** - 首页轮播展示特色活动
- **热门活动列表** - 实时更新的热门活动，支持无限滚动
- **智能搜索** - 按活动名称、描述搜索，支持实时搜索建议
- **标签分类** - 音乐、体育、艺术等多种分类标签
- **地理位置筛选** - 基于用户位置推荐附近活动(TODO)

#### 🎫 票务购买
- **活动详情查看** - 完整的活动信息、主办方介绍、场地信息
- **多场次选择** - 支持一个活动多个时间场次
- **实时库存显示** - 动态显示剩余票数
- **灵活票务选择** - 支持多张票同时购买
- **安全支付流程** - Stripe 集成，支持信用卡、Apple Pay、Google Pay等

#### 📱 个人中心
- **订单管理** - 查看购买历史、订单状态跟踪
- **电子票务** - 二维码电子票，支持离线显示
- **收藏功能** - 收藏感兴趣的活动，获取更新通知
- **个人资料** - 头像（TODO）、昵称、密码修改
- **通知设置** - 个性化推送通知偏好设置

### 🏢 主办方功能

#### 📊 活动管理
- **活动创建** - 图文并茂的活动创建向导
- **场次管理** - 灵活的时间、价格、容量设置
- **实时数据** - 销售数据、参与者统计实时更新
- **参与者管理** - 查看购票用户信息、参与者列表

#### 🎯 票务验证
- **扫码验票** - 相机扫描二维码验证票务真伪
- **入场管理** - 实时更新入场状态，防止重复入场
- **现场统计** - 实时查看入场人数、剩余容量（TODO）

#### 💰 收入分析（TODO）
- **收入报告** - 详细的销售数据分析和图表展示
- **手续费透明** - 清晰显示平台费用和净收入
- **导出功能** - 支持数据导出，便于财务管理

### 🔔 智能通知系统

#### 📲 自动化通知
- **活动提醒** - 活动开始前1小时自动提醒
- **活动更新** - 活动信息变更及时通知相关用户（TODO）
- **票务验证** - 扫码入场成功欢迎通知（TODO）

#### ⚙️ 个性化设置
- **通知偏好** - 用户可自定义接收哪些类型的通知
- **免打扰时间** - 设置勿扰时间段（TODO）
- **通知历史** - 查看所有通知记录（TODO）

## 🏗️ 技术架构

### 📱 前端技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| React Native | 0.76.9 | 跨平台移动应用框架 |
| Expo | ~52.0.47 | 开发工具链和运行时 |
| TypeScript | 5.3.3 | 类型安全的JavaScript |
| React Navigation | 7.x | 导航和路由管理 |
| React Native Reanimated | 3.18.0 | 高性能动画库 |
| Lottie React Native | 7.1.0 | 复杂动画支持 |
| React Native Fast Image | 8.6.3 | 高性能图片加载 |

### 🔧 后端服务

| 服务 | 用途 | 特性 |
|------|------|------|
| **Supabase** | 后端即服务 | PostgreSQL数据库、实时订阅、认证 |
| **Supabase Auth** | 用户认证 | JWT、PKCE流程、安全存储 |
| **Edge Functions** | 服务端逻辑 | Deno运行时、自动扩展 |
| **Stripe** | 支付处理 | 安全支付、多种支付方式 |
| **Expo Notifications** | 推送通知 | 跨平台推送、智能调度 |

### 🗂️ 项目结构

```
src/
├── components/          # 可复用UI组件
│   ├── Button.tsx      # 自定义按钮组件
│   ├── EventCard.tsx   # 活动卡片组件
│   ├── FeaturedEvent.tsx # 精选活动组件
│   └── ...
├── screens/            # 页面组件
│   ├── HomeScreen.tsx  # 首页
│   ├── EventDetailsScreen.tsx # 活动详情
│   ├── ProfileScreen.tsx # 个人中心
│   ├── organizer/      # 主办方功能页面
│   └── ...
├── services/           # 业务逻辑层
│   ├── eventService.ts # 活动相关API
│   ├── orderService.ts # 订单管理API
│   ├── pushNotificationService.ts # 推送通知
│   └── ...
├── context/            # 全局状态管理
│   └── AuthContext.tsx # 用户认证状态
├── navigation/         # 导航配置
│   └── AppNavigator.tsx # 主导航器
├── types/              # TypeScript类型定义
│   ├── database.ts     # 数据库类型
│   ├── navigation.ts   # 导航类型
│   └── ...
├── hooks/              # 自定义React Hooks
├── utils/              # 工具函数
├── config/             # 配置文件
│   └── theme.ts        # 设计系统配置
└── lib/                # 第三方库配置
    └── supabase.ts     # Supabase客户端
```

### 🔄 数据流架构

```
用户操作 → UI组件 → Service层 → Supabase API → 数据库
    ↓         ↓         ↓          ↓           ↓
状态更新 ← Context ← 响应处理 ← API响应 ← 数据返回
```

### 🛡️ 安全特性

- **端到端加密** - 敏感数据使用Expo SecureStore安全存储
- **Row Level Security** - 数据库级别的权限控制
- **JWT认证** - 无状态的安全认证机制
- **API密钥管理** - 环境变量安全管理
- **输入验证** - 前后端双重数据验证

## 🛠️ 开发指南

#### 构建和部署
```bash
# 创建开发构建
eas build --profile development

# 创建生产构建
eas build --profile production

# 发布更新
eas update --branch production
```

### 📁 项目结构说明

```
LezigoRN/
├── src/                    # 源代码目录
│   ├── components/         # 可复用组件
│   ├── screens/           # 页面组件
│   ├── services/          # 业务逻辑层
│   ├── context/           # 全局状态管理
│   ├── navigation/        # 导航配置
│   ├── types/             # TypeScript类型定义
│   ├── hooks/             # 自定义Hooks
│   ├── utils/             # 工具函数
│   ├── config/            # 配置文件
│   └── lib/               # 第三方库配置
├── assets/                # 静态资源
├── supabase/              # Supabase配置和函数
├── android/               # Android原生代码
├── ios/                   # iOS原生代码
├── App.tsx                # 应用入口
├── app.json               # Expo配置
├── package.json           # 项目依赖
└── tsconfig.json          # TypeScript配置
```

### 🎨 设计系统

应用使用统一的设计系统，定义在 `src/config/theme.ts`：

### 🔄 状态管理

应用使用React Context进行全局状态管理：

```typescript
// 使用认证上下文
const { user, signIn, signOut, loading } = useAuth();

// 本地状态管理
const [events, setEvents] = useState<Event[]>([]);
const [loading, setLoading] = useState(false);
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- EventCard.test.tsx

# 生成测试覆盖率报告
npm test -- --coverage
```

### 测试结构
```
__tests__/
├── components/
│   ├── EventCard.test.tsx
│   └── Button.test.tsx
├── services/
│   ├── eventService.test.ts
│   └── orderService.test.ts
└── utils/
    └── storage.test.ts
```

## 📱 平台特性

### iOS特性
- **Apple Pay集成** - 原生支付体验
- **iOS推送通知** - APNs集成

### Android特性
- **Google Pay集成** - 原生支付体验
- **Android推送通知** - FCM集成

## 📞 联系方式

- **开发者**: Realyujie
- **项目地址**: [GitHub Repository](https://github.com/username/LezigoRN)
- **问题反馈**: [Issues](https://github.com/username/LezigoRN/issues)

---

<div align="center">

**🎫 让每一次体验都值得纪念 - Lezigo**

Made with ❤️ by Squanchy

</div>

# Lezigo React Native 导航流程修改

## 主要修改

### 欢迎页面改进
- 将原来的 `AuthScreen` 重命名为 `WelcomeScreen`
- 修改 UI，将原来的登录和注册按钮合并为一个"Journey Into Experiences"按钮
- 用户点击该按钮后直接进入主页面浏览内容

### 导航逻辑调整
- 应用启动时显示 `WelcomeScreen`，不再强制要求登录
- 未登录用户可以浏览以下页面：
  - 主页 (HomeScreen)
  - 搜索页 (FindScreen)
  - 活动详情页 (EventDetailsScreen)
  - 标签列表页 (TagEventsScreen)

### 登录逻辑修改
- 未登录用户点击 ProfileScreen 标签时，会看到登录提示界面
- 未登录用户看不到活动卡片上的收藏按钮
- 未登录用户尝试购票时会被引导至登录页面 (SignInScreen)
- 所有需要登录的操作现在会直接引导至登录页面，而不是先显示 AuthScreen

## 导航流程

1. 应用启动 -> `WelcomeScreen`
2. 点击"Journey Into Experiences"按钮 -> `HomeScreen`
3. 未登录状态下浏览内容
4. 需要登录时 -> `SignInScreen`
5. 登录成功 -> 返回原来浏览的页面继续操作

## 文件修改

- `src/screens/AuthScreen.tsx` -> `src/screens/WelcomeScreen.tsx`
- `src/navigation/AppNavigator.tsx` (修改导航结构)
- `src/types/navigation.ts` (更新导航类型)
- 各个页面中的导航逻辑从 `navigation.navigate('Auth')` 改为 `navigation.navigate('SignIn')` 
