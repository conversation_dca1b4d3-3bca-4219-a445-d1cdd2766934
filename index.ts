// ✅ 添加 structuredClone polyfill - 必须在所有其他导入之前
import structuredClonePolyfill from '@ungap/structured-clone';

// 设置全局 structuredClone
if (typeof globalThis.structuredClone === 'undefined') {
  globalThis.structuredClone = structuredClonePolyfill as any;
}

import { registerRootComponent } from 'expo';

import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
