// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.
// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
// 验证用户身份并返回认证的客户端
async function authenticateUser(req) {
  const authHeader = req.headers.get('Authorization');
  if (!authHeader) {
    throw new Error('Missing authorization header');
  }
  const token = authHeader.replace('Bearer ', '');
  if (!token) {
    throw new Error('Invalid authorization token');
  }
  // 创建 Supabase 客户端（使用 anon key 进行用户验证）
  const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '', {
    global: {
      headers: {
        Authorization: authHeader
      }
    }
  });
  // 验证用户身份
  const { data: { user }, error } = await supabaseClient.auth.getUser(token);
  if (error || !user) {
    throw new Error('Invalid or expired token');
  }
  return {
    user,
    supabaseClient
  };
}
// 创建服务角色客户端（用于特权操作）
function createServiceClient() {
  return createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
}
console.log("✅ Process Free Tickets Function Started");
serve(async (req)=>{
  // 处理 CORS 预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    console.log('📝 Processing free tickets request');
    // 检查请求方法
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }
    // ✅ 验证用户身份
    const { user, supabaseClient } = await authenticateUser(req);
    console.log(`👤 Authenticated user: ${user.id}`);
    // 获取请求体
    const { order_id, quantity } = await req.json();
    // 验证必要参数
    if (!order_id || !quantity) {
      throw new Error('Missing required parameters: order_id and quantity are required');
    }
    if (typeof quantity !== 'number' || quantity <= 0) {
      throw new Error('Invalid quantity: must be a positive number');
    }
    console.log(`📋 Processing order: ${order_id}, quantity: ${quantity}`);
    // 创建服务角色客户端用于数据库操作
    const serviceClient = createServiceClient();
    // ✅ 验证订单所有权和状态
    const { data: order, error: orderError } = await serviceClient.from('orders').select('*').eq('order_id', order_id).eq('user_id', user.id) // 确保订单属于当前用户
    .single();
    if (orderError || !order) {
      console.error('❌ Order validation failed:', orderError);
      throw new Error('Order not found or access denied');
    }
    console.log(`🔍 Order found: ${order.order_id}, user: ${order.user_id}`);
    // 验证订单状态
    if (order.is_paid) {
      throw new Error('Order has already been processed');
    }
    if (order.total_amount !== 0) {
      throw new Error('Order is not free - payment required');
    }
    if (order.quantity !== quantity) {
      throw new Error(`Quantity mismatch: expected ${order.quantity}, got ${quantity}`);
    }
    // 检查订单是否过期（5分钟）
    const orderCreatedAt = new Date(order.created_at);
    const now = new Date();
    const timeDiff = now.getTime() - orderCreatedAt.getTime();
    const fiveMinutes = 5 * 60 * 1000;
    if (timeDiff > fiveMinutes) {
      console.log('⏰ Order expired, cleaning up...');
      throw new Error('Order has expired. Please create a new booking.');
    }
    // ✅ 使用原子性数据库函数处理免费票务
    console.log('🎫 Processing free tickets with atomic function...');
    const { data: result, error: processError } = await serviceClient.rpc('process_free_tickets_atomic', {
      p_order_id: order_id
    });
    if (processError) {
      console.error('❌ Failed to process free tickets:', processError);
      throw new Error(`Failed to process tickets: ${processError.message}`);
    }
    console.log('✅ Free tickets processed successfully:', result);
    // ✅ 成功响应
    console.log('🎉 Free tickets processing completed successfully');
    return new Response(JSON.stringify({
      success: true,
      message: 'Free tickets created successfully',
      data: {
        order_id: order_id,
        quantity: quantity,
        tickets_created: result?.tickets_created || quantity
      }
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('❌ Error processing free tickets:', error);
    // 确定适当的HTTP状态码
    let statusCode = 400;
    let errorMessage = 'Unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message;
      // 根据错误类型设置状态码
      if (errorMessage.includes('not found') || errorMessage.includes('access denied')) {
        statusCode = 404;
      } else if (errorMessage.includes('expired')) {
        statusCode = 410; // Gone
      } else if (errorMessage.includes('already been processed')) {
        statusCode = 409; // Conflict
      } else if (errorMessage.includes('authorization') || errorMessage.includes('token')) {
        statusCode = 401; // Unauthorized
      }
    }
    return new Response(JSON.stringify({
      success: false,
      error: {
        message: errorMessage,
        code: statusCode
      }
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: statusCode
    });
  }
}); /* ✅ To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/process-free-tickets' \
    --header 'Authorization: Bearer YOUR_USER_JWT_TOKEN' \
    --header 'Content-Type: application/json' \
    --data '{"order_id": "your-order-id", "quantity": 2}'

  Note: Replace YOUR_USER_JWT_TOKEN with a valid user JWT token
*/ 
