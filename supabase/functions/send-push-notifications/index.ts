import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const expoAccessToken = Deno.env.get('EXPO_ACCESS_TOKEN');
if (!supabaseUrl || !supabaseServiceKey || !expoAccessToken) {
  throw new Error('Missing required environment variables');
}
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});
/**
 * Send push notification via Expo Push API
 */ async function sendExpoPushNotification(payload) {
  try {
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${expoAccessToken}`
      },
      body: JSON.stringify(payload)
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Expo API error: ${response.status} - ${errorText}`);
    }
    const result = await response.json();
    console.log('Expo push notification sent:', result);
    return result;
  } catch (error) {
    console.error('Error sending push notification:', error);
    throw error;
  }
}
/**
 * Get notification template from database
 */ async function getNotificationTemplate(notificationType) {
  try {
    const { data: template, error } = await supabase.rpc('get_notification_template', {
      p_notification_type: notificationType
    });
    if (error) {
      console.error('Error getting notification template:', error);
      // Fallback to default template
      return {
        title: 'Notification',
        body: 'You have a new notification',
        sound: 'default',
        priority: 'default',
        category: 'general',
        channel_id: 'default'
      };
    }
    return template?.[0] || null;
  } catch (error) {
    console.error('Error fetching notification template:', error);
    return null;
  }
}
/**
 * Replace placeholders in notification content
 */ async function replaceNotificationPlaceholders(templateText, eventTitle, eventLocation, userName, organizerName, startTime) {
  try {
    const { data: result, error } = await supabase.rpc('replace_notification_placeholders', {
      template_text: templateText,
      event_title: eventTitle,
      event_location: eventLocation,
      user_name: userName,
      organizer_name: organizerName,
      start_time: startTime
    });
    if (error) {
      console.error('Error replacing placeholders:', error);
      return templateText;
    }
    return result || templateText;
  } catch (error) {
    console.error('Error in placeholder replacement:', error);
    return templateText;
  }
}
/**
 * Process event reminders
 */ async function processEventReminders(reminderMinutes = 60) {
  try {
    console.log(`Processing event reminders for ${reminderMinutes} minutes before start`);
    // Get eligible recipients
    const { data: recipients, error: queryError } = await supabase.rpc('get_event_reminder_recipients', {
      reminder_minutes_before: reminderMinutes
    });
    if (queryError) {
      console.error('Database query error:', queryError);
      throw new Error(`Failed to get event reminder recipients: ${queryError.message}`);
    }
    if (!recipients || recipients.length === 0) {
      console.log('No event reminders to send at this time');
      return {
        success: true,
        sent: 0,
        message: 'No reminders to send'
      };
    }
    console.log(`Found ${recipients.length} event reminder recipients`);
    const results = [];
    const errors = [];
    for (const recipient of recipients){
      try {
        // Get notification template from database
        const template = await getNotificationTemplate('event_reminder');
        if (!template) {
          throw new Error('Failed to get notification template');
        }
        // Replace placeholders in title and body
        const title = await replaceNotificationPlaceholders(template.title, recipient.event_title, recipient.event_location);
        const body = await replaceNotificationPlaceholders(template.body, recipient.event_title, recipient.event_location);
        // Create notification payload
        const payload = {
          to: recipient.expo_push_token,
          title,
          body,
          data: {
            type: 'event_reminder',
            sessionId: recipient.session_id,
            eventTitle: recipient.event_title,
            startTime: recipient.start_time,
            location: recipient.event_location
          },
          sound: template.sound || 'default',
          badge: 1,
          priority: template.priority || 'high',
          channelId: template.channel_id || 'event_reminder',
          _displayInForeground: true
        };
        // Record notification send attempt
        const { data: scheduledId, error: scheduleError } = await supabase.rpc('record_notification_send', {
          p_user_id: recipient.user_id,
          p_notification_type: 'event_reminder',
          p_reference_id: recipient.session_id,
          p_scheduled_for: recipient.start_time
        });
        if (scheduleError) {
          console.error('Failed to record notification:', scheduleError);
        }
        // Send push notification
        const pushResult = await sendExpoPushNotification(payload);
        // Update delivery status if successful
        if (pushResult && pushResult.data && scheduledId) {
          await supabase.rpc('update_notification_status', {
            p_scheduled_id: scheduledId,
            p_status: 'delivered',
            p_expo_receipt_id: pushResult.data.id
          });
        }
        results.push({
          user_id: recipient.user_id,
          session_id: recipient.session_id,
          expo_push_token: recipient.expo_push_token,
          status: 'sent',
          push_result: pushResult
        });
        console.log(`Event reminder sent to user ${recipient.user_id} for session ${recipient.session_id}`);
      } catch (error) {
        console.error(`Failed to send reminder to user ${recipient.user_id}:`, error);
        errors.push({
          user_id: recipient.user_id,
          session_id: recipient.session_id,
          error: error.message
        });
      // Record failure in database if we have a scheduled notification ID
      // Note: This would require knowing the scheduled_notification_id, 
      // which we might not have if the initial recording failed
      }
    }
    return {
      success: true,
      sent: results.length,
      failed: errors.length,
      results,
      errors
    };
  } catch (error) {
    console.error('Error processing event reminders:', error);
    throw error;
  }
}
/**
 * Send test notification
 */ async function sendTestNotification(userIds) {
  try {
    console.log(`Sending test notifications to ${userIds.length} users`);
    const results = [];
    const errors = [];
    for (const userId of userIds){
      try {
        // Get user's active device tokens
        const { data: deviceTokens, error: tokenError } = await supabase.from('user_device_tokens').select('expo_push_token, device_platform').eq('user_id', userId).eq('is_active', true);
        if (tokenError) {
          throw new Error(`Failed to get device tokens: ${tokenError.message}`);
        }
        if (!deviceTokens || deviceTokens.length === 0) {
          throw new Error('No active device tokens found');
        }
        for (const token of deviceTokens){
          // Get test notification template from database
          const template = await getNotificationTemplate('system_announcement');
          const title = template ? template.title : 'Test Notification 🧪';
          const body = template ? template.body : 'This is a test push notification from Lezigo!';
          const payload = {
            to: token.expo_push_token,
            title,
            body,
            data: {
              type: 'test',
              timestamp: new Date().toISOString()
            },
            sound: template?.sound || 'default',
            badge: 1,
            priority: template?.priority || 'default',
            channelId: template?.channel_id
          };
          const pushResult = await sendExpoPushNotification(payload);
          results.push({
            user_id: userId,
            expo_push_token: token.expo_push_token,
            platform: token.device_platform,
            status: 'sent',
            push_result: pushResult
          });
        }
      } catch (error) {
        console.error(`Failed to send test notification to user ${userId}:`, error);
        errors.push({
          user_id: userId,
          error: error.message
        });
      }
    }
    return {
      success: true,
      sent: results.length,
      failed: errors.length,
      results,
      errors
    };
  } catch (error) {
    console.error('Error sending test notifications:', error);
    throw error;
  }
}

/**
 * Main handler
 */ serve(async (req)=>{
  try {
    // Handle CORS
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      });
    }
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({
        error: 'Method not allowed'
      }), {
        status: 405,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    const requestBody = await req.json();
    const { type, userIds, customPayload, reminderMinutes } = requestBody;
    console.log(`Processing notification request: ${type}`);
    let result;
    switch(type){
      case 'event_reminder':
        result = await processEventReminders(reminderMinutes);
        break;
      case 'test':
        if (!userIds || userIds.length === 0) {
          throw new Error('User IDs required for test notifications');
        }
        result = await sendTestNotification(userIds);
        break;
      case 'custom':
        if (!customPayload) {
          throw new Error('Custom payload required for custom notifications');
        }
        const customResult = await sendExpoPushNotification(customPayload);
        result = {
          success: true,
          result: customResult
        };
        break;

      default:
        throw new Error(`Unsupported notification type: ${type}`);
    }
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('Function error:', error);
    return new Response(JSON.stringify({
      error: error.message,
      stack: error.stack
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
});
