import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required environment variables')
}

/**
 * Cron job to trigger event reminder notifications
 * This function should be called every 15 minutes via cron
 */
serve(async (req) => {
  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      })
    }

    console.log('Starting cron job for event reminders')

    // Call the send-push-notifications function
    const response = await fetch(`${supabaseUrl}/functions/v1/send-push-notifications`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
      },
      body: JSON.stringify({
        type: 'event_reminder',
        reminderMinutes: 60
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Failed to trigger notifications: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('Cron job completed:', result)

    return new Response(JSON.stringify({
      success: true,
      timestamp: new Date().toISOString(),
      result
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Cron job error:', error)
    
    return new Response(JSON.stringify({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}) 