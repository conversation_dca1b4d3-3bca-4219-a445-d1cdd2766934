import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import Stripe from 'https://esm.sh/stripe@14.21.0';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    const supabaseClient = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
      apiVersion: '2025-02-24.acacia'
    });
    const { order_id, amount, currency = 'aud', description } = await req.json();
    if (!order_id || !amount) {
      throw new Error('Missing required parameters: order_id and amount');
    }
    // 获取用户信息
    const authHeader = req.headers.get('Authorization');
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);
    if (authError || !user) {
      throw new Error('User not authenticated');
    }
    console.log(`🔍 Processing payment intent for order: ${order_id}`);
    // 查找订单
    const { data: order, error: orderError } = await supabaseClient.from('orders').select('*').eq('order_id', order_id).eq('user_id', user.id).single();
    if (orderError || !order) {
      console.log('❌ Order not found:', orderError);
      throw new Error('Order not found');
    }
    console.log('✅ Order found:', {
      order_id: order.order_id,
      status: order.status,
      total_amount: order.total_amount,
      expires_at: order.expires_at
    });
    // 验证订单状态
    if (order.status !== 'reserved') {
      throw new Error(`Invalid order status: ${order.status}. Expected: reserved`);
    }
    // 检查订单是否过期（5分钟）
    const orderCreatedAt = new Date(order.created_at);
    const now = new Date();
    const timeDiff = now.getTime() - orderCreatedAt.getTime();
    const fiveMinutes = 5 * 60 * 1000;
    if (timeDiff > fiveMinutes) {
      console.log('⏰ Order expired');
      throw new Error('Order has expired. Please create a new booking.');
    }
    // ✅ 检查剩余时间（但不设置PaymentIntent过期时间）
    const orderExpiresAt = new Date(order.expires_at);
    const remainingSeconds = Math.floor((orderExpiresAt.getTime() - now.getTime()) / 1000);
    // 确保至少有60秒剩余时间
    if (remainingSeconds < 60) {
      console.log('⏰ Order too close to expiration, remaining seconds:', remainingSeconds);
      throw new Error('Order is too close to expiration. Please create a new booking.');
    }
    console.log('🔄 Creating Stripe payment intent...');
    console.log(`⏰ Order expires at: ${orderExpiresAt.toISOString()}`);
    console.log(`⏰ Remaining time: ${remainingSeconds} seconds`);
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      description: description || `Payment for order ${order_id}`,
      metadata: {
        order_id: order_id,
        user_id: user.id,
        event_title: order.event_title || 'Event Ticket'
      },
      automatic_payment_methods: {
        enabled: true
      }
    });
    console.log(`✅ Payment intent created successfully: ${paymentIntent.id}`);
    // 更新订单的payment_intent_id
    const { error: updateError } = await supabaseClient.from('orders').update({
      payment_intent_id: paymentIntent.id,
      updated_at: new Date().toISOString()
    }).eq('order_id', order_id);
    if (updateError) {
      console.log('❌ Failed to update order with payment_intent_id:', updateError);
      throw new Error('Failed to update order with payment intent ID');
    }
    console.log('✅ Order updated with payment_intent_id');
    return new Response(JSON.stringify({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('❌ Error in create-payment-intent:', error);
    return new Response(JSON.stringify({
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 400
    });
  }
});
