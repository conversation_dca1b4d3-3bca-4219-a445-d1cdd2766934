import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CancelResult {
  payment_intent_id: string
  status: 'cancelled' | 'failed' | 'already_cancelled'
  error?: string
}

/**
 * Cancel expired PaymentIntents
 * This function is called by cron after database cleanup to cancel Stripe PaymentIntents
 */
serve(async (req) => {
  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    console.log('🚫 Starting PaymentIntent cancellation process')

    // Initialize Stripe client
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
      apiVersion: '2025-02-24.acacia'
    })

    // Create Supabase client with service role
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get PaymentIntent IDs to cancel from request body
    const { payment_intent_ids } = await req.json()
    
    if (!payment_intent_ids || !Array.isArray(payment_intent_ids)) {
      throw new Error('Invalid request: payment_intent_ids array required')
    }

    console.log(`📋 Processing ${payment_intent_ids.length} PaymentIntents for cancellation`)

    const results: CancelResult[] = []
    let successCount = 0
    let failureCount = 0

    // Process each PaymentIntent
    for (const paymentIntentId of payment_intent_ids) {
      try {
        console.log(`🔄 Attempting to cancel PaymentIntent: ${paymentIntentId}`)

        // First, check the current status of the PaymentIntent
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)
        
        if (paymentIntent.status === 'canceled') {
          console.log(`✅ PaymentIntent ${paymentIntentId} already cancelled`)
          results.push({
            payment_intent_id: paymentIntentId,
            status: 'already_cancelled'
          })
          successCount++
          continue
        }

        if (paymentIntent.status === 'succeeded') {
          console.log(`⚠️ PaymentIntent ${paymentIntentId} already succeeded, cannot cancel`)
          results.push({
            payment_intent_id: paymentIntentId,
            status: 'failed',
            error: 'Payment already succeeded'
          })
          failureCount++
          continue
        }

        // Cancel the PaymentIntent if it's in a cancellable state
        if (['requires_payment_method', 'requires_confirmation', 'requires_action'].includes(paymentIntent.status)) {
          const cancelledPaymentIntent = await stripe.paymentIntents.cancel(paymentIntentId)
          
          console.log(`✅ Successfully cancelled PaymentIntent: ${paymentIntentId}`)
          results.push({
            payment_intent_id: paymentIntentId,
            status: 'cancelled'
          })
          successCount++

          // Update the order record to mark PaymentIntent as cancelled
          await supabase
            .from('orders')
            .update({ 
              payment_intent_cancelled_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('payment_intent_id', paymentIntentId)

        } else {
          console.log(`⚠️ PaymentIntent ${paymentIntentId} in non-cancellable state: ${paymentIntent.status}`)
          results.push({
            payment_intent_id: paymentIntentId,
            status: 'failed',
            error: `Non-cancellable status: ${paymentIntent.status}`
          })
          failureCount++
        }

      } catch (error) {
        console.error(`❌ Failed to cancel PaymentIntent ${paymentIntentId}:`, error)
        results.push({
          payment_intent_id: paymentIntentId,
          status: 'failed',
          error: error.message
        })
        failureCount++
      }

      // Add small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log(`✅ PaymentIntent cancellation completed:`)
    console.log(`   - Total processed: ${payment_intent_ids.length}`)
    console.log(`   - Successful: ${successCount}`)
    console.log(`   - Failed: ${failureCount}`)

    return new Response(JSON.stringify({
      success: true,
      timestamp: new Date().toISOString(),
      summary: {
        total_processed: payment_intent_ids.length,
        successful: successCount,
        failed: failureCount
      },
      results
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('❌ PaymentIntent cancellation error:', error)
    
    return new Response(JSON.stringify({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/cancel-payment-intents' \
    --header 'Content-Type: application/json' \
    --data '{"payment_intent_ids": ["pi_1234567890", "pi_0987654321"]}'

  Note: This function should be called by the cron job after database cleanup
*/
