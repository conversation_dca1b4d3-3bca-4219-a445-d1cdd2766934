# 🎫 票务系统完整修复记录

## 📋 对话概述

本次对话从用户报告免费票购买错误开始，逐步发现并解决了整个票务系统中的多个关键问题，包括支付取消功能、倒计时机制、以及最终发现的付费票务处理缺陷。

## 🚨 初始问题：票务系统超售

### 核心问题：票务超售现象
**问题描述**: 票务系统出现超售情况，售出的票数超过了活动场次的实际容量

**具体表现**:
- 多个用户同时购买最后几张票时，系统允许超出容量的购买
- `sold_tickets` 数量超过 `capacity`，导致 `available` 计算为负数
- 用户可以购买到实际不存在的票

### 衍生问题1：免费票购买报错
**错误信息**: `(NOBRIDGE) ERROR Free ticket processing error: [Error: Invalid or expired token]`

**根本原因**:
- Edge Function `process-free-tickets` 设置了 `verify_jwt: true`
- 客户端使用了错误的 Authorization token（anon key 而不是用户 JWT token）

### 衍生问题2：支付取消后订单未被取消
**现象**: 用户关闭 Stripe 支付界面后，订单仍然保持 `reserved` 状态
**影响**: 票数被占用，其他用户无法购买，加剧了票务紧张情况

## 🔍 问题深度分析

### 阶段0：超售问题根本原因分析

#### 发现的核心问题
1. **缺乏原子性库存检查**
   - 原始的订单创建过程没有原子性的库存检查
   - 多个用户同时购买时，存在竞态条件
   - `sold_tickets` 和 `reserved_tickets` 更新不同步

2. **库存计算逻辑缺陷**
   ```sql
   -- 原始的非原子性检查
   available = capacity - sold_tickets - reserved_tickets
   ```
   - 在高并发情况下，多个事务可能同时读取到相同的库存数据
   - 导致超出容量的订单被创建

#### 超售解决方案
1. **创建原子性订单函数**
   ```sql
   CREATE OR REPLACE FUNCTION public.create_order_with_inventory_check(
     p_user_id uuid,
     p_session_id uuid,
     p_quantity integer,
     p_total_amount decimal
   )
   ```

2. **实现库存锁定机制**
   ```sql
   -- 锁定场次记录，防止并发修改
   SELECT * FROM event_sessions WHERE id = p_session_id FOR UPDATE;

   -- 原子性检查和更新库存
   IF (capacity - sold_tickets - reserved_tickets) < p_quantity THEN
     RAISE EXCEPTION 'INSUFFICIENT_INVENTORY';
   END IF;
   ```

3. **区分免费和付费票务的库存处理**
   - **免费票**: 直接更新 `sold_tickets`，不设置过期时间
   - **付费票**: 先更新 `reserved_tickets`，设置5分钟过期时间

### 阶段1：免费票务修复

#### 发现的问题
1. **Edge Function 调用不存在的数据库函数**
   - `process-free-tickets` 调用 `process_free_tickets_atomic`
   - 该函数不存在，导致免费票处理失败

2. **认证 Token 错误**
   - 客户端使用 `EXPO_PUBLIC_SUPABASE_ANON_KEY`
   - 应该使用用户的 JWT `access_token`

#### 修复方案
1. **创建缺失的数据库函数**
   ```sql
   CREATE OR REPLACE FUNCTION public.process_free_tickets_atomic(p_order_id uuid)
   RETURNS TABLE(tickets_created integer, order_status text)
   ```

2. **修复客户端认证**
   ```typescript
   // 错误的方式
   'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`
   
   // 正确的方式
   const { data: { session } } = await supabase.auth.getSession();
   'Authorization': `Bearer ${session.access_token}`
   ```

### 阶段2：支付取消功能实现

#### 用户需求变更
- 移除 "Reservation Expired" 弹窗
- 将倒计时从页面顶部移动到对应 Session
- 将倒计时显示在 Go 按钮上
- **关键需求**: 用户关闭 Stripe 支付时自动取消预留

#### 实现方案
1. **创建取消预留的 Edge Function**
   - 文件: `supabase/functions/cancel-reservation/index.ts`
   - 功能: 验证用户身份，调用数据库函数取消预留

2. **创建原子性数据库函数**
   ```sql
   CREATE OR REPLACE FUNCTION public.cancel_reservation_atomic(p_order_id uuid)
   RETURNS TABLE(released_tickets integer, order_status text)
   ```

3. **修改支付取消检测逻辑**
   ```typescript
   if (presentError.code === 'Canceled') {
     await cancelReservation(currentOrder.order_id);
   }
   ```

### 阶段3：架构简化

#### 发现问题
- Edge Function `cancel-reservation` 未部署到 Supabase
- 增加了不必要的复杂性

#### 解决方案
- 移除 Edge Function，直接调用数据库函数
- 简化调用链：客户端 → 数据库函数

### 阶段4：RLS 权限问题

#### 发现问题
- `cancel_reservation_atomic` 函数无法更新 orders 表
- RLS 策略阻止了 SECURITY DEFINER 函数的访问

#### 解决方案
```sql
-- 为服务角色添加完全访问权限
CREATE POLICY "Service role can manage all orders" ON orders
FOR ALL TO service_role USING (true) WITH CHECK (true);

-- 为 SECURITY DEFINER 函数添加访问权限  
CREATE POLICY "DEFINER functions can manage orders" ON orders
FOR ALL TO postgres USING (true) WITH CHECK (true);
```

### 阶段5：支付取消问题发现

#### 关键发现
通过日志分析发现：
```
💳 User cancelled payment, cancelling reservation immediately
🔍 Current order state: null
⚠️ No currentOrder found, cannot cancel reservation
Order reservation expired - cleaned up silently
```

**根本原因**:
- 用户在支付过程中，5分钟倒计时到期
- `handleOrderExpired` 被调用，清空了 `currentOrder`
- 用户取消支付时，`currentOrder` 已经是 `null`

#### 解决方案
在 `processPaidTickets` 函数开始时保存订单ID：
```typescript
const processPaidTickets = async (orderId: string, totalAmount: number) => {
  // ✅ 保存订单ID，防止currentOrder状态被清空
  const payingOrderId = orderId;

  // ... 支付流程 ...

  if (presentError.code === 'Canceled') {
    // ✅ 使用保存的订单ID而不是currentOrder状态
    if (payingOrderId) {
      await cancelReservation(payingOrderId);
    }
  }
}
```

## 📊 修改文件统计

### 数据库修改
1. **核心函数**: `create_order_with_inventory_check` - 解决超售的原子性订单创建
2. **支持函数**: `process_free_tickets_atomic` - 免费票务原子性处理
3. **取消函数**: `cancel_reservation_atomic` - 支付取消时的原子性预留释放
4. **清理函数**: `cleanup_expired_orders` - 自动清理过期订单
5. **🆕 付费处理函数**: `process_paid_tickets_atomic` - 付费票务原子性处理（修复系统缺陷）
6. **RLS 策略**: orders 表的服务角色和 DEFINER 权限
7. **RLS 策略**: ticket_reservations 表的完整权限策略

### 客户端修改
1. **src/screens/EventDetailsScreen.tsx**
   - 修复免费票务认证问题
   - 移除 Reservation Expired 弹窗
   - 将倒计时移动到 Go 按钮
   - 添加支付取消检测逻辑
   - 实现双重状态管理
   - 完善错误处理和状态清理
   - 🆕 删除倒计时功能，简化购买流程
   - 🆕 修复支付取消时的订单ID获取问题

2. **src/components/TicketConfirmationSheet.tsx**
   - 移除不必要的取消预留按钮
   - 简化组件接口

### 临时文件（已删除）
1. **supabase/functions/cancel-reservation/index.ts** - 后来简化架构时删除

## 🎯 最终效果

### 修复前的问题流程
```
多用户同时购买 → 非原子性库存检查 → 超售发生 ❌
用户购买 → 创建订单 → 设置 currentOrder → 开始倒计时
→ 进入支付 → 倒计时过期 → 清空 currentOrder
→ 用户取消支付 → currentOrder 为 null → 无法取消预留 ❌
→ 票数被永久占用 → 加剧超售问题 ❌

付费用户支付成功 → Stripe webhook 调用 → process_paid_tickets_atomic 不存在 ❌
→ 函数调用失败 → 订单状态未更新 → 票据未创建 ❌
→ 用户被扣款但没有票 → 严重的支付问题 ❌
```

### 修复后的正确流程
```
多用户同时购买 → 原子性库存检查 → 防止超售 ✅
用户购买 → 原子性创建订单 → 简化的购买流程（无倒计时）
→ 进入支付 → 用户取消支付 → 使用保存的订单ID → 成功取消预留 ✅
→ 票数立即释放 → 其他用户可购买 ✅

付费用户支付成功 → Stripe webhook 调用 → process_paid_tickets_atomic 正常执行 ✅
→ 创建票据 → 更新库存状态 → 订单标记为已完成 ✅
→ 用户收到票据 → 完整的支付流程 ✅
```

## ✅ 验证结果

### 支付取消修复后的日志：
```
💳 Payment sheet result: {"hasError": true, "errorCode": "Canceled", ...}
💳 User cancelled payment, cancelling reservation immediately
🔍 Paying order ID: "订单ID"
🔄 Calling cancelReservation for order: "订单ID"
🚫 Starting cancellation for order: "订单ID"
✅ Reservation cancelled successfully
```

### 付费票务修复后的效果：
```sql
-- 订单状态正确更新
orders: status = 'fulfilled', is_paid = true

-- 库存状态正确转换
event_sessions: reserved_tickets 减少, sold_tickets 增加

-- 票据正常创建
tickets: 创建对应数量的有效票据
```

## 🏆 技术亮点

1. **超售问题解决** - 通过原子性数据库函数彻底解决并发超售问题
2. **库存管理优化** - 区分免费和付费票务的不同库存处理策略
3. **问题诊断能力** - 通过日志分析找到支付取消的根本原因
4. **架构优化** - 从复杂的 Edge Function 简化为直接数据库调用
5. **状态管理** - 保存订单ID确保关键数据不丢失
6. **权限配置** - 正确配置 RLS 策略
7. **用户体验** - 简化购买流程，立即响应
8. **🆕 系统完整性修复** - 发现并修复付费票务处理的严重缺陷
9. **🆕 函数设计优化** - 避免列名冲突，确保函数正常执行

### 阶段6：倒计时功能移除

#### 用户需求变更
- 删除Go按钮的倒计时功能
- 简化购买流程：点击购买 → 直接支付

#### 实现方案
1. **移除CountdownButton组件**
2. **删除currentOrder状态管理**
3. **简化按钮显示逻辑**
4. **清理相关样式定义**

### 阶段7：付费票务处理缺陷发现

#### 严重问题发现
用户报告：预留订单后立即支付也没有生成票据

**Console Log显示**：
```
💳 Starting payment process for order: aefa6cd0-f9da-4e6e-939a-1544fbbec163
💳 Presenting payment sheet...
💳 Payment sheet result: {"hasError": false}
💳 Payment successful
```

但是：
- ❌ 订单状态仍为 `reserved`
- ❌ `is_paid` 仍为 `false`
- ❌ 没有创建票据
- ❌ 库存没有从 `reserved` 转为 `sold`

#### 根本原因分析
1. **函数不存在**：
   - `stripe-webhook` 调用 `process_paid_tickets_atomic`
   - 但数据库中该函数不存在
   - 导致所有付费支付的webhook处理失败

2. **函数语法错误**：
   ```sql
   -- 错误的函数定义导致列名冲突
   RETURNS TABLE(
     order_id UUID,  -- 与表列名冲突
     tickets_created INTEGER,
     status TEXT
   )
   ```

3. **系统状态**：
   - ✅ 免费票务：完整实现
   - ❌ 付费票务：只有一半实现（订单创建 ✅，票务处理 ❌）

#### 修复方案
1. **创建缺失的函数**：
   ```sql
   CREATE OR REPLACE FUNCTION process_paid_tickets_atomic(
     p_payment_intent_id TEXT
   )
   RETURNS TABLE(
     result_order_id UUID,  -- 重命名避免冲突
     tickets_created INTEGER,
     status TEXT
   )
   ```

2. **修复列名冲突**：
   ```sql
   -- 在UPDATE语句中明确指定表名
   WHERE orders.order_id = v_order.order_id
   WHERE ticket_reservations.order_id = v_order.order_id
   ```

3. **完整的票务处理逻辑**：
   - 验证订单状态和过期时间
   - 创建票据
   - 更新库存：`reserved_tickets` → `sold_tickets`
   - 更新订单状态：`reserved` → `fulfilled`
   - 标记为已支付：`is_paid = true`

## 📝 经验总结

1. **并发控制的重要性** - 高并发场景下必须使用原子性操作防止竞态条件
2. **库存管理策略** - 不同类型的票务需要不同的库存处理逻辑
3. **日志的重要性** - 详细的日志帮助快速定位问题
4. **状态管理复杂性** - 异步操作中的状态同步需要特别注意
5. **权限系统理解** - RLS 和 SECURITY DEFINER 的交互需要深入理解
6. **架构简化原则** - 能用简单方案就不用复杂方案
7. **边界情况处理** - 倒计时过期等边界情况往往是问题根源
8. **数据库设计** - 合理的数据库函数设计是解决复杂业务逻辑的关键
9. **系统完整性验证** - 必须验证整个支付流程的完整性，不能只测试部分功能
10. **函数命名和参数设计** - 避免与表列名冲突，使用明确的命名规范
